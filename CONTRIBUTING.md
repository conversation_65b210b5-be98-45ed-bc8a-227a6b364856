# Contributing Guide

## 🚀 Development Setup

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Git

### Getting Started

1. **Clone and install**

   ```bash
   git clone <repository-url>
   cd tucsenberg-web-stable
   npm install
   ```

2. **Set up environment**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start development**
   ```bash
   npm run dev
   ```

## 📝 Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **test**: Adding missing tests or correcting existing tests
- **chore**: Changes to the build process or auxiliary tools
- **perf**: A code change that improves performance
- **ci**: Changes to CI configuration files and scripts
- **build**: Changes that affect the build system or external dependencies
- **revert**: Reverts a previous commit

### Examples

```bash
feat(auth): add user login functionality
fix(ui): resolve button alignment issue
docs(readme): update installation instructions
style(header): improve responsive design
refactor(utils): simplify date formatting function
test(auth): add unit tests for login component
chore(deps): update dependencies to latest versions
```

## 🔍 Code Quality

### Pre-commit Hooks

- **ESLint**: Code linting and error detection
- **Prettier**: Code formatting
- **TypeScript**: Type checking
- **Lint-staged**: Only check staged files

### Manual Quality Checks

```bash
# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Formatting
npm run prettier
npm run prettier:fix

# All quality checks
npm run quality-check
```

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── layout/         # Layout components
│   └── features/       # Feature-specific components
├── lib/                # Utility functions and configurations
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
└── styles/             # Global styles and themes
```

## 🎯 Development Guidelines

### Component Development

- Use TypeScript for all components
- Follow React best practices
- Implement proper error boundaries
- Write comprehensive tests
- Document complex logic

### Styling

- Use Tailwind CSS for styling
- Follow mobile-first approach
- Ensure accessibility compliance
- Support light/dark themes

### Performance

- Optimize images and assets
- Implement proper code splitting
- Use React.memo for expensive components
- Monitor Core Web Vitals

## 🧪 Testing

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

### Writing Tests

- Write tests for all new features
- Maintain >85% code coverage
- Use React Testing Library for component tests
- Use Playwright for E2E tests

## 📋 Pull Request Process

1. **Create a feature branch**

   ```bash
   git checkout -b feat/your-feature-name
   ```

2. **Make your changes**

   - Follow coding standards
   - Add tests for new functionality
   - Update documentation if needed

3. **Commit your changes**

   ```bash
   git add .
   git commit -m "feat(scope): description"
   ```

4. **Push and create PR**

   ```bash
   git push origin feat/your-feature-name
   ```

5. **PR Requirements**
   - All tests must pass
   - Code coverage must not decrease
   - All quality checks must pass
   - Documentation must be updated

## 🐛 Bug Reports

When reporting bugs, please include:

- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details
- Screenshots if applicable

## 💡 Feature Requests

For new features:

- Describe the use case
- Explain the expected behavior
- Consider implementation complexity
- Discuss potential alternatives

## 📞 Getting Help

- Check existing documentation
- Search existing issues
- Create a new issue with detailed information
- Join our development discussions

---

Thank you for contributing to Tucsenberg Web Stable! 🎉
