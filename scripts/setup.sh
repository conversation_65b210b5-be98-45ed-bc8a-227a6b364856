#!/bin/bash

# Tucsenberg Web Stable - Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Tucsenberg Web Stable development environment..."

# Check Node.js version
echo "📋 Checking Node.js version..."
node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$node_version" -lt 18 ]; then
    echo "❌ Node.js 18+ is required. Current version: $(node -v)"
    exit 1
fi
echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Set up environment variables
if [ ! -f .env.local ]; then
    echo "🔧 Setting up environment variables..."
    cp .env.example .env.local
    echo "✅ Created .env.local from template"
    echo "📝 Please edit .env.local with your configuration"
else
    echo "✅ .env.local already exists"
fi

# Set up Git hooks
echo "🪝 Setting up Git hooks..."
npm run prepare

# Run quality checks
echo "🔍 Running quality checks..."
npm run type-check
npm run lint
npm run prettier

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Edit .env.local with your configuration"
echo "   2. Run 'npm run dev' to start development server"
echo "   3. Open http://localhost:3000 in your browser"
echo ""
echo "📚 Useful commands:"
echo "   npm run dev          - Start development server"
echo "   npm run build        - Build for production"
echo "   npm run lint         - Run ESLint"
echo "   npm run type-check   - Run TypeScript checks"
echo "   npm run quality-check - Run all quality checks"
echo ""
