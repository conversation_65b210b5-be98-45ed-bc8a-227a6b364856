#!/usr/bin/env node

/**
 * Build Analysis Script
 *
 * Analyzes Next.js build output and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

function analyzeBuildOutput() {
  console.log(colorize('\n🔍 Analyzing Next.js build output...', 'blue'));

  const buildDir = path.join(process.cwd(), '.next');

  if (!fs.existsSync(buildDir)) {
    console.log(
      colorize(
        '❌ Build directory not found. Run "npm run build" first.',
        'red'
      )
    );
    return false;
  }

  // Analyze static files
  const staticDir = path.join(buildDir, 'static');
  if (fs.existsSync(staticDir)) {
    analyzeStaticFiles(staticDir);
  }

  // Analyze server files
  const serverDir = path.join(buildDir, 'server');
  if (fs.existsSync(serverDir)) {
    analyzeServerFiles(serverDir);
  }

  // Check for build manifest
  const buildManifest = path.join(buildDir, 'build-manifest.json');
  if (fs.existsSync(buildManifest)) {
    analyzeBuildManifest(buildManifest);
  }

  return true;
}

function analyzeStaticFiles(staticDir) {
  console.log(colorize('\n📦 Static Files Analysis:', 'magenta'));

  const chunks = path.join(staticDir, 'chunks');
  if (fs.existsSync(chunks)) {
    const files = fs.readdirSync(chunks, { withFileTypes: true });
    let totalSize = 0;
    let jsFiles = 0;
    let cssFiles = 0;

    files.forEach(file => {
      if (file.isFile()) {
        const filePath = path.join(chunks, file.name);
        const stats = fs.statSync(filePath);
        totalSize += stats.size;

        if (file.name.endsWith('.js')) {
          jsFiles++;
          if (stats.size > 500 * 1024) {
            // > 500KB
            console.log(
              colorize(
                `  ⚠️  Large JS chunk: ${file.name} (${formatBytes(stats.size)})`,
                'yellow'
              )
            );
          }
        } else if (file.name.endsWith('.css')) {
          cssFiles++;
        }
      }
    });

    console.log(`  ✅ Total chunks: ${files.length} files`);
    console.log(`  📊 JS files: ${jsFiles}, CSS files: ${cssFiles}`);
    console.log(`  💾 Total size: ${formatBytes(totalSize)}`);

    // Recommendations
    if (totalSize > 5 * 1024 * 1024) {
      // > 5MB
      console.log(
        colorize('  💡 Consider code splitting to reduce bundle size', 'yellow')
      );
    }
  }
}

function analyzeServerFiles(serverDir) {
  console.log(colorize('\n🖥️  Server Files Analysis:', 'magenta'));

  const pagesDir = path.join(serverDir, 'pages');
  if (fs.existsSync(pagesDir)) {
    const files = getAllFiles(pagesDir);
    console.log(`  ✅ Server pages: ${files.length} files`);

    files.forEach(file => {
      const stats = fs.statSync(file);
      if (stats.size > 1024 * 1024) {
        // > 1MB
        const relativePath = path.relative(serverDir, file);
        console.log(
          colorize(
            `  ⚠️  Large server file: ${relativePath} (${formatBytes(stats.size)})`,
            'yellow'
          )
        );
      }
    });
  }
}

function analyzeBuildManifest(manifestPath) {
  console.log(colorize('\n📋 Build Manifest Analysis:', 'magenta'));

  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

    // Analyze pages
    const pages = Object.keys(manifest.pages || {});
    console.log(`  ✅ Total pages: ${pages.length}`);

    // Analyze shared chunks
    const sharedFiles =
      manifest.pages?.['/']?.filter(
        file => file.includes('chunks/') && !file.includes('pages/')
      ) || [];
    console.log(`  📦 Shared chunks: ${sharedFiles.length}`);

    // Check for large pages
    Object.entries(manifest.pages || {}).forEach(([page, files]) => {
      const pageFiles = files.filter(file => file.includes('pages/'));
      if (pageFiles.length > 5) {
        console.log(
          colorize(
            `  ⚠️  Page with many chunks: ${page} (${pageFiles.length} files)`,
            'yellow'
          )
        );
      }
    });
  } catch (error) {
    console.log(
      colorize(`  ❌ Error reading build manifest: ${error.message}`, 'red')
    );
  }
}

function getAllFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir, { withFileTypes: true });

    items.forEach(item => {
      const fullPath = path.join(currentDir, item.name);

      if (item.isDirectory()) {
        traverse(fullPath);
      } else {
        files.push(fullPath);
      }
    });
  }

  traverse(dir);
  return files;
}

function checkOptimizations() {
  console.log(colorize('\n🚀 Optimization Recommendations:', 'blue'));

  const recommendations = [];

  // Check next.config.ts
  const nextConfig = path.join(process.cwd(), 'next.config.ts');
  if (fs.existsSync(nextConfig)) {
    const config = fs.readFileSync(nextConfig, 'utf8');

    if (!config.includes('swcMinify')) {
      recommendations.push('Enable SWC minification in next.config.ts');
    }

    if (!config.includes('compress: true')) {
      recommendations.push('Enable compression in next.config.ts');
    }

    if (!config.includes('optimizePackageImports')) {
      recommendations.push(
        'Configure optimizePackageImports for better tree shaking'
      );
    }
  }

  // Check for bundle analyzer
  const packageJson = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packageJson)) {
    const pkg = JSON.parse(fs.readFileSync(packageJson, 'utf8'));

    if (!pkg.devDependencies?.['@next/bundle-analyzer']) {
      recommendations.push(
        'Install @next/bundle-analyzer for detailed bundle analysis'
      );
    }
  }

  // Display recommendations
  if (recommendations.length > 0) {
    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  } else {
    console.log(colorize('  ✅ All optimizations are configured!', 'green'));
  }
}

function generateReport() {
  console.log(colorize('\n📊 Performance Report:', 'bold'));
  console.log(colorize('='.repeat(50), 'blue'));

  const buildSuccess = analyzeBuildOutput();

  if (buildSuccess) {
    checkOptimizations();

    console.log(colorize('\n💡 Next Steps:', 'cyan'));
    console.log('  1. Run "npm run build" to see detailed build output');
    console.log('  2. Use "npm run start" to test production build');
    console.log('  3. Consider using Lighthouse for performance auditing');
    console.log('  4. Monitor Core Web Vitals in production');
  }

  console.log('');
}

// Run the analysis
generateReport();
