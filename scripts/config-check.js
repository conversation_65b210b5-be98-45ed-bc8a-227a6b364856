#!/usr/bin/env node

/**
 * Configuration Check Script
 *
 * Validates environment configuration and provides helpful feedback
 * for development and deployment.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function checkEnvironmentFiles() {
  console.log(colorize('\n🔍 Checking environment files...', 'blue'));

  const envFiles = [
    {
      file: '.env.example',
      required: true,
      description: 'Environment template',
    },
    {
      file: '.env.local',
      required: false,
      description: 'Local development config',
    },
    {
      file: '.env.production.example',
      required: true,
      description: 'Production template',
    },
    { file: '.env.test.example', required: true, description: 'Test template' },
  ];

  let allGood = true;

  envFiles.forEach(({ file, required, description }) => {
    const exists = fs.existsSync(path.join(process.cwd(), file));

    if (exists) {
      console.log(`  ✅ ${file} - ${description}`);
    } else if (required) {
      console.log(`  ❌ ${file} - ${description} (MISSING)`);
      allGood = false;
    } else {
      console.log(`  ⚠️  ${file} - ${description} (optional, not found)`);
    }
  });

  return allGood;
}

function checkRequiredDirectories() {
  console.log(colorize('\n📁 Checking required directories...', 'blue'));

  const directories = [
    { dir: 'src/lib', description: 'Configuration library' },
    { dir: 'scripts', description: 'Utility scripts' },
  ];

  let allGood = true;

  directories.forEach(({ dir, description }) => {
    const exists = fs.existsSync(path.join(process.cwd(), dir));

    if (exists) {
      console.log(`  ✅ ${dir}/ - ${description}`);
    } else {
      console.log(`  ❌ ${dir}/ - ${description} (MISSING)`);
      allGood = false;
    }
  });

  return allGood;
}

function checkConfigurationFiles() {
  console.log(colorize('\n⚙️  Checking configuration files...', 'blue'));

  const configFiles = [
    { file: 'src/lib/env.ts', description: 'Environment type definitions' },
    { file: 'src/lib/config.ts', description: 'Configuration management' },
    {
      file: 'src/lib/config-validator.ts',
      description: 'Configuration validator',
    },
  ];

  let allGood = true;

  configFiles.forEach(({ file, description }) => {
    const exists = fs.existsSync(path.join(process.cwd(), file));

    if (exists) {
      console.log(`  ✅ ${file} - ${description}`);
    } else {
      console.log(`  ❌ ${file} - ${description} (MISSING)`);
      allGood = false;
    }
  });

  return allGood;
}

function checkPackageDependencies() {
  console.log(colorize('\n📦 Checking package dependencies...', 'blue'));

  try {
    const packageJson = JSON.parse(
      fs.readFileSync(path.join(process.cwd(), 'package.json'), 'utf8')
    );

    const requiredDeps = ['zod'];
    let allGood = true;

    requiredDeps.forEach(dep => {
      const hasInDeps =
        packageJson.dependencies && packageJson.dependencies[dep];
      const hasInDevDeps =
        packageJson.devDependencies && packageJson.devDependencies[dep];

      if (hasInDeps || hasInDevDeps) {
        const version = hasInDeps
          ? packageJson.dependencies[dep]
          : packageJson.devDependencies[dep];
        console.log(`  ✅ ${dep}@${version}`);
      } else {
        console.log(`  ❌ ${dep} (MISSING)`);
        allGood = false;
      }
    });

    return allGood;
  } catch (error) {
    console.log(`  ❌ Error reading package.json: ${error.message}`);
    return false;
  }
}

function provideSetupInstructions() {
  console.log(colorize('\n📋 Setup Instructions:', 'magenta'));
  console.log('');
  console.log('1. Copy environment template:');
  console.log(colorize('   cp .env.example .env.local', 'cyan'));
  console.log('');
  console.log('2. Edit .env.local with your configuration:');
  console.log(
    colorize('   # Update NEXT_PUBLIC_APP_URL, database settings, etc.', 'cyan')
  );
  console.log('');
  console.log('3. Install dependencies (if missing):');
  console.log(colorize('   npm install', 'cyan'));
  console.log('');
  console.log('4. Validate configuration:');
  console.log(colorize('   npm run dev', 'cyan'));
  console.log('');
}

function main() {
  console.log(
    colorize('🚀 Tucsenberg Web Stable - Configuration Check', 'bold')
  );
  console.log(colorize('='.repeat(50), 'blue'));

  const checks = [
    checkEnvironmentFiles(),
    checkRequiredDirectories(),
    checkConfigurationFiles(),
    checkPackageDependencies(),
  ];

  const allPassed = checks.every(check => check);

  console.log(colorize('\n📊 Summary:', 'bold'));

  if (allPassed) {
    console.log(colorize('✅ All configuration checks passed!', 'green'));
    console.log(
      colorize('🎉 Your environment is ready for development.', 'green')
    );
  } else {
    console.log(colorize('❌ Some configuration checks failed.', 'red'));
    console.log(
      colorize('🔧 Please fix the issues above before proceeding.', 'yellow')
    );
    provideSetupInstructions();
  }

  console.log('');
  process.exit(allPassed ? 0 : 1);
}

// Run the check
main();
