#!/usr/bin/env node

/**
 * Performance Check Script for CI/CD Pipeline
 *
 * This script runs comprehensive performance checks and validates against
 * the performance budget. It's designed to be used in CI/CD pipelines
 * to ensure performance regressions are caught early.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Load performance budget
const performanceBudget = require('../performance-budget.json');

/**
 * Colors for console output
 */
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

/**
 * Log with colors
 */
function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Run command and return output
 */
function runCommand(command, options = {}) {
  try {
    return execSync(command, {
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    if (!options.allowFailure) {
      log(`Command failed: ${command}`, 'red');
      log(error.message, 'red');
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if required dependencies are installed
 */
function checkDependencies() {
  log('🔍 Checking dependencies...', 'blue');

  const requiredCommands = ['npm', 'node'];
  for (const cmd of requiredCommands) {
    const result = runCommand(`which ${cmd}`, {
      silent: true,
      allowFailure: true,
    });
    if (!result) {
      log(`❌ Required command not found: ${cmd}`, 'red');
      process.exit(1);
    }
  }

  log('✅ All dependencies are available', 'green');
}

/**
 * Build the application
 */
function buildApplication() {
  log('🏗️  Building application...', 'blue');
  runCommand('npm run build');
  log('✅ Application built successfully', 'green');
}

/**
 * Run Lighthouse CI tests
 */
function runLighthouseTests() {
  log('🚀 Running Lighthouse performance tests...', 'blue');

  try {
    runCommand('npm run lighthouse:collect');
    log('✅ Lighthouse tests completed successfully', 'green');
    return true;
  } catch (error) {
    log('❌ Lighthouse tests failed', 'red');
    return false;
  }
}

/**
 * Analyze Lighthouse results
 */
function analyzeLighthouseResults() {
  log('📊 Analyzing Lighthouse results...', 'blue');

  const lighthouseDir = './.lighthouseci';
  if (!fs.existsSync(lighthouseDir)) {
    log('❌ Lighthouse results directory not found', 'red');
    return false;
  }

  // Get the latest result file
  const files = fs
    .readdirSync(lighthouseDir)
    .filter(file => file.endsWith('.json'))
    .sort()
    .reverse();

  if (files.length === 0) {
    log('❌ No Lighthouse result files found', 'red');
    return false;
  }

  const latestFile = path.join(lighthouseDir, files[0]);
  const results = JSON.parse(fs.readFileSync(latestFile, 'utf8'));

  // Extract key metrics
  const metrics = {
    performance: results.categories.performance.score * 100,
    accessibility: results.categories.accessibility.score * 100,
    bestPractices: results.categories['best-practices'].score * 100,
    seo: results.categories.seo.score * 100,
    fcp: results.audits['first-contentful-paint'].numericValue,
    lcp: results.audits['largest-contentful-paint'].numericValue,
    tti: results.audits.interactive.numericValue,
    tbt: results.audits['total-blocking-time'].numericValue,
    cls: results.audits['cumulative-layout-shift'].numericValue,
    si: results.audits['speed-index'].numericValue,
  };

  // Check against budget
  const budget = performanceBudget.lighthouse;
  const timingBudget = performanceBudget.budgets[0].timings;

  let passed = true;
  const violations = [];

  // Check category scores
  if (metrics.performance < budget.performance.budget) {
    violations.push(
      `Performance: ${metrics.performance}% < ${budget.performance.budget}%`
    );
    passed = false;
  }

  if (metrics.accessibility < budget.accessibility.budget) {
    violations.push(
      `Accessibility: ${metrics.accessibility}% < ${budget.accessibility.budget}%`
    );
    passed = false;
  }

  if (metrics.bestPractices < budget['best-practices'].budget) {
    violations.push(
      `Best Practices: ${metrics.bestPractices}% < ${budget['best-practices'].budget}%`
    );
    passed = false;
  }

  if (metrics.seo < budget.seo.budget) {
    violations.push(`SEO: ${metrics.seo}% < ${budget.seo.budget}%`);
    passed = false;
  }

  // Check timing metrics
  const timingChecks = [
    {
      metric: 'fcp',
      name: 'First Contentful Paint',
      value: metrics.fcp,
      budget: timingBudget[0].budget,
    },
    {
      metric: 'lcp',
      name: 'Largest Contentful Paint',
      value: metrics.lcp,
      budget: timingBudget[1].budget,
    },
    {
      metric: 'tti',
      name: 'Time to Interactive',
      value: metrics.tti,
      budget: timingBudget[2].budget,
    },
    {
      metric: 'tbt',
      name: 'Total Blocking Time',
      value: metrics.tbt,
      budget: timingBudget[3].budget,
    },
    {
      metric: 'cls',
      name: 'Cumulative Layout Shift',
      value: metrics.cls,
      budget: timingBudget[4].budget,
    },
    {
      metric: 'si',
      name: 'Speed Index',
      value: metrics.si,
      budget: timingBudget[5].budget,
    },
  ];

  for (const check of timingChecks) {
    if (check.value > check.budget) {
      violations.push(
        `${check.name}: ${check.value.toFixed(0)}ms > ${check.budget}ms`
      );
      passed = false;
    }
  }

  // Report results
  log('\n📈 Performance Results:', 'cyan');
  log(
    `Performance Score: ${metrics.performance.toFixed(1)}%`,
    metrics.performance >= budget.performance.budget ? 'green' : 'red'
  );
  log(
    `Accessibility Score: ${metrics.accessibility.toFixed(1)}%`,
    metrics.accessibility >= budget.accessibility.budget ? 'green' : 'red'
  );
  log(
    `Best Practices Score: ${metrics.bestPractices.toFixed(1)}%`,
    metrics.bestPractices >= budget['best-practices'].budget ? 'green' : 'red'
  );
  log(
    `SEO Score: ${metrics.seo.toFixed(1)}%`,
    metrics.seo >= budget.seo.budget ? 'green' : 'red'
  );

  log('\n⏱️  Core Web Vitals:', 'cyan');
  for (const check of timingChecks) {
    const status = check.value <= check.budget ? 'green' : 'red';
    const unit = check.metric === 'cls' ? '' : 'ms';
    log(
      `${check.name}: ${check.value.toFixed(check.metric === 'cls' ? 3 : 0)}${unit}`,
      status
    );
  }

  if (violations.length > 0) {
    log('\n❌ Performance Budget Violations:', 'red');
    violations.forEach(violation => log(`  • ${violation}`, 'red'));
  }

  return passed;
}

/**
 * Generate performance report
 */
function generateReport(passed) {
  const timestamp = new Date().toISOString();
  const report = {
    timestamp,
    passed,
    budget: performanceBudget,
    environment: process.env.NODE_ENV || 'development',
    commit: process.env.GITHUB_SHA || 'unknown',
    branch: process.env.GITHUB_REF_NAME || 'unknown',
  };

  const reportPath = './performance-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  log(`\n📄 Performance report saved to: ${reportPath}`, 'blue');
}

/**
 * Main execution
 */
function main() {
  log('🚀 Starting Performance Check Pipeline', 'magenta');
  log('=====================================\n', 'magenta');

  const startTime = Date.now();

  try {
    // Step 1: Check dependencies
    checkDependencies();

    // Step 2: Build application
    buildApplication();

    // Step 3: Run Lighthouse tests
    const lighthouseSuccess = runLighthouseTests();
    if (!lighthouseSuccess) {
      log('\n❌ Performance check failed at Lighthouse testing stage', 'red');
      process.exit(1);
    }

    // Step 4: Analyze results
    const budgetPassed = analyzeLighthouseResults();

    // Step 5: Generate report
    generateReport(budgetPassed);

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);

    if (budgetPassed) {
      log(
        `\n✅ Performance check completed successfully in ${duration}s`,
        'green'
      );
      log('All performance budgets are within acceptable limits!', 'green');
    } else {
      log(`\n❌ Performance check failed in ${duration}s`, 'red');
      log('Performance budget violations detected!', 'red');
      process.exit(1);
    }
  } catch (error) {
    log(`\n💥 Performance check crashed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, analyzeLighthouseResults, generateReport };
