#!/bin/bash

# Set proper permissions for scripts and Git hooks

echo "🔧 Setting permissions for scripts and Git hooks..."

# Make scripts executable
chmod +x scripts/setup.sh
chmod +x scripts/test-hooks.sh
chmod +x scripts/set-permissions.sh

# Make Git hooks executable
chmod +x .husky/pre-commit
chmod +x .husky/commit-msg
chmod +x .husky/_/husky.sh

echo "✅ Permissions set successfully!"
echo ""
echo "📋 Executable files:"
echo "   ✅ scripts/setup.sh"
echo "   ✅ scripts/test-hooks.sh"
echo "   ✅ .husky/pre-commit"
echo "   ✅ .husky/commit-msg"
echo "   ✅ .husky/_/husky.sh"
