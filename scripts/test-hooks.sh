#!/bin/bash

# Test Git hooks functionality
# This script tests the pre-commit and commit-msg hooks

set -e

echo "🧪 Testing Git hooks functionality..."

# Test pre-commit hook
echo "📋 Testing pre-commit hook..."
if [ -f .husky/pre-commit ]; then
    echo "✅ pre-commit hook exists"
    
    # Test if the hook is executable
    if [ -x .husky/pre-commit ]; then
        echo "✅ pre-commit hook is executable"
    else
        echo "⚠️  Making pre-commit hook executable..."
        chmod +x .husky/pre-commit
    fi
else
    echo "❌ pre-commit hook not found"
    exit 1
fi

# Test commit-msg hook
echo "📋 Testing commit-msg hook..."
if [ -f .husky/commit-msg ]; then
    echo "✅ commit-msg hook exists"
    
    # Test if the hook is executable
    if [ -x .husky/commit-msg ]; then
        echo "✅ commit-msg hook is executable"
    else
        echo "⚠️  Making commit-msg hook executable..."
        chmod +x .husky/commit-msg
    fi
else
    echo "❌ commit-msg hook not found"
    exit 1
fi

# Test lint-staged configuration
echo "📋 Testing lint-staged configuration..."
if grep -q "lint-staged" package.json; then
    echo "✅ lint-staged configuration found in package.json"
else
    echo "❌ lint-staged configuration not found"
    exit 1
fi

# Test commit message validation
echo "📋 Testing commit message validation..."
temp_file=$(mktemp)

# Test valid commit messages
valid_messages=(
    "feat: add new feature"
    "fix(auth): resolve login issue"
    "docs: update README"
    "style(ui): improve button design"
    "refactor: simplify utility functions"
    "test: add unit tests"
    "chore: update dependencies"
)

for msg in "${valid_messages[@]}"; do
    echo "$msg" > "$temp_file"
    if .husky/commit-msg "$temp_file" >/dev/null 2>&1; then
        echo "✅ Valid: '$msg'"
    else
        echo "❌ Should be valid: '$msg'"
        exit 1
    fi
done

# Test invalid commit messages
invalid_messages=(
    "invalid message"
    "fix bug"
    "update"
    "WIP"
)

for msg in "${invalid_messages[@]}"; do
    echo "$msg" > "$temp_file"
    if .husky/commit-msg "$temp_file" >/dev/null 2>&1; then
        echo "❌ Should be invalid: '$msg'"
        exit 1
    else
        echo "✅ Invalid (correctly rejected): '$msg'"
    fi
done

# Clean up
rm "$temp_file"

echo ""
echo "🎉 All Git hooks tests passed!"
echo ""
echo "📋 Git hooks are properly configured:"
echo "   ✅ pre-commit: Runs lint-staged on staged files"
echo "   ✅ commit-msg: Validates commit message format"
echo ""
echo "💡 Try making a commit to test the hooks in action!"
