# Production Environment Variables Template
# Use this template for production deployment

# =============================================================================
# CORE CONFIGURATION (REQUIRED)
# =============================================================================

NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME="Tucsenberg Web Stable"

# =============================================================================
# DEVELOPMENT CONFIGURATION (DISABLED IN PRODUCTION)
# =============================================================================

NEXT_PUBLIC_SHOW_DEBUG_INFO=false
NEXT_PUBLIC_ENABLE_DEVTOOLS=false

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED)
# =============================================================================

DATABASE_URL="************************************************/tucsenberg_prod"
DATABASE_DIRECT_URL="************************************************/tucsenberg_prod"

# =============================================================================
# AUTHENTICATION CONFIGURATION (REQUIRED)
# =============================================================================

NEXTAUTH_SECRET="your-production-secret-key-32-characters-long"
NEXTAUTH_URL="https://your-domain.com"
JWT_SECRET="your-production-jwt-secret-32-characters"

# =============================================================================
# EMAIL SERVICE CONFIGURATION (RECOMMENDED)
# =============================================================================

SMTP_HOST="smtp.your-provider.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-production-email-password"
SMTP_FROM="<EMAIL>"

# =============================================================================
# ANALYTICS CONFIGURATION (RECOMMENDED)
# =============================================================================

NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_VERCEL_ANALYTICS_ID="your-vercel-analytics-id"
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# =============================================================================
# API KEYS AND INTEGRATIONS (AS NEEDED)
# =============================================================================

OPENAI_API_KEY="sk-your-production-openai-key"
STRIPE_SECRET_KEY="sk_live_your-production-stripe-key"
STRIPE_PUBLISHABLE_KEY="pk_live_your-production-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-production-webhook-secret"

# =============================================================================
# SECURITY CONFIGURATION (REQUIRED)
# =============================================================================

ENCRYPTION_KEY="your-32-character-production-encryption-key"
