# Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Node.js Environment (development, test, production)
NODE_ENV=development

# Next.js Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Tucsenberg Web Stable"

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Tools
NEXT_PUBLIC_SHOW_DEBUG_INFO=true
NEXT_PUBLIC_ENABLE_DEVTOOLS=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database (Required for production)
# DATABASE_URL="postgresql://username:password@localhost:5432/tucsenberg_dev"
# DATABASE_DIRECT_URL="postgresql://username:password@localhost:5432/tucsenberg_dev"

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# NextAuth.js Configuration (Required for production)
# NEXTAUTH_SECRET="your-secret-key-here"
# NEXTAUTH_URL="http://localhost:3000"

# JWT Secret for custom authentication
# JWT_SECRET="your-jwt-secret-here"

# =============================================================================
# EMAIL SERVICE CONFIGURATION
# =============================================================================

# SMTP Configuration for email sending
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASSWORD="your-app-password"
# SMTP_FROM="<EMAIL>"

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=false

# Analytics Services
# NEXT_PUBLIC_VERCEL_ANALYTICS_ID="your-vercel-analytics-id"
# NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# =============================================================================
# API KEYS AND INTEGRATIONS
# =============================================================================

# OpenAI API (for AI features)
# OPENAI_API_KEY="sk-..."

# Stripe Payment Processing
# STRIPE_SECRET_KEY="sk_test_..."
# STRIPE_PUBLISHABLE_KEY="pk_test_..."
# STRIPE_WEBHOOK_SECRET="whsec_..."

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Encryption Key for sensitive data
# ENCRYPTION_KEY="your-32-character-encryption-key"
