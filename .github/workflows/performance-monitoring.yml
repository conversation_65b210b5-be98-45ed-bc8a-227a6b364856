name: Performance Monitoring

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  performance-check:
    name: Performance Budget Check
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup Chrome for Lighthouse
        uses: browser-actions/setup-chrome@latest
        with:
          chrome-version: stable

      - name: Run performance check
        run: npm run performance:check
        env:
          NODE_ENV: production
          GITHUB_SHA: ${{ github.sha }}
          GITHUB_REF_NAME: ${{ github.ref_name }}

      - name: Upload Lighthouse reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-reports-node-${{ matrix.node-version }}
          path: .lighthouseci/
          retention-days: 30

      - name: Upload performance report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-report-node-${{ matrix.node-version }}
          path: performance-report.json
          retention-days: 30

      - name: Comment PR with performance results
        if: github.event_name == 'pull_request' && matrix.node-version == '20.x'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');

            try {
              const report = JSON.parse(fs.readFileSync('performance-report.json', 'utf8'));
              const lighthouseFiles = fs.readdirSync('.lighthouseci/')
                .filter(file => file.endsWith('.json'))
                .sort()
                .reverse();
              
              if (lighthouseFiles.length === 0) {
                console.log('No Lighthouse results found');
                return;
              }
              
              const latestFile = `.lighthouseci/${lighthouseFiles[0]}`;
              const results = JSON.parse(fs.readFileSync(latestFile, 'utf8'));
              
              const metrics = {
                performance: Math.round(results.categories.performance.score * 100),
                accessibility: Math.round(results.categories.accessibility.score * 100),
                bestPractices: Math.round(results.categories['best-practices'].score * 100),
                seo: Math.round(results.categories.seo.score * 100),
                fcp: Math.round(results.audits['first-contentful-paint'].numericValue),
                lcp: Math.round(results.audits['largest-contentful-paint'].numericValue),
                tti: Math.round(results.audits.interactive.numericValue),
                tbt: Math.round(results.audits['total-blocking-time'].numericValue),
                cls: results.audits['cumulative-layout-shift'].numericValue.toFixed(3),
              };
              
              const status = report.passed ? '✅ PASSED' : '❌ FAILED';
              const statusEmoji = report.passed ? '🎉' : '⚠️';
              
              const comment = `## ${statusEmoji} Performance Check Results ${status}
              
              ### Lighthouse Scores
              | Category | Score | Budget | Status |
              |----------|-------|--------|--------|
              | Performance | ${metrics.performance}% | 90% | ${metrics.performance >= 90 ? '✅' : '❌'} |
              | Accessibility | ${metrics.accessibility}% | 100% | ${metrics.accessibility >= 100 ? '✅' : '❌'} |
              | Best Practices | ${metrics.bestPractices}% | 95% | ${metrics.bestPractices >= 95 ? '✅' : '❌'} |
              | SEO | ${metrics.seo}% | 90% | ${metrics.seo >= 90 ? '✅' : '❌'} |
              
              ### Core Web Vitals
              | Metric | Value | Budget | Status |
              |--------|-------|--------|--------|
              | First Contentful Paint | ${metrics.fcp}ms | 1500ms | ${metrics.fcp <= 1500 ? '✅' : '❌'} |
              | Largest Contentful Paint | ${metrics.lcp}ms | 2500ms | ${metrics.lcp <= 2500 ? '✅' : '❌'} |
              | Time to Interactive | ${metrics.tti}ms | 3000ms | ${metrics.tti <= 3000 ? '✅' : '❌'} |
              | Total Blocking Time | ${metrics.tbt}ms | 300ms | ${metrics.tbt <= 300 ? '✅' : '❌'} |
              | Cumulative Layout Shift | ${metrics.cls} | 0.1 | ${parseFloat(metrics.cls) <= 0.1 ? '✅' : '❌'} |
              
              ### Summary
              - **Commit**: \`${report.commit.substring(0, 7)}\`
              - **Branch**: \`${report.branch}\`
              - **Timestamp**: ${report.timestamp}
              - **Node Version**: ${{ matrix.node-version }}
              
              ${report.passed ? 
                '🎯 All performance budgets are within acceptable limits!' : 
                '⚠️ Some performance budgets have been exceeded. Please review and optimize before merging.'
              }`;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
              
            } catch (error) {
              console.error('Error creating performance comment:', error);
            }

  lighthouse-ci:
    name: Lighthouse CI
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run Lighthouse CI
        run: npm run lighthouse:collect
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload Lighthouse CI results
        run: |
          if [ -n "${{ secrets.LHCI_GITHUB_APP_TOKEN }}" ]; then
            npx lhci upload
          else
            echo "LHCI_GITHUB_APP_TOKEN not set, skipping upload"
          fi

  performance-regression-check:
    name: Performance Regression Check
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Check for performance regression
        run: |
          echo "🔍 Checking for performance-related changes..."

          # Check if performance-critical files have changed
          CHANGED_FILES=$(git diff --name-only origin/main...HEAD)
          CRITICAL_FILES="next.config.mjs tailwind.config.ts middleware.ts package.json performance-budget.json"

          echo "Changed files:"
          echo "$CHANGED_FILES"

          for file in $CRITICAL_FILES; do
            if echo "$CHANGED_FILES" | grep -q "$file"; then
              echo "⚠️ Performance-critical file changed: $file"
              echo "PERFORMANCE_CRITICAL_CHANGE=true" >> $GITHUB_ENV
            fi
          done

          # Check for large bundle changes
          if echo "$CHANGED_FILES" | grep -E "\.(js|ts|jsx|tsx|css)$" | wc -l | grep -q "[1-9][0-9]"; then
            echo "⚠️ Many source files changed, potential bundle impact"
            echo "BUNDLE_IMPACT=true" >> $GITHUB_ENV
          fi

      - name: Run performance check if critical changes detected
        if: env.PERFORMANCE_CRITICAL_CHANGE == 'true' || env.BUNDLE_IMPACT == 'true'
        run: npm run performance:check
