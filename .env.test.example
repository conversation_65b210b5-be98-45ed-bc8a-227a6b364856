# Test Environment Variables Template
# Use this template for testing and CI/CD

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

NODE_ENV=test
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Tucsenberg Web Stable (Test)"

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

NEXT_PUBLIC_SHOW_DEBUG_INFO=true
NEXT_PUBLIC_ENABLE_DEVTOOLS=true

# =============================================================================
# DATABASE CONFIGURATION (TEST DATABASE)
# =============================================================================

DATABASE_URL="postgresql://test_user:test_password@localhost:5432/tucsenberg_test"
DATABASE_DIRECT_URL="postgresql://test_user:test_password@localhost:5432/tucsenberg_test"

# =============================================================================
# AUTHENTICATION CONFIGURATION (TEST KEYS)
# =============================================================================

NEXTAUTH_SECRET="test-secret-key-for-testing-only-32-chars"
NEXTAUTH_URL="http://localhost:3000"
JWT_SECRET="test-jwt-secret-for-testing-only-32"

# =============================================================================
# EMAIL SERVICE CONFIGURATION (MOCK/TEST)
# =============================================================================

SMTP_HOST="localhost"
SMTP_PORT=1025
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_FROM="<EMAIL>"

# =============================================================================
# ANALYTICS CONFIGURATION (DISABLED)
# =============================================================================

NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=false

# =============================================================================
# API KEYS AND INTEGRATIONS (TEST KEYS)
# =============================================================================

OPENAI_API_KEY="sk-test-key-for-testing-only"
STRIPE_SECRET_KEY="sk_test_your-test-stripe-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-test-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_test_webhook_secret"

# =============================================================================
# SECURITY CONFIGURATION (TEST KEYS)
# =============================================================================

ENCRYPTION_KEY="test-encryption-key-32-characters-"
