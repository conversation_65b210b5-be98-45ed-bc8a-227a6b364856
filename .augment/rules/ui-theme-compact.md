---
type: 'agent_requested'
description: 'UI component patterns with Radix UI, shadcn/ui, and next-themes integration'
---

# UI Component and Theme Patterns

## Component Design Principles

- Follow shadcn/ui New York style, use Radix UI primitives
- Support light/dark themes with next-themes, mobile-first responsive design
- Use Tailwind CSS with tailwindcss-animate for smooth animations

## Component Structure Template

```typescript
import { cn } from '@/lib/utils'
import { cva, type VariantProps } from 'class-variance-authority'

const variants = cva("base-classes", {
  variants: {
    variant: { default: "default-classes", secondary: "secondary-classes" },
    size: { sm: "small-classes", md: "medium-classes" },
  },
  defaultVariants: { variant: "default", size: "md" },
})

interface Props extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof variants> {}

export const Component = ({ className, variant, size, ...props }: Props) => (
  <div className={cn(variants({ variant, size, className }))} {...props} />
)
```

## Radix UI Patterns

```typescript
// Dialog: Use @radix-ui/react-dialog
<Dialog.Root open={open} onOpenChange={onOpenChange}>
  <Dialog.Portal>
    <Dialog.Overlay className="fixed inset-0 bg-black/50" />
    <Dialog.Content className="fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] bg-background border rounded-lg p-6">
      <Dialog.Title>{title}</Dialog.Title>
      {children}
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

// Dropdown: Use @radix-ui/react-dropdown-menu
<DropdownMenu.Root>
  <DropdownMenu.Trigger asChild>{trigger}</DropdownMenu.Trigger>
  <DropdownMenu.Content className="bg-popover border rounded-md p-1">
    {items.map(item => (
      <DropdownMenu.Item onSelect={() => onSelect(item.value)}>{item.label}</DropdownMenu.Item>
    ))}
  </DropdownMenu.Content>
</DropdownMenu.Root>
```

## Theme System with next-themes

```typescript
// Provider setup
<ThemeProvider attribute="class" defaultTheme="system" enableSystem>
  {children}
</ThemeProvider>

// Theme toggle
const { theme, setTheme } = useTheme()
<button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
  Toggle Theme
</button>
```

## CSS Variables Architecture

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --secondary: 210 40% 96%;
  --border: 214.3 31.8% 91.4%;
}
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --secondary: 217.2 32.6% 17.5%;
  --border: 217.2 32.6% 17.5%;
}
```

## Tailwind Configuration

```typescript
export default {
  darkMode: ['class'],
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: { DEFAULT: 'hsl(var(--primary))', foreground: 'hsl(var(--primary-foreground))' },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
      },
    },
  },
};
```

## Theme-Aware Components

```typescript
import { useTheme } from 'next-themes'

export function ThemeAwareCard({ children, className }) {
  return (
    <div className={cn(
      'rounded-lg border bg-card text-card-foreground shadow-sm',
      'transition-colors duration-200',
      className
    )}>
      {children}
    </div>
  )
}
```

## Animation Integration

```typescript
import { motion } from 'framer-motion'

export function AnimatedButton({ children, variant = 'default', ...props }) {
  return (
    <motion.button
      className={cn('inline-flex items-center justify-center rounded-md px-4 py-2', 'transition-colors focus-visible:outline-none focus-visible:ring-2', variant === 'default' && 'bg-primary text-primary-foreground hover:bg-primary/90')}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      {...props}
    >
      {children}
    </motion.button>
  )
}
```

## Accessibility Standards

- Include proper ARIA labels and roles
- Ensure keyboard navigation support
- Maintain color contrast ratios (WCAG 2.1 AA)
- Provide focus indicators for interactive elements
- Support screen readers with semantic HTML

## Form Components

- Use controlled components with proper validation
- Implement error states with clear messaging
- Support both client and server-side validation
- Include loading states for async operations

## Performance Guidelines

- Use React.memo() for expensive components
- Implement proper loading states
- Use dynamic imports for code splitting
- Minimize theme transition flashes with proper SSR handling
