---
type: 'agent_requested'
description: 'Quality gates, CI/CD processes, and performance budgets'
---

# Quality Assurance and CI/CD Standards

## Quality Gates & Error Prevention

```bash
# Complete quality gate pipeline
npm run quality-gate
# Executes: type-check:strict + lint:strict + test:coverage + test:a11y + error-check
```

### Error Prevention Checks

- [ ] TypeScript strict mode enabled, all dependencies installed
- [ ] Component props have explicit interfaces, event handlers typed correctly
- [ ] Unused parameters prefixed with underscore, useEffect dependencies complete
- [ ] useState/useTransition/useDeferredValue properly typed
- [ ] Server Components use async/await patterns, Suspense boundaries implemented

### Quality Metrics Thresholds

- **Type Coverage**: ≥95%, **Code Coverage**: ≥80%, **Accessibility**: Zero violations
- **Performance**: Core Web Vitals compliance, **Bundle Size**: Within budget limits

## CI/CD Pipeline Configuration

### Pre-commit Quality Checks

```bash
# .husky/pre-commit
npm run lint-staged
npm run type-check
npm run test:coverage
```

### GitHub Actions Workflow

```yaml
# .github/workflows/quality-gate.yml
name: Quality Gate
on: [push, pull_request]
jobs:
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run quality-gate
      - run: npm run lighthouse:ci
```

## Performance Budget Management

### Lighthouse CI Configuration

```javascript
// lighthouserc.js - Performance budgets
module.exports = {
  ci: {
    collect: { url: ['http://localhost:3000'], numberOfRuns: 3 },
    assert: { assertions: { 'categories:performance': ['error', { minScore: 0.9 }] } },
  },
};
```

## Code Quality Standards

### TypeScript Strict Configuration

```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### ESLint Quality Rules

```javascript
// .eslintrc.js
module.exports = {
  extends: ['next/core-web-vitals', 'prettier'],
  rules: {
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    'react-hooks/exhaustive-deps': 'error',
    'no-console': 'warn',
  },
};
```

## Bundle Analysis

### Next.js Configuration

```javascript
// next.config.mjs
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer({
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
});
```

## Configuration Validation

### Environment Validation

```javascript
// Validate required environment variables
const requiredEnvVars = ['NODE_ENV', 'NEXT_PUBLIC_APP_URL'];
const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missing.length > 0) process.exit(1);
```

## Quality Gate Scripts

```json
{
  "scripts": {
    "quality-gate": "npm run type-check:strict && npm run lint:strict && npm run test:coverage && npm run test:a11y",
    "type-check:strict": "tsc --noEmit --strict",
    "type-coverage": "type-coverage --at-least 95",
    "lint:strict": "eslint --ext .js,.jsx,.ts,.tsx . --max-warnings 0",
    "test:coverage": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":80,\"functions\":80,\"lines\":80,\"statements\":80}}'",
    "lighthouse:ci": "lhci autorun",
    "config-validate": "npm run config-check && npm run type-check"
  }
}
```

## Coverage Reporting

```javascript
// jest.config.js coverage configuration
module.exports = {
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: { branches: 80, functions: 80, lines: 80, statements: 80 },
  },
};
```

## Deployment Quality Checks

### Pre-deployment Validation

```bash
#!/bin/bash
# scripts/pre-deploy.sh
echo "🚀 Running pre-deployment quality checks..."

npm run build || exit 1
npm run quality-gate || exit 1
npm run lighthouse:ci || exit 1
npm audit --audit-level moderate || exit 1

echo "✅ All quality checks passed - ready for deployment"
```

### Production Monitoring

```javascript
// Monitor Core Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  console.log(metric);
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```
