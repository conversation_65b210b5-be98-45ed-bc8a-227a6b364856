# Augment Rules Configuration for tucsenberg-web-stable

## Overview

This directory contains 12 optimized Augment AI rules that guide development practices for the
tucsenberg-web-stable project. These rules ensure consistent, high-quality code that follows
enterprise standards. Rules have been optimized to stay within Augment's character limits while
maintaining comprehensive coverage of all development areas.

## Rule Files Structure

### 🔧 `coding-standards.md` (Always)

**Type**: Always  
**Purpose**: Core TypeScript/React development standards  
**Key Areas**: TypeScript strict mode, React patterns, import standards, naming conventions

### ⚛️ `react18-nextjs14-essentials.md` (Auto)

**Type**: Auto  
**Description**: Essential React 18 and Next.js 14 patterns and best practices  
**Key Areas**: React 18 hooks (useTransition, useDeferredValue), Next.js 14 App Router, Suspense
patterns, Server Components

### 🔄 `git-workflow.md` (Auto)

**Type**: Auto  
**Description**: Git workflow, commit standards, and quality gates  
**Key Areas**: Conventional commits, pre-commit hooks, quality checks

### ⚡ `performance-optimization.md` (Auto)

**Type**: Auto  
**Description**: Performance optimization strategies and Core Web Vitals  
**Key Areas**: Next.js optimization, image handling, caching, monitoring

### 🔬 `technical-implementation-methodology.md` (Auto)

**Type**: Auto  
**Description**: Research-first methodology for technical decisions  
**Key Areas**: Documentation research, quality assessment, decision frameworks

### 🎨 `ui-theme-compact.md` (Auto)

**Type**: Auto  
**Description**: UI component patterns with Radix UI, shadcn/ui, and next-themes integration  
**Key Areas**: shadcn/ui patterns, Radix components, theme system, CSS variables

### 🗃️ `data-code-compact.md` (Auto)

**Type**: Auto  
**Description**: Data validation with Zod, type patterns, and code generation templates  
**Key Areas**: Zod validation, type definitions, component templates, error handling

### 🧪 `testing-compact.md` (Auto)

**Type**: Auto  
**Description**: Testing strategy and best practices with Jest and Testing Library  
**Key Areas**: Jest configuration, component testing, accessibility testing

### ♿ `accessibility-compact.md` (Auto)

**Type**: Auto  
**Description**: Accessibility testing and WCAG 2.1 AA compliance with jest-axe integration  
**Key Areas**: WCAG compliance, jest-axe testing, semantic HTML, ARIA patterns

### 🔒 `quality-compact.md` (Auto)

**Type**: Auto  
**Description**: Quality gates, CI/CD processes, and performance budgets  
**Key Areas**: Quality metrics, CI/CD pipeline, performance budgets

### 📚 `augment-setup-guide.md` (Manual)

**Type**: Manual  
**Description**: Setup and configuration guide for Augment AI integration  
**Key Areas**: Installation, configuration, usage patterns

## Usage Guidelines

### For Developers

1. **Always Rules** are automatically included in every AI interaction
2. **Auto Rules** are intelligently selected based on your query context
3. **Manual Rules** can be explicitly referenced using @filename

### For AI Assistant

- Follow coding-standards.md for all code generation
- Reference appropriate auto rules based on task context
- Use react18-nextjs14-essentials.md for modern React/Next.js patterns
- Ensure compliance with accessibility and quality standards
- Apply performance optimization strategies consistently

## Rule Optimization Strategy

- **Character Efficiency**: Optimized content while maintaining comprehensive coverage
- **Consolidation**: Merged related rules to reduce redundancy
- **Focus Areas**: Prioritized most critical development patterns
- **Modern Patterns**: Added React 18 and Next.js 14 specific guidance
- **Quality Preserved**: Maintained all essential best practices

## Integration with Development Workflow

- Rules complement existing ESLint/Prettier configuration
- Align with husky pre-commit hooks and quality checks
- Support CI/CD pipeline requirements with quality gates
- Enhance code review process with consistent standards
- Integrate with Jest testing framework and accessibility testing
- Support Lighthouse CI performance monitoring
- Enable next-themes and Zod validation patterns
- Guide React 18 concurrent features and Next.js 14 App Router usage

## File Organization

```
.augment/rules/
├── README.md                              # This file
├── coding-standards.md                    # Always applied
├── react18-nextjs14-essentials.md        # React/Next.js patterns
├── accessibility-compact.md              # WCAG compliance
├── data-code-compact.md                  # Data validation
├── git-workflow.md                       # Git standards
├── performance-optimization.md           # Performance
├── quality-compact.md                    # Quality gates
├── technical-implementation-methodology.md # Research methodology
├── testing-compact.md                    # Testing strategy
├── ui-theme-compact.md                   # UI patterns
└── augment-setup-guide.md                # Setup guide
```

This configuration ensures comprehensive development guidance while maintaining optimal performance
within Augment's constraints.
