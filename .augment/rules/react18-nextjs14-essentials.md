---
type: 'agent_requested'
description: 'Essential React 18 and Next.js 14 patterns and best practices'
---

# React 18 + Next.js 14 Essentials

## React 18 Patterns

```typescript
// useTransition for non-urgent updates
const [isPending, startTransition] = useTransition();
const handleSearch = (value: string) => {
  setQuery(value);
  startTransition(() => performSearch(value).then(setResults));
};

// useDeferredValue for expensive computations
const deferredQuery = useDeferredValue(query);
const results = useMemo(() => performExpensiveCalculation(deferredQuery), [deferredQuery]);

// Suspense boundaries
function App() {
  return (
    <Suspense fallback={<Loading />}>
      <ErrorBoundary fallback={<ErrorFallback />}>
        <DataComponent />
      </ErrorBoundary>
    </Suspense>
  );
}
```

## Next.js 14 Patterns

```typescript
// Server Components data fetching
async function ServerComponent({ id }: { id: string }) {
  const data = await fetch(`https://api.example.com/data/${id}`, {
    next: { revalidate: 3600 }
  }).then(res => res.json());

  return (
    <div>
      <h1>{data.title}</h1>
      <Suspense fallback={<CommentsSkeleton />}>
        <Comments postId={id} />
      </Suspense>
    </div>
  );
}

// API Routes
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  if (!id) return Response.json({ error: 'ID required' }, { status: 400 });
  const user = await getUserById(id);
  return Response.json(user);
}

export async function POST(request: Request) {
  const { name, email } = await request.json();
  if (!name || !email) return Response.json({ error: 'Required fields missing' }, { status: 400 });
  const user = await createUser({ name, email });
  return Response.json(user, { status: 201 });
}

// Client Component with optimistic updates
'use client';
function ClientComponent({ serverData }: { serverData: Data }) {
  const [optimisticData, setOptimisticData] = useState(serverData);
  const [isPending, startTransition] = useTransition();

  const handleUpdate = async (newData: Partial<Data>) => {
    setOptimisticData(prev => ({ ...prev, ...newData }));

    startTransition(async () => {
      try {
        const response = await fetch('/api/update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(newData)
        });

        if (!response.ok) throw new Error('Update failed');
        setOptimisticData(await response.json());
      } catch (error) {
        setOptimisticData(serverData);
      }
    });
  };

  return (
    <div>
      {isPending && <div>Updating...</div>}
      <DataDisplay data={optimisticData} onUpdate={handleUpdate} />
    </div>
  );
}
```

## Data Fetching & Forms

```typescript
// Static generation
export async function generateStaticParams() {
  const posts = await fetch('https://api.example.com/posts').then(res => res.json());
  return posts.map((post: Post) => ({ slug: post.slug }));
}

// Client-side with SWR
'use client';
function ClientDataComponent() {
  const { data, error, isLoading } = useSWR('/api/data', fetcher);
  if (error) return <ErrorComponent error={error} />;
  if (isLoading) return <LoadingSpinner />;
  return <DataDisplay data={data} />;
}

// Form handling
'use client';
function ContactForm() {
  const [state, setState] = useState({ loading: false, error: null, success: false });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setState({ loading: true, error: null, success: false });

    const formData = new FormData(e.currentTarget);
    const data = { name: formData.get('name') as string, email: formData.get('email') as string };

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (!response.ok) throw new Error('Failed to submit');
      setState({ loading: false, error: null, success: true });
    } catch (error) {
      setState({ loading: false, error: error.message, success: false });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input name="name" required />
      <input name="email" type="email" required />
      <button type="submit" disabled={state.loading}>
        {state.loading ? 'Submitting...' : 'Submit'}
      </button>
      {state.error && <div className="error">{state.error}</div>}
      {state.success && <div className="success">Message sent!</div>}
    </form>
  );
}
```

## Performance & Error Handling

```typescript
// Code splitting & Image optimization
import dynamic from 'next/dynamic';
import Image from 'next/image';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>Loading...</p>,
  ssr: false
});

function OptimizedImage() {
  return <Image src="/hero.jpg" alt="Hero image" width={800} height={600} priority />;
}

// Error Boundary
'use client';
class ErrorBoundary extends Component<
  { children: ReactNode; fallback: ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  render() {
    if (this.state.hasError) {
      return <this.props.fallback error={this.state.error!} />;
    }
    return this.props.children;
  }
}

// Error UI (error.tsx)
'use client';
export default function Error({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h2 className="text-xl font-semibold mb-4">Something went wrong!</h2>
      <button onClick={reset} className="px-4 py-2 bg-blue-500 text-white rounded">
        Try again
      </button>
    </div>
  );
}
```
