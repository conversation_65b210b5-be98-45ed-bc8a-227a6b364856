---
type: 'always_apply'
description:
  'Technical implementation methodology and research-first approach for technology decisions'
---

# Technical Implementation Best Practices

## Research-First Methodology (5-Step Process)

### Step 1: Official Documentation Research

- Use resolve-library-id + get-library-docs to obtain official documentation (6000-8000 tokens)
- Prioritize authoritative sources over community tutorials
- Focus on latest stable versions and official examples
- Document key APIs, configuration options, and best practices

### Step 2: Information Quality Assessment

Evaluate sources based on:

- **Authority**: Official docs > Maintained libraries > Community content
- **Timeliness**: Latest versions, recent updates, current best practices
- **Completeness**: Comprehensive coverage of use cases and edge cases

### Step 3: Critical Decision Point Identification

Identify key decisions for:

- **Architecture**: Component structure, data flow, state management
- **Configuration**: Build settings, environment variables, deployment options
- **Dependencies**: Version compatibility, bundle size impact, maintenance status
- **Validation**: Testing strategies, error handling, performance monitoring

### Step 4: Detailed Implementation Plan

- Break down implementation into measurable milestones
- Define success criteria and validation checkpoints
- Identify potential risks and mitigation strategies
- Plan for rollback scenarios if needed

### Step 5: Progressive Execution and Validation

- Implement in small, testable increments
- Validate each step against success criteria
- Gather feedback and adjust approach as needed
- Document lessons learned for future reference

## Application Scenarios

### High-Value Use Cases

- **New Technology Stack Integration**: React 19, Next.js 15, new UI libraries
- **Architecture Changes**: Migration to App Router, state management updates
- **Performance Optimization**: Core Web Vitals improvements, bundle optimization
- **Security Enhancements**: Authentication systems, data protection measures

### Expected Outcomes

- **80% Reduction in Rework**: Thorough research prevents common pitfalls
- **90% First-Time Success Rate**: Systematic approach increases implementation quality
- **Faster Decision Making**: Structured evaluation process
- **Knowledge Retention**: Documented decisions for team learning

## Integration with Current Workflow

- Apply before major feature development
- Use during technology evaluation phases
- Implement for complex problem-solving scenarios
- Document decisions in project documentation

## Quality Gates

- All research sources documented and verified
- Key decisions justified with evidence
- Implementation plan reviewed and approved
- Success criteria defined and measurable
- Rollback plan prepared for critical changes
