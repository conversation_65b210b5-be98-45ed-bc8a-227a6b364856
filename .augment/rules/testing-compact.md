---
type: 'agent_requested'
description: 'Testing strategy and best practices with Jest and Testing Library'
---

# Testing Strategy and Best Practices

## Testing Philosophy

- **Test-Driven Development**: Write tests before implementation when possible
- **Coverage Goals**: Maintain minimum 80% code coverage
- **Quality over Quantity**: Focus on meaningful tests that catch real bugs
- **Fast Feedback**: Tests should run quickly

## Jest Configuration

```javascript
// jest.config.js - Basic setup with 80% coverage threshold
const nextJest = require('next/jest');
const createJestConfig = nextJest({ dir: './' });

module.exports = createJestConfig({
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jsdom',
  collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}', '!src/**/*.d.ts'],
  coverageThreshold: { global: { branches: 80, functions: 80, lines: 80, statements: 80 } },
});

// jest.setup.js
import '@testing-library/jest-dom';
import { toHaveNoViolations } from 'jest-axe';
expect.extend(toHaveNoViolations);
```

## Testing Patterns

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { axe } from 'jest-axe';
import { renderHook, act } from '@testing-library/react';

// Component testing
describe('Button Component', () => {
  it('renders and handles events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('meets accessibility standards', async () => {
    const { container } = render(<Button>Accessible button</Button>);
    expect(await axe(container)).toHaveNoViolations();
  });
});

// Hook testing
describe('useLocalStorage', () => {
  it('manages localStorage state', () => {
    const { result } = renderHook(() => useLocalStorage('key', 'default'));
    act(() => result.current[1]('new-value'));
    expect(localStorage.getItem('key')).toBe('"new-value"');
  });
});
```

## API Route Testing

```typescript
import { createMocks } from 'node-mocks-http';
import handler from '@/app/api/users/route';

describe('/api/users', () => {
  it('returns users list', async () => {
    const { req, res } = createMocks({ method: 'GET' });
    await handler(req, res);
    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('users');
  });
});
```

## Page Integration Testing

```typescript
import { render, screen } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';

const messages = { HomePage: { title: 'Welcome' } };

describe('Home Page', () => {
  it('renders page content correctly', () => {
    render(
      <NextIntlClientProvider locale="en" messages={messages}>
        <HomePage />
      </NextIntlClientProvider>
    );
    expect(screen.getByText('Welcome')).toBeInTheDocument();
  });
});
```

## Mocking Strategies

```typescript
// Mock libraries
jest.mock('framer-motion', () => ({
  motion: { div: ({ children, ...props }) => <div {...props}>{children}</div> },
}));
jest.mock('next-themes', () => ({ useTheme: () => ({ theme: 'light', setTheme: jest.fn() }) }));
```

## Test Organization

### File Structure and Naming

- **Test files**: `component.test.tsx` or `component.spec.tsx`
- **Test descriptions**: Use descriptive names explaining expected behavior
- **Test groups**: Group related tests using `describe` blocks
- **Test cases**: Start with action verbs (renders, handles, validates)
- **File location**: Co-locate tests with components or use `__tests__` folder

## Performance Testing

```typescript
describe('Performance Tests', () => {
  it('renders large list efficiently', () => {
    const items = Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }));
    const startTime = performance.now();
    render(<LargeList items={items} />);
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(100); // Under 100ms
  });
});
```

## Test Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:a11y": "jest --testPathPattern=a11y",
    "test:performance": "jest --testPathPattern=performance"
  }
}
```

## Quality Gates

- **Minimum Coverage**: 80% across all metrics
- **Accessibility**: Zero violations in jest-axe tests
- **Performance**: Component render times under defined thresholds
- **Type Safety**: All tests must pass TypeScript compilation
