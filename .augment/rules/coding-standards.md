---
type: 'always_apply'
---

# TypeScript/React Development Standards

## Core Principles

- Use TypeScript strict mode with enhanced type safety
- Prefer functional components with hooks over class components
- Follow Next.js 14 App Router patterns exclusively
- Use server components by default, client components only when necessary

## TypeScript Guidelines (Enhanced Standards)

- **Strict Mode Compliance**: Never use `any` type - use proper type definitions or `unknown`
- **Type Safety**: Avoid type assertions with `as` or `!` - prefer proper type guards
- **Data Structures**: Avoid enums; use objects with `as const` or maps instead
- **Interface Preference**: Prefer interfaces over types for object shapes
- **Variable Usage**: Ensure all variables and parameters are used
  (noUnusedLocals/noUnusedParameters)
- **Type Definitions**: Define proper return types for functions and correct type definitions
- **Path Aliases**: Use @/ path aliases instead of relative imports consistently
- **Exact Types**: Use exact optional property types for better type safety
- **Import Management**: Only import what you use - avoid wildcard imports
- **Reference Validation**: Ensure all imports are actually referenced in code
- **Documentation**: Use JSDoc to document public classes and methods
- Enable and respect all strict mode options in tsconfig.json

## React Patterns (IDE Automated Standards)

- Use Next.js App Router with server components as default
- Client components must have 'use client' directive at top
- Prefer async server components for data fetching
- Use proper TypeScript interfaces for component props
- **Hook Dependencies**: Ensure useEffect dependencies are complete and accurate
- **Hook Order**: Maintain consistent Hook calling order (no conditional hooks)
- **Unnecessary Effects**: Avoid unnecessary useEffect - prefer server-side data fetching
- **Component Lifecycle**: Follow proper component lifecycle patterns

## Import/Export Standards

- Use @/ path aliases for all internal imports
- Import only what you use - avoid wildcard imports
- Group imports: external libraries first, then internal modules
- Use named exports for components and utilities
- Use default exports only for pages and layouts

## Naming Conventions

- **Files/Directories**: Use kebab-case (user-profile.tsx, auth-components/)
- **Components**: Use PascalCase (UserProfile, AuthButton)
- **Variables/Functions**: Use camelCase (userData, handleSubmit)
- **Constants**: Use UPPERCASE for environment variables (API_URL, MAX_RETRIES)
- **Boolean Variables**: Use descriptive verbs (isLoading, hasPermission, canEdit)
- **Complete Words**: Avoid abbreviations except standard ones (API, URL, ID)

## Code Organization

- Components in src/components with clear folder structure
- Utilities in src/lib with proper TypeScript types
- Types in src/types for shared interfaces
- Follow Next.js 14 file conventions (page.tsx, layout.tsx, error.tsx, loading.tsx)

## Code Generation Rules

- **Component Syntax**: Use const declarations, let TypeScript infer return types
- **Event Handlers**: Prefix with "handle" (handleClick, handleSubmit, handleKeyDown)
- **Early Returns**: Use early returns for better code readability
- **Descriptive Naming**: Use clear, descriptive variable and function names
- **Accessibility**: Include ARIA attributes, tabindex, and semantic HTML

## Function Design Principles

- **Function Length**: <20 lines, single purpose
- **Naming**: Start with verbs (get, set, create, update, delete, validate)
- **Booleans**: Use verbs (isLoading, hasError, canDelete, shouldUpdate)
- **Parameters**: Use default values, RO-RO pattern for multiple params
- **Abstraction**: One level per function, prefer map/filter/reduce

## ESLint Configuration

```json
{
  "extends": ["next/core-web-vitals", "@typescript-eslint/recommended"],
  "rules": {
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
    "react-hooks/exhaustive-deps": "warn",
    "react/no-unescaped-entities": "error",
    "react-hooks/rules-of-hooks": [
      "error",
      { "additionalHooks": "useTransition|useDeferredValue|useId" }
    ]
  }
}
```

### Quick Fixes

```typescript
// TS6133: const _unusedVar = someValue; // Add underscore prefix
// react/no-unescaped-entities: <p>Use &ldquo;quotes&rdquo; instead of "quotes"</p>
// @typescript-eslint/no-explicit-any: const handler = (event: React.MouseEvent) => {};
```

## Performance Guidelines

- Use Next.js Image component, loading.tsx files, React.memo() for expensive components
- Implement error boundaries with error.tsx files
- Optimize bundle size with dynamic imports, use server components for data fetching
