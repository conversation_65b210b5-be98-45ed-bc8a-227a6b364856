---
type: 'agent_requested'
description: 'Accessibility testing and WCAG 2.1 AA compliance with jest-axe integration'
---

# Accessibility Standards and Testing

## WCAG 2.1 AA Compliance

- **Perceivable**: Information presentable in ways users can perceive
- **Operable**: Interface components operable by all users
- **Understandable**: Information and UI operation understandable
- **Robust**: Content robust for various assistive technologies

## jest-axe Testing Setup

```javascript
// jest.setup.js
import '@testing-library/jest-dom';
import { toHaveNoViolations } from 'jest-axe';
expect.extend(toHaveNoViolations);
```

### Component Testing Pattern

```typescript
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

describe('Component Accessibility', () => {
  it('meets WCAG 2.1 AA standards', async () => {
    const { container } = render(<Component />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

});
```

````

## Semantic HTML Guidelines

```typescript
// ✅ Use semantic elements
<header>
  <nav aria-label="Main navigation">
    <ul><li><a href="/">Home</a></li></ul>
  </nav>
</header>
<main>
  <h1>Page Title</h1>
  <section aria-labelledby="content-heading">
    <h2 id="content-heading">Content Section</h2>
  </section>
</main>
<footer><p>&copy; 2025 Company</p></footer>
````

## ARIA Patterns

```typescript
// Modal Dialog
<div role="dialog" aria-modal="true" aria-labelledby="modal-title">
  <h2 id="modal-title">{title}</h2>
  <button aria-label="Close modal">×</button>
</div>

// Dropdown Menu
<button aria-haspopup="true" aria-expanded={isOpen}>Menu</button>
<ul role="menu" aria-labelledby="menu-trigger">
  <li role="menuitem"><a href="/link">Item</a></li>
</ul>

// Loading State
<button aria-busy={isLoading}>
  {isLoading && <span className="sr-only">Loading...</span>}
  Submit
</button>
```

## Color Contrast Standards

```css
/* WCAG 2.1 AA: 4.5:1 normal text, 3:1 large text */
:root {
  --text-primary: #1a1a1a; /* 16.94:1 on white */
  --accent: #0066cc; /* 7.73:1 on white */
}

[data-theme='dark'] {
  --text-primary: #ffffff; /* 21:1 on dark */
  --accent: #4da6ff; /* 8.59:1 on dark */
}

.focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}
```

## Keyboard Navigation

```typescript
// Custom keyboard navigation hook
const useKeyboardNavigation = (items) => {
  const [activeIndex, setActiveIndex] = useState(-1);

  const handleKeyDown = (event) => {
    switch (event.key) {
      case 'ArrowDown':
        setActiveIndex(prev => (prev + 1) % items.length);
        break;
      case 'ArrowUp':
        setActiveIndex(prev => (prev - 1 + items.length) % items.length);
        break;
      case 'Escape':
        setActiveIndex(-1);
        break;
    }
  };

  return { activeIndex, handleKeyDown };
};

// Skip link
const SkipLink = () => (
  <a href="#main-content" className="sr-only focus:not-sr-only">
    Skip to main content
  </a>
);
```

## Screen Reader Support

```css
/* Screen reader only utility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

```typescript
// Descriptive content
const AccessibleImage = ({ src, alt, decorative = false }) => (
  <img src={src} alt={decorative ? "" : alt} role={decorative ? "presentation" : undefined} />
);

// Complex content descriptions
const DataVisualization = ({ data, description }) => (
  <div>
    <div aria-describedby="chart-description">
      <Chart data={data} />
    </div>
    <div id="chart-description" className="sr-only">{description}</div>
  </div>
);
```

## Testing Scripts

```json
{
  "scripts": {
    "test:a11y": "jest --testPathPattern=a11y",
    "lighthouse:a11y": "lhci collect --settings.onlyCategories=accessibility"
  }
}
```

## Accessibility Checklist

### Development

- [ ] Keyboard accessible interactive elements
- [ ] Visible focus indicators with proper contrast
- [ ] Images have appropriate alt text
- [ ] Form inputs have associated labels
- [ ] Error messages properly associated
- [ ] Color not sole means of conveying information
- [ ] Text meets contrast ratios (4.5:1 normal, 3:1 large)
- [ ] Logical heading hierarchy
- [ ] Correct ARIA usage
- [ ] Semantic HTML structure

### Testing

- [ ] jest-axe tests pass
- [ ] Lighthouse accessibility score ≥95%
- [ ] Manual keyboard navigation works
- [ ] Screen reader testing completed
- [ ] Color contrast validated
- [ ] Focus management in dynamic content
