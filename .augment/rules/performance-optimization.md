---
type: 'agent_requested'
description: ' Performance optimization strategies and Core Web Vitals  '
---

# Performance Optimization Guidelines

## Core Web Vitals 2025 Compliance

- Largest Contentful Paint (LCP): < 2.5s
- First Input Delay (FID): < 100ms
- Cumulative Layout Shift (CLS): < 0.1
- Interaction to Next Paint (INP): < 200ms

## Next.js Optimization Strategies

- Use server components by default for better performance
- Implement proper image optimization with next/image
- Use dynamic imports for code splitting large components
- Leverage Next.js built-in font optimization with next/font
- Implement proper caching strategies for API routes

## Image Optimization

```typescript
import Image from 'next/image'

// Always specify width and height
<Image
  src="/image.jpg"
  alt="Description"
  width={800}
  height={600}
  priority={isAboveFold}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

## Bundle Size Optimization

- Use dynamic imports for heavy components
- Implement tree shaking for unused code
- Analyze bundle with npm run build-analyze
- Avoid importing entire libraries when only using specific functions
- Use next/dynamic for client-side only components

## Loading Performance

- Implement proper loading states for async operations
- Use React.Suspense for component-level loading
- Preload critical resources with next/head
- Implement progressive enhancement patterns
- Use skeleton screens for better perceived performance

## Runtime Performance

- Use React.memo() for expensive components
- Implement proper useCallback and useMemo usage
- Avoid unnecessary re-renders with proper dependency arrays
- Use virtualization for large lists
- Implement debouncing for search and input handlers

## Caching Strategies

- Use Next.js built-in caching for static assets
- Implement proper cache headers for API responses
- Use SWR or React Query for client-side data caching
- Leverage browser caching for static resources
- Implement service worker for offline functionality

## Monitoring and Measurement

- Use Web Vitals library for performance monitoring
- Implement proper error boundaries
- Monitor bundle size changes in CI/CD
- Use Lighthouse CI for automated performance testing
- Track Core Web Vitals in production

## Database and API Optimization

- Implement proper database indexing
- Use connection pooling for database connections
- Implement request deduplication
- Use proper HTTP caching headers
- Implement rate limiting for API endpoints
