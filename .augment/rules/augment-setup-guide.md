# Augment Rules Setup Guide

## Quick Setup Instructions

### 1. Import Rules into Augment

1. Open VS Code with Augment extension
2. Go to Settings > User Guidelines and Rules
3. Click "Import rules"
4. Select the `.augment/rules` directory
5. Augment will automatically detect and import all markdown files

### 2. Configure Rule Types

After import, configure each rule type:

**coding-standards.md**

- Type: Always
- This ensures TypeScript/React standards are always applied

**project-architecture.md**

- Type: Auto
- Description: "Project structure, architecture decisions, and technology stack guidelines"

**git-workflow.md**

- Type: Auto
- Description: "Git workflow, commit standards, quality checks, and code review process"

**ui-component-patterns.md**

- Type: Auto
- Description: "UI component development, styling patterns, and design system guidelines"

**performance-optimization.md**

- Type: Auto
- Description: "Performance optimization, Core Web Vitals, and Next.js best practices"

### 3. Verify Setup

Test the configuration by asking Augment:

- "Create a new React component" (should trigger coding-standards + ui-component-patterns)
- "Help me optimize this page performance" (should trigger performance-optimization)
- "How should I structure this new feature?" (should trigger project-architecture)

## Example Usage Scenarios

### Scenario 1: Creating a New Component

**Query**: "Create a button component with variants" **Expected Rules**: coding-standards.md
(always) + ui-component-patterns.md (auto) **Result**: Component following shadcn/ui patterns with
proper TypeScript

### Scenario 2: Performance Issue

**Query**: "This page is loading slowly, help optimize it"  
**Expected Rules**: coding-standards.md (always) + performance-optimization.md (auto) **Result**:
Next.js optimization suggestions with Core Web Vitals focus

### Scenario 3: Git Workflow Question

**Query**: "How should I commit these changes?" **Expected Rules**: coding-standards.md (always) +
git-workflow.md (auto)  
**Result**: Conventional commit format with quality check reminders

## Troubleshooting

### Rules Not Loading

- Check file paths are correct in `.augment/rules/`
- Ensure files have `.md` extension
- Verify Augment extension is updated (v0.492.0+)
- Try manual import if auto-detection fails

### Rules Not Triggering

- Check rule descriptions match your query context
- Try manual rule selection with @filename
- Verify rule type configuration (Always/Auto/Manual)
- Check character limits (49,512 total for workspace rules)

### Performance Issues

- Monitor total rule character count
- Split large rules into focused files
- Use Auto rules instead of Always for context-specific guidance
- Regular cleanup of unused rules

## Best Practices

- Start with Always rules for core standards
- Use Auto rules for context-specific guidance
- Keep rule descriptions clear and specific
- Regular review and updates of rule content
- Test rule triggering with various query types
