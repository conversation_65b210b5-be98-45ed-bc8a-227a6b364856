---
type: 'agent_requested'
description: 'Git workflow, commit standards, and quality gates  '
---

# Git Workflow and Quality Standards

## Commit Standards

- Use conventional commit format: `type(scope): description`
- Types: feat, fix, docs, style, refactor, test, chore
- Keep commit messages under 72 characters for title
- Include detailed description for complex changes
- Reference issues/tickets when applicable

## Pre-commit Quality Checks

- All TypeScript must compile without errors
- ESLint must pass with no warnings or errors
- Prettier formatting must be applied
- All tests must pass
- No unused imports or variables allowed

## Branch Strategy

- Use feature branches for all development
- Branch naming: `feature/description`, `fix/issue-number`, `docs/update-type`
- Keep branches focused and small
- Rebase before merging to maintain clean history

## Code Review Requirements

- All code must be reviewed before merging
- Check for TypeScript strict mode compliance
- Verify proper error handling and edge cases
- Ensure accessibility standards are met
- Validate performance implications

## Quality Gates

- TypeScript strict mode must pass (npm run type-check)
- ESLint strict rules must pass (npm run lint:strict)
- Prettier formatting must be applied (npm run prettier)
- All quality checks combined (npm run quality-check)

## Automated Checks (<PERSON>sky + lint-staged)

- Pre-commit: ESLint fix + Prettier format for staged files
- Pre-push: Full quality check suite
- Commit message validation
- No direct commits to main/master branch

## File Organization Rules

- Use @/ path aliases consistently
- Group related files in appropriate directories
- Follow Next.js 14 file conventions
- Keep components focused and single-responsibility
- Maintain clear separation between client and server code

## Documentation Standards

- Update README.md for significant changes
- Document complex business logic with comments
- Maintain API documentation for endpoints
- Include setup instructions for new features
- Document breaking changes in CHANGELOG.md
