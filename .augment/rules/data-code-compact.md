---
type: 'agent_requested'
description: 'Data validation with Zod, type patterns, and code generation templates'
---

# Data Validation and Code Generation Patterns

## Zod Validation Patterns

```typescript
import { z } from 'zod';

// Basic schema
const UserSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  email: z.string().email(),
  age: z.number().min(0).max(150).optional(),
  preferences: z
    .object({
      theme: z.enum(['light', 'dark', 'system']),
      language: z.enum(['en', 'zh']),
    })
    .optional(),
});

type User = z.infer<typeof UserSchema>;

// Safe validation
function safeValidate(data: unknown) {
  const result = UserSchema.safeParse(data);
  return result.success
    ? { success: true, data: result.data }
    : { success: false, error: result.error.message };
}
```

## Form Validation

```typescript
const ContactFormSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  message: z.string().min(10).max(1000),
  consent: z.boolean().refine(val => val === true, 'Must agree to terms'),
});

const RegistrationSchema = z
  .object({
    username: z
      .string()
      .min(3)
      .regex(/^[a-zA-Z0-9_]+$/),
    password: z.string().min(8).regex(/[A-Z]/).regex(/[0-9]/),
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });
```

## API Response Validation

```typescript
// Generic API response wrapper
function createApiResponseSchema<T extends z.ZodType>(dataSchema: T) {
  return z
    .object({
      success: z.literal(true),
      data: dataSchema,
    })
    .or(
      z.object({
        success: z.literal(false),
        error: z.string(),
      })
    );
}
```

## Type Definition Best Practices

```typescript
// ✅ Use interfaces for object shapes
interface UserProfile {
  id: string;
  name: string;
  email: string;
}

// ✅ Use types for unions
type Status = 'pending' | 'approved' | 'rejected';

// ✅ Avoid enums, use objects with as const
const UserRole = {
  ADMIN: 'admin',
  USER: 'user',
} as const;

type UserRoleType = (typeof UserRole)[keyof typeof UserRole];
```

## Component Generation Templates

```typescript
// Server component (default)
const ComponentName = ({ title, children }: { title: string; children?: React.ReactNode }) => {
  return (
    <div>
      <h1>{title}</h1>
      {children}
    </div>
  );
};

// Client component
'use client';
const InteractiveComponent = () => {
  const [state, setState] = useState('');
  const handleClick = () => { /* logic */ };

  return <button onClick={handleClick}>Interactive</button>;
};
```

## Data Fetching Patterns

```typescript
// Server component data fetching
async function getData() {
  const res = await fetch('https://api.example.com/data', { next: { revalidate: 3600 } });
  if (!res.ok) throw new Error('Failed to fetch');
  return res.json();
}

export default async function Page() {
  const data = await getData();
  return <div>{/* Render with data */}</div>;
}
```

## Error Handling

```typescript
// Result type pattern
type Result<T, E = Error> = { success: true; data: T } | { success: false; error: E };

async function safeApiCall<T>(apiCall: () => Promise<T>): Promise<Result<T>> {
  try {
    const data = await apiCall();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error : new Error('Unknown') };
  }
}
```

## RO-RO Pattern

```typescript
// ❌ Multiple parameters
function createUser(name: string, email: string, age: number) {}

// ✅ Object parameter and return
interface CreateUserParams {
  name: string;
  email: string;
  age: number;
}

interface CreateUserResult {
  user: UserProfile;
  success: boolean;
}

function createUser(params: CreateUserParams): CreateUserResult {
  return { user: newUser, success: true };
}
```

## Metadata Templates

```typescript
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Page Title',
  description: 'Page description',
  openGraph: {
    title: 'Page Title',
    images: ['/og-image.jpg'],
  },
};
```

## Accessibility Patterns

```typescript
const AccessibleButton = ({ onClick, children }: ButtonProps) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') onClick();
  };

  return (
    <button
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      aria-label="Descriptive label"
      className="focus:outline-none focus:ring-2"
    >
      {children}
    </button>
  );
};
```
