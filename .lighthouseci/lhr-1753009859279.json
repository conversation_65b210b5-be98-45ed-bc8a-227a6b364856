{"lighthouseVersion": "12.6.1", "requestedUrl": "http://localhost:3000/", "mainDocumentUrl": "http://localhost:3000/", "finalDisplayedUrl": "http://localhost:3000/", "finalUrl": "http://localhost:3000/", "fetchTime": "2025-07-20T11:10:51.299Z", "gatherMode": "navigation", "runWarnings": [], "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2910.5, "credits": {"axe-core": "4.10.3"}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "notApplicable"}, "viewport": {"id": "viewport", "title": "Does not have a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 0, "scoreDisplayMode": "metricSavings", "explanation": "No `<meta name=\"viewport\">` tag found", "metricSavings": {"INP": 300}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 1058.9672, "numericUnit": "millisecond", "displayValue": "1.1 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.77, "scoreDisplayMode": "numeric", "numericValue": 3014.9344, "numericUnit": "millisecond", "displayValue": "3.0 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 1058.9672, "numericUnit": "millisecond", "displayValue": "1.1 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3000, "items": [{"timing": 375, "timestamp": 356276713231, "data": "data:image/jpeg;base64,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"}, {"timing": 750, "timestamp": 356277088231, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAMBAQEBAQAAAAAAAAAAAAQFBgMBBwII/8QALxAAAgICAgEDAwQCAgIDAAAAAAECAwQRBRIhBhMxFCJBByMyURVhUoEWFyRicv/EABQBAQAAAAAAAAAAAAAAAAAAAAD/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwD+qQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABif1A9Zv0zyHF01xslW39TnOGHbke3jKUYSbcE/b/AJOfaXjVUl8sich+oduHG+T43EnBZFuPTrP0268yvFbs/b/bW7VL5l4Wno3dmJj2yulbj0zldWqrXKCfeC3qMv7X3S8Px9z/ALKez0fwNlGTVLjMbWVfHIvkoJTtlG73kpy+ZRU/PV+NePgDGQ/UbK/z3IxyMXriYOP2nRRYp9rK5Zym4zcV2i1iw14jp/8Aadtyvr2ziniQy8LBndbN944ubO5RhuldlKNOt/vLxPp+NN9tmtxeH43E9r6XjsOn2oRrr9uiMekY9tRWl4S7z0v/ALS/tnCHpvg4V49cOF4yNePKU6YrFrSqlL+TiteG/wAtfIGK4r19yFsMRZeFjSy8rKvxK4QyOlEXDKsqTnJwcovVf+9tpaW/E7h/X13JZmDGPEwhh5F1WPO76rcoTshZJOMempQ3DXbstqSevway3g+JtpyareLwZ1ZTbvhLHg1a29vstfdtpPz+TvHj8KDg4YmPFwcXHVUV1cVqOvH4Xhf0gMNyf6jWYnKcpiY/CzuWJkLEhZK6VanY5Ux8v22ox/e+U5Px8Lfjrf66z6LL67OIwoTruvpTnyLjCXs197H2dXj5SiteUpN9dGvs4fjLM23Ms47Dnl2xjCy+VEXOcYtOKctbaTSa/ppHuXxHG5lDpy+PxL6Xb77hbTGUXZ/z01/L/fyBT+mfUt/O9roYFePh/UexCVmQ1bJe13bdfTw9tLr2f5f48xuL9a1ch65u9O140XGFWRZHJhObTlTOqE4tOCW926+2Uv4tPTNOsPGU4zWPSpxsdyl0W1NxcXL/APTTa386bRxp4njaeTt5Gnj8SvkLY9bMqFMVbNePDnrbX2x/P4X9AfO/Uv6g5+L6cx+Vox8ammyq3JUKcqNtsq1h5V0VJOGq59qI/iS3tedMtK/X2Rfm5WNi8M5+3kPGqnbfKuM5Ry4Y0uzdfhbn2XXvtR86bRq/8Bw+shf4nj9ZE5WXL6aH7s5RcZSl48txlKLb+U2vydYcTxteXkZVfH4kcrIcJXXKmKnY4NOLlLW3ppa38aAyvA+sszO5fkqc3jo14+Li0We3iud9qtldfVNaUV2inT4el/b+dLjyHrydHLXYNmDOn28muFbjY5Tug7YQk0vbcPHdbipOS2k1Fs2lWBh05P1NWJjwyOjh7sa0pdXLs1vW9OXnX9+TkuH4yObdmLjsNZd0oStvVEe9jh/Fylrba/G/gD5vV+puf9a5Pjce2vJxMO7Gx6cmVig7a8m1uc41tp9aYrr1aXzvXkvfU3r9cL/iFXxORkzz8KzPcFJqVdcHUpR8Rac/3V8uMfD3JeDRS9M8DKi+iXCcY6b5Kd1bxK+tkk3JOS15acm9v8t/2TczjsLO+n+tw8bI+nsVtPu1Rn7c18SjteGv7QGLxv1AtzMnIxcbjcWORTm2YMnkZ6rqc4QnOUlNQe46iknre+6aXR74ZH6k2VvMcOIi6qbPahZPKcIuSyKqH2l7fVLdvZOLl4j51tGzyOB4jIxvp8jisC3H6wj7U8eEo6i24rTWtJyk1/W3/Zy5D01w3ISyJZXG4srMiVUrrFUozt9ucZwU5Jbkk4rw/H4AxmT6+5B5VkHhUU0VWV0TdWT3k7Pro40ujdepQ09/CfyvD8kzi/XuXyl2DRh8TjO/OrpvojPOaUa7YXzXu6rbhNKhpxSl5fz4ZrlwfExpxalxeCqsRdceCx4apW1LUFr7fMYvx+Yp/g6YvFcfiWWWYuBiU2WWu+cq6YxcrGmnNtLzJptb+dNgY7n/AFnn4XG8LyuBgVXUZPF5HJX4tl/RqMKq7Eoz6vyuzXwk/wC0Rv8A2bC3k8zCwuM+plVXKyqULZpWKGRCmxPdfzFz+Id9uLS863vZ8fhWUwqniY8qoVOmMHVFxjW0k4Ja8RaSTXx4IVnpvg7JZUrOG42UsqPXIcsWDdy8eJ+PuX2x+f8Aiv6Ap8n1i/8ABcPyGDj4dz5DHnkr3suVNcYxr7ySlKvbl/ScYvSbetNEH/z+c8KzMq47HWO7441Mbc1V3e47qqt219P24bt22nJpaevuRr8rieOzMKnDy+PxL8SlxdVFtMZQg4/xcYtaWvxr4PLOH4yzIy77OOw535dfs5FkqIuV0Na6zetyWvw/AGYfrpVWX0ZGJjLKpsrrcKcxWRl393zF9VtL2n+N/O9NMrrf1GzaMKORkcHUlLFwL+teZKbUsu2VdcfFW9Lrttf3pJmzj6f4aNuNbHiOPVmNV7NE1jQ3VX/wi9fbH/S8HeziuPsotoswcSVNtUaLK5UxcZ1x31g1rzFbel8LbAwMf1DzM+zj4YnHV4XfOxMXI+ov/c/cskpKEOv3R1B6k3F+X9q6s+lFd/g+J97Fu/xeD7uJBV48/p4dqYJpqMHr7Umk0l/RYgAAAAAAAAZB+t8bEtxKuUonU8zIuqosi4Rr6wulXHcpyXabUe3WKb0/h/moX6wenZYdd8ac9ysh7ldSjX2sh7fudk+/V+PHXfbbXjyaePpHiZXVW5VMsmdORPJq92b1GUrXak4rSkozblHsn1emvJzXoj08sP6WPH9aVPvFRusTh9rj1jJS3GHVtdE1HTa1oCv9U+tLOIzK6MTjbMqNnF3clC3sl/CdUVDq2nt+7/rT1/trzJ/UXisXkMbAysXOpzLZyjOmSrcqdWqvctTe03Lf2dnrbevg0PI8BxnI5FF+ZiRstoqnRXJScdVz6uUfDW03CL0/zFHLK9NcRk5tWZdhp5NVjtU4zlFttqTUtP7luMX1e1tJ6Ag8j6pXHc3l4mVgZMsOlY+8qro4Rla5RipJyUv5JLwn/JbIS/UHD+grzJ8Ty0MedNNzk4VNx97ftQaVm+0tLwt67x2151d5vpriM3lo8nlYaszVFR795JSUd9dxT6vXaWm09bbWjyPpjho5uHlxwYK/EqhTS1KWlGCagnHepddvTkm1t6+QKB/qXw08e/IxMbkcvHpsnCVtNUevWE6oOzbktQ3cn2elqE3+PP4zP1P4TFVzlTmWxpxvqZOn2rFr6f6jqtT8v29fcvsbaSl5Lqr0X6fpxXj4/Gwoq9irHXsWTrlGFUnKtRlFpxalJvaaf+xZ6M9P2RlGXHR9uWN9JKtWTUJV9Pb8xT05dPt7NdteNgduS9QfQxw4PjM+/Nya7Lo4dPtO2MYJdm9zUfDlFaUntyWvyV93rbErscY8byVilbOmmUY1avnDJhjyUdzWtWWR/l18JtbLnnOD47naa6uUxvfhW246nKD8rTW4tNxa8OL8NfKZwr9L8NXnXZkMGCyLZqxvvLSkrI2bjHeo7nCMn1S7NJvYFNd+ofGY98KsvEzsaTpuul73tR17Xu+5BLvucoumW1DtrcX8PZH5n9RMbiMuC5HBysSmCnXdXb0dqt7YyrjHrNw1L6qPly0tedaZf2+lOEuzVlXYMZ2r3H1lZNwbm5uTcN9W37tnlraUml4OUfRnARxXjvj4zramm7LZzn9zg2+zk5bXtV6e9x6R1rSAg5HrD6n0/wAZyfD4ltizeQhg9blFOr91wnJrsk9dXrUmntNbR+sT1xi5VmBXTxfJu3OdLx4tVffXbXbZGzfuaUetFm09S8Lx5LunhOPpwcXDrx19PjWq6qMpyk1Ym5dnJvbe22229t+SNxvpfhuNuhbhYUa7IWK2Eu8pdGoTglHbeoqNk0or7V2ekgIHMes8fjOajxn+M5LKvlbCiM6FV0lZKqdqj91kX/CuT3rRXX/qbw9WEsz6TkpYk9xptjXDV1ioV7rinPfbo35aUdxa3/epyOE47IzoZl2LGeTC1Xxm29qarlWn8/8ACcl/2V0fRPp2M5SjxdS3UqevaXXqoxgmlvSl1jGPb+TSSb0BH4f1zxvK+o7eEppy682qU6591BxjZCMZSg+sm0131vWm09N+NxsX17iWZ88J4WdbOFypldCuuNalO26uuPmze3KlreteU31TetDRwXHUcxdylOMoZ1y++anLq20k5dd9VJqMU5JbaSWzjV6a4eq2dteDCNk7YXSfaXmcLJWRfz+Jzm/+wMvxP6kUT9P4/Icrx+ZW5UwndOqEPbrsnQ8iNS/ccm/b6+dabkt9W2lOn69ohl42N/hOXd+RKuEYJUeJTrnZGLfu630rk/6+POyyfoz0+5Vt8bX+3jrFjHvPr7arda3HenJQbipP7km1smvgeMllVZLxIe/VOFkJbfiUa5Vxfz+Izkv+wMjn/qjxteJmz4/EvysijDWZCv3al3i41T6vU3KD62xf3RXw9b8bn/8AnuFVlZWPbjZlk8W3rkSrqgljQlc6oynuzyu0ZeY78Rb0vG5tfob05X7/AF4yCjfQ8acPdm4+24Rg0o9tJuMIJtab6rb8HefpDgp30XT4+MrabHbGUrJvtLsp7n5+/wC5KSUtpPyvIFNZ+oGO4Uzhx2bTCV2HF/UKC9yrJvePCcOs5fE9Np6evx5NuUeR6W4m54n/AMZRjjSplGKb1JUzdlae/wARsamv9pfjwW2JjVYlCpx4uNabkk5OXltt+X5+WwOwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//Z"}, {"timing": 1125, "timestamp": 356277463231, "data": "data:image/jpeg;base64,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"}, {"timing": 1500, "timestamp": 356277838231, "data": "data:image/jpeg;base64,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"}, {"timing": 1875, "timestamp": 356278213231, "data": "data:image/jpeg;base64,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"}, {"timing": 2250, "timestamp": 356278588231, "data": "data:image/jpeg;base64,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"}, {"timing": 2625, "timestamp": 356278963231, "data": "data:image/jpeg;base64,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"}, {"timing": 3000, "timestamp": 356279338231, "data": "data:image/jpeg;base64,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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 637, "timestamp": 356276975388, "data": "data:image/jpeg;base64,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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "0 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 45, "numericUnit": "millisecond", "displayValue": "50 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "unitless", "displayValue": "0", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0, "newEngineResult": {"cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0}, "newEngineResultDiffered": false}]}}, "errors-in-console": {"id": "errors-in-console", "title": "Browser errors were logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "sourceLocation", "valueType": "source-location", "label": "Source"}, {"key": "description", "valueType": "code", "label": "Description"}], "items": [{"source": "console.error", "description": "ChunkLoadError: Loading chunk 61 failed.\n(error: http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/page-548c5342d6ca6bb7.js)\n    at d.f.j (http://localhost:3000/_next/static/chunks/webpack-db7a9e385aa403d2.js:1:2924)\n    at http://localhost:3000/_next/static/chunks/webpack-db7a9e385aa403d2.js:1:1385\n    at Array.reduce (<anonymous>)\n    at d.e (http://localhost:3000/_next/static/chunks/webpack-db7a9e385aa403d2.js:1:1351)\n    at http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js:1:200319\n    at http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js:1:200521\n    at t (http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js:1:200724)", "sourceLocation": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 6733}}, {"source": "network", "description": "Failed to load resource: the server responded with a status of 400 (Bad Request)", "sourceLocation": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/page-548c5342d6ca6bb7.js", "urlProvider": "network", "line": 0, "column": 0}}, {"source": "network", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "sourceLocation": {"type": "source-location", "url": "http://localhost:3000/favicon.ico", "urlProvider": "network", "line": 0, "column": 0}}, {"source": "security", "description": "Refused to execute script from 'http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/page-548c5342d6ca6bb7.js' because its MIME type ('text/html') is not executable, and strict MIME type checking is enabled.", "sourceLocation": {"type": "source-location", "url": "http://localhost:3000/", "urlProvider": "network", "line": 0, "column": 0}}, {"source": "security", "description": "Refused to execute script from 'http://localhost:3000/_next/static/css/275ed64cc4367444.css' because its MIME type ('text/css') is not executable, and strict MIME type checking is enabled.", "sourceLocation": {"type": "source-location", "url": "http://localhost:3000/", "urlProvider": "network", "line": 0, "column": 0}}]}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 1.396, "numericUnit": "millisecond", "displayValue": "Root document took 0 ms", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:3000/", "responseTime": 1.396}], "overallSavingsMs": 0}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.96, "scoreDisplayMode": "numeric", "numericValue": 3014.9344, "numericUnit": "millisecond", "displayValue": "3.0 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": null, "scoreDisplayMode": "notApplicable", "displayValue": "", "details": {"type": "criticalrequestchain", "chains": {"002A744ADDF4E8CF5688BD5E2DB526CA": {"request": {"url": "http://localhost:3000/", "startTime": 356276.339224, "endTime": 356276.341769, "responseReceivedTime": 356276.34084099997, "transferSize": 1868}}}, "longestChain": {"duration": 2.5450000166893005, "length": 1, "transferSize": 1868}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 232.50400000000033, "numericUnit": "millisecond", "displayValue": "0.2 s", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 107.91600000000037}, {"group": "other", "groupLabel": "Other", "duration": 60.69199999999996}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 46.73599999999999}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 13.08}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 2.24}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 1.84}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 108.24800000000027, "numericUnit": "millisecond", "displayValue": "0.1 s", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js", "total": 105.45600000000027, "scripting": 87.25200000000028, "scriptParseCompile": 14.963999999999999}, {"url": "Unattributable", "total": 57.304, "scripting": 6.032, "scriptParseCompile": 0}], "summary": {"wastedMs": 108.24800000000027}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": ["Lighthouse was unable to automatically check the `font-display` value for the origin http://localhost:3000."], "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 22, "numScripts": 16, "numStylesheets": 2, "numFonts": 1, "numTasks": 366, "numTasksOver10ms": 1, "numTasksOver25ms": 0, "numTasksOver50ms": 0, "numTasksOver100ms": 0, "numTasksOver500ms": 0, "rtt": 0.0189, "throughput": 130447270.65483156, "maxRtt": 0.0189, "maxServerLatency": 3.7336, "totalByteWeight": 257748, "totalTaskTime": 58.126, "mainDocumentTransferSize": 1868}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "http://localhost:3000/", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 0, "networkRequestTime": 0.48499995470046997, "networkEndTime": 3.0299999713897705, "finished": true, "transferSize": 1868, "resourceSize": 3583, "statusCode": 307, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/webpack-db7a9e385aa403d2.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.518999993801117, "networkRequestTime": 7.180999994277954, "networkEndTime": 10.888999998569489, "finished": true, "transferSize": 2262, "resourceSize": 3567, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.667999982833862, "networkRequestTime": 7.342999994754791, "networkEndTime": 10.765999972820282, "finished": true, "transferSize": 2895, "resourceSize": 9123, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-fc2d4738-02f2d2f8b334b4bd.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.700999975204468, "networkRequestTime": 8.15499997138977, "networkEndTime": 11.486000001430511, "finished": true, "transferSize": 5280, "resourceSize": 15605, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-362d063c-e29bb3d8a73b2f00.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.741999983787537, "networkRequestTime": 8.317999958992004, "networkEndTime": 16.129999935626984, "finished": true, "transferSize": 16208, "resourceSize": 65826, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-05e245ef-5a7c97a617d0fd1d.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.773999929428101, "networkRequestTime": 8.433999955654144, "networkEndTime": 13.690999984741211, "finished": true, "transferSize": 4379, "resourceSize": 9226, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-6808aa01-7242b38f53a11ca4.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.7989999651908875, "networkRequestTime": 8.548999965190887, "networkEndTime": 14.575999975204468, "finished": true, "transferSize": 7063, "resourceSize": 23341, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.827999949455261, "networkRequestTime": 10.932999968528748, "networkEndTime": 27.242999970912933, "finished": true, "transferSize": 66156, "resourceSize": 213786, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-0fbe0e3f-c41127693f4cbef0.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.852999985218048, "networkRequestTime": 10.99399995803833, "networkEndTime": 16.9889999628067, "finished": true, "transferSize": 3515, "resourceSize": 10214, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-2ac4632b-df92205087a65995.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.888999938964844, "networkRequestTime": 11.537999987602234, "networkEndTime": 21.858999967575073, "finished": true, "transferSize": 21177, "resourceSize": 72811, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/framework-89d5c698-14f0d132976395df.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.918999969959259, "networkRequestTime": 13.80699998140335, "networkEndTime": 26.601000010967255, "finished": true, "transferSize": 44364, "resourceSize": 136435, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/css/275ed64cc4367444.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.942999958992004, "networkRequestTime": 14.629999995231628, "networkEndTime": 18.91299992799759, "finished": true, "transferSize": 1235, "resourceSize": 2153, "statusCode": 200, "mimeType": "text/css", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/vendors-5375ce5e1cf5482c.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.967999994754791, "networkRequestTime": 16.19899994134903, "networkEndTime": 22.844999969005585, "finished": true, "transferSize": 14827, "resourceSize": 48453, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/main-app-2875713947ff2908.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 7.055999934673309, "networkRequestTime": 17.063999950885773, "networkEndTime": 19.551999986171722, "finished": true, "transferSize": 1040, "resourceSize": 497, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/media/e4af272ccee01ff0-s.p.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 39.65499997138977, "networkRequestTime": 39.97999995946884, "networkEndTime": 41.8989999294281, "finished": true, "transferSize": 48928, "resourceSize": 48432, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/css/275ed64cc4367444.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 39.808999955654144, "networkRequestTime": 40.12799996137619, "networkEndTime": 40.4990000128746, "finished": true, "transferSize": 0, "resourceSize": 2153, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/app/page-0ad3be519ce526ad.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 40.72900003194809, "networkRequestTime": 40.908999979496, "networkEndTime": 42.75199997425079, "finished": true, "transferSize": 912, "resourceSize": 369, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/app/not-found-8192f24c052ee8ed.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 40.91899996995926, "networkRequestTime": 41.12699997425079, "networkEndTime": 42.42099994421005, "finished": true, "transferSize": 1014, "resourceSize": 471, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/en?_rsc=1iwkq", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 50.51300001144409, "networkRequestTime": 51.01800000667572, "networkEndTime": 61.9099999666214, "finished": true, "transferSize": 3534, "resourceSize": 17544, "statusCode": 200, "mimeType": "text/x-component", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/favicon.ico", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 53.526999950408936, "networkRequestTime": 53.79100000858307, "networkEndTime": 68.1609999537468, "finished": true, "transferSize": 4288, "resourceSize": 11696, "statusCode": 404, "mimeType": "text/html", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/css/6a2a11d6bf3a8157.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 60.621999979019165, "networkRequestTime": 61.13499999046326, "networkEndTime": 67.62099999189377, "finished": true, "transferSize": 6285, "resourceSize": 26873, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/page-548c5342d6ca6bb7.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 61.230000019073486, "networkRequestTime": 61.621999979019165, "networkEndTime": 67.216000020504, "finished": true, "transferSize": 518, "resourceSize": 0, "statusCode": 400, "mimeType": "text/html", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 356276338739}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 0.0189, "numericUnit": "millisecond", "displayValue": "0 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:3000", "rtt": 0.0189}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 3.7336, "numericUnit": "millisecond", "displayValue": "0 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:3000", "serverResponseTime": 3.7336}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 11.225, "startTime": 30.839}, {"duration": 5.048, "startTime": 44.507}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 3015, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 1059, "largestContentfulPaint": 3015, "interactive": 3015, "speedIndex": 1059, "totalBlockingTime": 0, "maxPotentialFID": 45, "cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0, "timeToFirstByte": 454, "observedTimeOrigin": 0, "observedTimeOriginTs": 356276338231, "observedNavigationStart": 0, "observedNavigationStartTs": 356276338231, "observedFirstPaint": 90, "observedFirstPaintTs": 356276428589, "observedFirstContentfulPaint": 90, "observedFirstContentfulPaintTs": 356276428589, "observedFirstContentfulPaintAllFrames": 90, "observedFirstContentfulPaintAllFramesTs": 356276428589, "observedLargestContentfulPaint": 90, "observedLargestContentfulPaintTs": 356276428589, "observedLargestContentfulPaintAllFrames": 90, "observedLargestContentfulPaintAllFramesTs": 356276428589, "observedTraceEnd": 2409, "observedTraceEndTs": 356278746837, "observedLoad": 50, "observedLoadTs": 356276387897, "observedDomContentLoaded": 9, "observedDomContentLoadedTs": 356276347144, "observedCumulativeLayoutShift": 0, "observedCumulativeLayoutShiftMainFrame": 0, "observedFirstVisualChange": 18, "observedFirstVisualChangeTs": 356276356231, "observedLastVisualChange": 81, "observedLastVisualChangeTs": 356276419231, "observedSpeedIndex": 81, "observedSpeedIndexTs": 356276419063}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 21, "transferSize": 253460}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 16, "transferSize": 192845}, {"resourceType": "font", "label": "Font", "requestCount": 1, "transferSize": 48928}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 2, "transferSize": 6285}, {"resourceType": "other", "label": "Other", "requestCount": 1, "transferSize": 3534}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 1868}, {"resourceType": "image", "label": "Image", "requestCount": 0, "transferSize": 0}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 0, "transferSize": 0}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "3,010 ms", "metricSavings": {"LCP": 500}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-H2", "path": "1,H<PERSON><PERSON>,1,BODY,5,DIV,0,DIV,0,H2", "selector": "body > div > div > h2", "boundingRect": {"top": 392, "bottom": 448, "left": 16, "right": 396, "width": 380, "height": 56}, "snippet": "<h2 style=\"font-size: 14px; font-weight: 400; line-height: 28px; margin: 0px 8px;\">", "nodeLabel": "Application error: a client-side exception has occurred (see the browser consol…"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 453.7336, "percent": "15%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 2561.2008, "percent": "85%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [], "items": [], "debugData": {"type": "debugdata", "urls": [], "tasks": []}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Page has valid source maps", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No CSP found in enforcement mode"}]}}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No HSTS header found"}]}}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"description": "No COOP header found", "severity": "High"}]}}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "http://localhost:3000/", "resourceBytes": 1980, "encodedBytes": 710, "children": [{"name": "(inline) (self.__next_f=…", "resourceBytes": 72, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 200, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 1468, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 240, "unusedBytes": 0}]}, {"name": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "resourceBytes": 9123, "encodedBytes": 2320, "unusedBytes": 4132}, {"name": "http://localhost:3000/_next/static/chunks/webpack-db7a9e385aa403d2.js", "resourceBytes": 3567, "encodedBytes": 1688, "unusedBytes": 166}, {"name": "http://localhost:3000/_next/static/chunks/framework-fc2d4738-02f2d2f8b334b4bd.js", "resourceBytes": 15605, "encodedBytes": 4705, "unusedBytes": 2891}, {"name": "http://localhost:3000/_next/static/chunks/framework-05e245ef-5a7c97a617d0fd1d.js", "resourceBytes": 9226, "encodedBytes": 3804, "unusedBytes": 9141}, {"name": "http://localhost:3000/_next/static/chunks/framework-6808aa01-7242b38f53a11ca4.js", "resourceBytes": 23341, "encodedBytes": 6488, "unusedBytes": 21838}, {"name": "http://localhost:3000/_next/static/chunks/framework-362d063c-e29bb3d8a73b2f00.js", "resourceBytes": 65826, "encodedBytes": 15632, "unusedBytes": 21097}, {"name": "http://localhost:3000/_next/static/chunks/framework-0fbe0e3f-c41127693f4cbef0.js", "resourceBytes": 10214, "encodedBytes": 2940, "unusedBytes": 8199}, {"name": "http://localhost:3000/_next/static/chunks/main-app-2875713947ff2908.js", "resourceBytes": 497, "encodedBytes": 497, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/framework-2ac4632b-df92205087a65995.js", "resourceBytes": 72811, "encodedBytes": 20601, "unusedBytes": 64335}, {"name": "http://localhost:3000/_next/static/chunks/vendors-5375ce5e1cf5482c.js", "resourceBytes": 48453, "encodedBytes": 14252, "unusedBytes": 47159}, {"name": "http://localhost:3000/_next/static/chunks/framework-89d5c698-14f0d132976395df.js", "resourceBytes": 136435, "encodedBytes": 43788, "unusedBytes": 136283}, {"name": "http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js", "resourceBytes": 213786, "encodedBytes": 65580, "unusedBytes": 84758}, {"name": "http://localhost:3000/_next/static/chunks/app/not-found-8192f24c052ee8ed.js", "resourceBytes": 471, "encodedBytes": 471, "unusedBytes": 139}, {"name": "http://localhost:3000/_next/static/chunks/app/page-0ad3be519ce526ad.js", "resourceBytes": 369, "encodedBytes": 369, "unusedBytes": 0}]}}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "button-name": {"id": "button-name", "title": "Buttons have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "notApplicable"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors have a sufficient contrast ratio", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "notApplicable"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "document-title": {"id": "document-title", "title": "Document doesn't have a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-HTML", "path": "1,HTML", "selector": "html#__next_error__", "boundingRect": {"top": 0, "bottom": 839, "left": 0, "right": 412, "width": 412, "height": 839}, "snippet": "<html id=\"__next_error__\">", "nodeLabel": "html#__next_error__", "explanation": "Fix any of the following:\n  Document does not have a non-empty <title> element"}}], "debugData": {"type": "debugdata", "impact": "serious", "tags": ["cat.text-alternatives", "wcag2a", "wcag242", "TTv5", "TT12.a", "EN-301-549", "EN-*******", "ACT"]}}}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "notApplicable"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "notApplicable"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "notApplicable"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "notApplicable"}, "heading-order": {"id": "heading-order", "title": "Heading elements appear in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element does not have a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-HTML", "path": "1,HTML", "selector": "html#__next_error__", "boundingRect": {"top": 0, "bottom": 839, "left": 0, "right": 412, "width": 412, "height": 839}, "snippet": "<html id=\"__next_error__\">", "nodeLabel": "html#__next_error__", "explanation": "Fix any of the following:\n  The <html> element does not have a lang attribute"}}], "debugData": {"type": "debugdata", "impact": "serious", "tags": ["cat.language", "wcag2a", "wcag311", "TTv5", "TT11.a", "EN-301-549", "EN-*******", "ACT"]}}}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": null, "scoreDisplayMode": "notApplicable"}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": null, "scoreDisplayMode": "notApplicable"}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-HTML", "path": "1,HTML", "selector": "html#__next_error__", "boundingRect": {"top": 0, "bottom": 839, "left": 0, "right": 412, "width": 412, "height": 839}, "snippet": "<html id=\"__next_error__\">", "nodeLabel": "html#__next_error__", "explanation": "Fix all of the following:\n  Document does not have a main landmark"}}], "debugData": {"type": "debugdata", "impact": "moderate", "tags": ["cat.semantics", "best-practice"]}}}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "notApplicable"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": null, "scoreDisplayMode": "notApplicable"}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": null, "scoreDisplayMode": "notApplicable"}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "notApplicable"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "target-size": {"id": "target-size", "title": "Touch targets have sufficient size and spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "notApplicable"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "notApplicable"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "manual"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "manual"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "manual"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "manual"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "manual"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "manual"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "manual"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "manual"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "manual"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "manual"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Uses efficient cache policy on static assets", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "byte", "displayValue": "0 resources found", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 257748, "numericUnit": "byte", "displayValue": "Total size was 252 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js", "totalBytes": 66156}, {"url": "http://localhost:3000/_next/static/media/e4af272ccee01ff0-s.p.woff2", "totalBytes": 48928}, {"url": "http://localhost:3000/_next/static/chunks/framework-89d5c698-14f0d132976395df.js", "totalBytes": 44364}, {"url": "http://localhost:3000/_next/static/chunks/framework-2ac4632b-df92205087a65995.js", "totalBytes": 21177}, {"url": "http://localhost:3000/_next/static/chunks/framework-362d063c-e29bb3d8a73b2f00.js", "totalBytes": 16208}, {"url": "http://localhost:3000/_next/static/chunks/vendors-5375ce5e1cf5482c.js", "totalBytes": 14827}, {"url": "http://localhost:3000/_next/static/chunks/framework-6808aa01-7242b38f53a11ca4.js", "totalBytes": 7063}, {"url": "http://localhost:3000/_next/static/css/6a2a11d6bf3a8157.css", "totalBytes": 6285}, {"url": "http://localhost:3000/_next/static/chunks/framework-fc2d4738-02f2d2f8b334b4bd.js", "totalBytes": 5280}, {"url": "http://localhost:3000/_next/static/chunks/framework-05e245ef-5a7c97a617d0fd1d.js", "totalBytes": 4379}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 450, "numericUnit": "millisecond", "displayValue": "Est savings of 68 KiB", "metricSavings": {"FCP": 0, "LCP": 450}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/framework-89d5c698-14f0d132976395df.js", "totalBytes": 43788, "wastedBytes": 43739, "wastedPercent": 99.88859163704328}, {"url": "http://localhost:3000/_next/static/chunks/framework-cfb98476-9eeb771454ce87ba.js", "totalBytes": 65580, "wastedBytes": 26000, "wastedPercent": 39.646188244319085}], "overallSavingsMs": 450, "overallSavingsBytes": 69739, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 450}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 11 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "wastedBytes": 11135, "subItems": {"type": "subitems", "items": [{"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 1271}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 659}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 772}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 1148}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 1406}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 401}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 316}}]}, "totalBytes": 0}], "overallSavingsMs": 0, "overallSavingsBytes": 11135, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": 1, "scoreDisplayMode": "binary"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": 1, "scoreDisplayMode": "binary"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 8, "numericUnit": "element", "displayValue": "8 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 8}}, {"node": {"type": "node", "lhId": "1-1-H2", "path": "1,H<PERSON><PERSON>,1,BODY,5,DIV,0,DIV,0,H2", "selector": "body > div > div > h2", "boundingRect": {"top": 392, "bottom": 448, "left": 16, "right": 396, "width": 380, "height": 56}, "snippet": "<h2 style=\"font-size: 14px; font-weight: 400; line-height: 28px; margin: 0px 8px;\">", "nodeLabel": "Application error: a client-side exception has occurred (see the browser consol…"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 4}}, {"node": {"type": "node", "lhId": "1-2-<PERSON>ODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body", "boundingRect": {"top": 8, "bottom": 831, "left": 8, "right": 404, "width": 396, "height": 823}, "snippet": "<body>", "nodeLabel": "body"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 6}}]}, "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "notApplicable"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document does not have a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": 0, "scoreDisplayMode": "binary"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": 1, "scoreDisplayMode": "binary"}, "font-size": {"id": "font-size", "title": "Document doesn't use legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": 0, "scoreDisplayMode": "binary", "explanation": "Text is illegible because there's no viewport meta tag optimized for mobile screens."}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": 1, "scoreDisplayMode": "binary", "warnings": [], "details": {"type": "table", "headings": [], "items": []}}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": null, "scoreDisplayMode": "notApplicable"}, "hreflang": {"id": "hreflang", "title": "Document has a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "canonical": {"id": "canonical", "title": "Document has a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": null, "scoreDisplayMode": "notApplicable"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "manual"}, "bf-cache": {"id": "bf-cache", "title": "Page prevented back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 0, "scoreDisplayMode": "binary", "displayValue": "1 failure reason", "details": {"type": "table", "headings": [{"key": "reason", "valueType": "text", "subItemsHeading": {"key": "frameUrl", "valueType": "url"}, "label": "Failure reason"}, {"key": "failureType", "valueType": "text", "label": "Failure type"}], "items": [{"reason": "Only pages with a status code of 2XX can be cached.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/"}]}, "protocolReason": "HTTPStatusNotOK"}]}, "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": []}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Avoids redirects", "value": true}, "serverResponseIsFast": {"label": "Server responds quickly (observed 1 ms) ", "value": true}, "usesCompression": {"label": "Applies text compression", "value": true}}, "debugData": {"type": "debugdata", "redirectDuration": 0, "serverResponseTime": 1, "uncompressedResponseBytes": 0, "wastedBytes": 0}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 10}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-10-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body", "boundingRect": {"top": 8, "bottom": 831, "left": 8, "right": 404, "width": 396, "height": 823}, "snippet": "<body>", "nodeLabel": "body"}, "value": {"type": "numeric", "granularity": 1, "value": 6}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-0-H2", "path": "1,H<PERSON><PERSON>,1,BODY,5,DIV,0,DIV,0,H2", "selector": "body > div > div > h2", "boundingRect": {"top": 392, "bottom": 448, "left": 16, "right": 396, "width": 380, "height": 56}, "snippet": "<h2 style=\"font-size: 14px; font-weight: 400; line-height: 28px; margin: 0px 8px;\">", "nodeLabel": "Application error: a client-side exception has occurred (see the browser consol…"}, "value": {"type": "numeric", "granularity": 1, "value": 4}}], "debugData": {"type": "debugdata", "totalElements": 10, "maxChildren": 6, "maxDepth": 4}}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.", "score": 1, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [], "items": []}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "INP by phase", "description": "Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "LCP by phase", "description": "Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Phase"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"phase": "timeToFirstByte", "label": "Time to first byte", "duration": 2.544}, {"phase": "elementRenderDelay", "label": "Element render delay", "duration": 87.81400000000001}]}, {"type": "node", "lhId": "page-0-H2", "path": "1,H<PERSON><PERSON>,1,BODY,5,DIV,0,DIV,0,H2", "selector": "body > div > div > h2", "boundingRect": {"top": 392, "bottom": 448, "left": 16, "right": 396, "width": 380, "height": 56}, "snippet": "<h2 style=\"font-size: 14px; font-weight: 400; line-height: 28px; margin: 0px 8px;\">", "nodeLabel": "Application error: a client-side exception has occurred (see the browser consol…"}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable older browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support older browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0.5, "scoreDisplayMode": "metricSavings", "displayValue": "Est savings of 14 KiB", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Wasted bytes"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "wastedBytes": 13894, "subItems": {"type": "subitems", "items": [{"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 1271}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 659}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 772}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 1148}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 1406}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 401}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/framework-27f02048-fd99759d63527373.js", "urlProvider": "network", "line": 0, "column": 316}}]}}], "debugData": {"type": "debugdata", "wastedBytes": 13894}}, "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "network-tree", "chains": {"002A744ADDF4E8CF5688BD5E2DB526CA": {"url": "http://localhost:3000/", "navStartToEndTime": 8, "transferSize": 1868, "isLongest": true, "children": {}}}, "longestChain": {"duration": 8}}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) can move these network requests out of the critical path.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) to prioritize your page's content.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-9-META", "path": "", "selector": "meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">", "nodeLabel": "meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": null, "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.94}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 0, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 0, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 0, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 0, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 0, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 0, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 0, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 0, "group": "a11y-aria"}, {"id": "aria-text", "weight": 0, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 0, "group": "a11y-aria"}, {"id": "button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 0, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 0, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 0, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 0, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 0, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 0, "group": "a11y-language"}, {"id": "image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "label", "weight": 0, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 0, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 0, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 0, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 0, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 0, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 0, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 0, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 0, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 0, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 0, "group": "a11y-language"}, {"id": "video-caption", "weight": 0, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": 0.59}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": 0.89}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 0, "group": "seo-crawl"}, {"id": "image-alt", "weight": 0, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 0, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": 0.8}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "localhost", "origins": ["http://localhost:3000"], "isFirstParty": true, "isUnrecognized": true}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 412, "height": 839}, "nodes": {"page-0-H2": {"id": "", "top": 400, "bottom": 456, "left": 16, "right": 396, "width": 380, "height": 56}, "page-1-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-2-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-3-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-4-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-5-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-6-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-7-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-8-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-9-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-10-BODY": {"id": "", "top": 8, "bottom": 847, "left": 8, "right": 404, "width": 396, "height": 839}, "1-0-HTML": {"id": "__next_error__", "top": 0, "bottom": 855, "left": 0, "right": 412, "width": 412, "height": 855}, "1-1-H2": {"id": "", "top": 400, "bottom": 456, "left": 16, "right": 396, "width": 380, "height": 56}, "1-2-BODY": {"id": "", "top": 8, "bottom": 847, "left": 8, "right": 404, "width": 396, "height": 839}, "1-3-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 917.46, "name": "lh:config", "duration": 382.96, "entryType": "measure"}, {"startTime": 918.68, "name": "lh:config:resolveArtifactsToDefns", "duration": 76.92, "entryType": "measure"}, {"startTime": 1300.53, "name": "lh:runner:gather", "duration": 5460.99, "entryType": "measure"}, {"startTime": 1400.17, "name": "lh:driver:connect", "duration": 3.88, "entryType": "measure"}, {"startTime": 1404.21, "name": "lh:driver:navigate", "duration": 44.01, "entryType": "measure"}, {"startTime": 1448.57, "name": "lh:gather:getBenchmarkIndex", "duration": 1002.78, "entryType": "measure"}, {"startTime": 2451.53, "name": "lh:gather:getVersion", "duration": 0.55, "entryType": "measure"}, {"startTime": 2452.21, "name": "lh:prepare:navigationMode", "duration": 52.19, "entryType": "measure"}, {"startTime": 2463.59, "name": "lh:storage:clearDataForOrigin", "duration": 26.24, "entryType": "measure"}, {"startTime": 2489.95, "name": "lh:storage:clearBrowserCaches", "duration": 13.13, "entryType": "measure"}, {"startTime": 2503.58, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 0.81, "entryType": "measure"}, {"startTime": 2545.61, "name": "lh:driver:navigate", "duration": 2445.71, "entryType": "measure"}, {"startTime": 5107.49, "name": "lh:computed:NetworkRecords", "duration": 0.69, "entryType": "measure"}, {"startTime": 5108.44, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.04, "entryType": "measure"}, {"startTime": 5108.49, "name": "lh:gather:getArtifact:Trace", "duration": 0.02, "entryType": "measure"}, {"startTime": 5108.51, "name": "lh:gather:getArtifact:Accessibility", "duration": 76.76, "entryType": "measure"}, {"startTime": 5185.3, "name": "lh:gather:getArtifact:AnchorElements", "duration": 3.67, "entryType": "measure"}, {"startTime": 5188.99, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.06, "entryType": "measure"}, {"startTime": 5189.06, "name": "lh:gather:getArtifact:CSSUsage", "duration": 4.5, "entryType": "measure"}, {"startTime": 5193.58, "name": "lh:gather:getArtifact:Doctype", "duration": 0.49, "entryType": "measure"}, {"startTime": 5194.07, "name": "lh:gather:getArtifact:DOMStats", "duration": 2.07, "entryType": "measure"}, {"startTime": 5196.18, "name": "lh:gather:getArtifact:FontSize", "duration": 2.55, "entryType": "measure"}, {"startTime": 5198.74, "name": "lh:gather:getArtifact:Inputs", "duration": 1.19, "entryType": "measure"}, {"startTime": 5199.94, "name": "lh:gather:getArtifact:ImageElements", "duration": 2.29, "entryType": "measure"}, {"startTime": 5202.28, "name": "lh:gather:getArtifact:InspectorIssues", "duration": 0.16, "entryType": "measure"}, {"startTime": 5202.45, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.04, "entryType": "measure"}, {"startTime": 5202.5, "name": "lh:gather:getArtifact:LinkElements", "duration": 1.5, "entryType": "measure"}, {"startTime": 5203.9, "name": "lh:computed:MainResource", "duration": 0.09, "entryType": "measure"}, {"startTime": 5204.02, "name": "lh:gather:getArtifact:MainDocumentContent", "duration": 0.83, "entryType": "measure"}, {"startTime": 5204.85, "name": "lh:gather:getArtifact:MetaElements", "duration": 1.15, "entryType": "measure"}, {"startTime": 5206.02, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.06, "entryType": "measure"}, {"startTime": 5206.16, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.16, "entryType": "measure"}, {"startTime": 5206.34, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 1.75, "entryType": "measure"}, {"startTime": 5208.1, "name": "lh:gather:getArtifact:RobotsTxt", "duration": 16.25, "entryType": "measure"}, {"startTime": 5224.37, "name": "lh:gather:getArtifact:Scripts", "duration": 0.09, "entryType": "measure"}, {"startTime": 5224.48, "name": "lh:gather:getArtifact:SourceMaps", "duration": 0.12, "entryType": "measure"}, {"startTime": 5224.61, "name": "lh:gather:getArtifact:Stacks", "duration": 5.35, "entryType": "measure"}, {"startTime": 5224.67, "name": "lh:gather:collectStacks", "duration": 5.28, "entryType": "measure"}, {"startTime": 5229.96, "name": "lh:gather:getArtifact:Stylesheets", "duration": 2.28, "entryType": "measure"}, {"startTime": 5232.27, "name": "lh:gather:getArtifact:TraceElements", "duration": 172.41, "entryType": "measure"}, {"startTime": 5232.44, "name": "lh:computed:TraceEngineResult", "duration": 149.84, "entryType": "measure"}, {"startTime": 5232.49, "name": "lh:computed:ProcessedTrace", "duration": 8.5, "entryType": "measure"}, {"startTime": 5241.57, "name": "lh:computed:TraceEngineResult:total", "duration": 136.78, "entryType": "measure"}, {"startTime": 5241.61, "name": "lh:computed:TraceEngineResult:parse", "duration": 90.13, "entryType": "measure"}, {"startTime": 5242.11, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 38.82, "entryType": "measure"}, {"startTime": 5280.96, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 1.43, "entryType": "measure"}, {"startTime": 5282.56, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 1.38, "entryType": "measure"}, {"startTime": 5283.97, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 1.28, "entryType": "measure"}, {"startTime": 5285.26, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 1.24, "entryType": "measure"}, {"startTime": 5286.51, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 1.24, "entryType": "measure"}, {"startTime": 5287.77, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 2.32, "entryType": "measure"}, {"startTime": 5290.12, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 5.29, "entryType": "measure"}, {"startTime": 5295.43, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 2.27, "entryType": "measure"}, {"startTime": 5297.71, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 0.53, "entryType": "measure"}, {"startTime": 5298.26, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 1.2, "entryType": "measure"}, {"startTime": 5299.48, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 1.21, "entryType": "measure"}, {"startTime": 5300.7, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 1.39, "entryType": "measure"}, {"startTime": 5302.1, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 1.34, "entryType": "measure"}, {"startTime": 5303.45, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 5.92, "entryType": "measure"}, {"startTime": 5309.39, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 0.9, "entryType": "measure"}, {"startTime": 5310.31, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 0.52, "entryType": "measure"}, {"startTime": 5310.84, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 1.29, "entryType": "measure"}, {"startTime": 5312.16, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 0.81, "entryType": "measure"}, {"startTime": 5312.98, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 1.89, "entryType": "measure"}, {"startTime": 5314.89, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 1.07, "entryType": "measure"}, {"startTime": 5315.97, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 0.94, "entryType": "measure"}, {"startTime": 5316.92, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 1.91, "entryType": "measure"}, {"startTime": 5318.84, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 2.28, "entryType": "measure"}, {"startTime": 5321.14, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 1.08, "entryType": "measure"}, {"startTime": 5322.24, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 3.26, "entryType": "measure"}, {"startTime": 5325.52, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 0.64, "entryType": "measure"}, {"startTime": 5326.17, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 0.66, "entryType": "measure"}, {"startTime": 5326.84, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 1.12, "entryType": "measure"}, {"startTime": 5327.98, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 1.45, "entryType": "measure"}, {"startTime": 5329.46, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 1.3, "entryType": "measure"}, {"startTime": 5330.78, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 0.94, "entryType": "measure"}, {"startTime": 5331.74, "name": "lh:computed:TraceEngineResult:insights", "duration": 46.61, "entryType": "measure"}, {"startTime": 5331.86, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 11.94, "entryType": "measure"}, {"startTime": 5343.9, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.33, "entryType": "measure"}, {"startTime": 5344.23, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 0.45, "entryType": "measure"}, {"startTime": 5344.69, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.17, "entryType": "measure"}, {"startTime": 5344.86, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.3, "entryType": "measure"}, {"startTime": 5345.17, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 0.94, "entryType": "measure"}, {"startTime": 5346.11, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.11, "entryType": "measure"}, {"startTime": 5346.39, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.11, "entryType": "measure"}, {"startTime": 5346.5, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.15, "entryType": "measure"}, {"startTime": 5346.66, "name": "lh:computed:TraceEngineResult:insights:InteractionToNextPaint", "duration": 0.06, "entryType": "measure"}, {"startTime": 5346.72, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.1, "entryType": "measure"}, {"startTime": 5346.82, "name": "lh:computed:TraceEngineResult:insights:LCPPhases", "duration": 0.13, "entryType": "measure"}, {"startTime": 5346.96, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 25.73, "entryType": "measure"}, {"startTime": 5372.7, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 1.16, "entryType": "measure"}, {"startTime": 5373.87, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 1.12, "entryType": "measure"}, {"startTime": 5374.99, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.14, "entryType": "measure"}, {"startTime": 5375.14, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.1, "entryType": "measure"}, {"startTime": 5375.24, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 2.7, "entryType": "measure"}, {"startTime": 5377.94, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.08, "entryType": "measure"}, {"startTime": 5383.84, "name": "lh:computed:ProcessedNavigation", "duration": 0.34, "entryType": "measure"}, {"startTime": 5384.21, "name": "lh:computed:CumulativeLayoutShift", "duration": 8.81, "entryType": "measure"}, {"startTime": 5393.32, "name": "lh:computed:Responsiveness", "duration": 0.08, "entryType": "measure"}, {"startTime": 5404.69, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.78, "entryType": "measure"}, {"startTime": 5405.49, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1063.68, "entryType": "measure"}, {"startTime": 6469.21, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 282.71, "entryType": "measure"}, {"startTime": 6761.73, "name": "lh:runner:audit", "duration": 388.13, "entryType": "measure"}, {"startTime": 6761.79, "name": "lh:runner:auditing", "duration": 387.79, "entryType": "measure"}, {"startTime": 6762.28, "name": "lh:audit:is-on-https", "duration": 1.31, "entryType": "measure"}, {"startTime": 6763.72, "name": "lh:audit:redirects-http", "duration": 0.42, "entryType": "measure"}, {"startTime": 6764.25, "name": "lh:audit:viewport", "duration": 0.58, "entryType": "measure"}, {"startTime": 6764.4, "name": "lh:computed:ViewportMeta", "duration": 0.12, "entryType": "measure"}, {"startTime": 6764.94, "name": "lh:audit:first-contentful-paint", "duration": 3.6, "entryType": "measure"}, {"startTime": 6765.11, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 2.03, "entryType": "measure"}, {"startTime": 6765.23, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 1.91, "entryType": "measure"}, {"startTime": 6765.28, "name": "lh:computed:PageDependencyGraph", "duration": 1.2, "entryType": "measure"}, {"startTime": 6766.49, "name": "lh:computed:LoadSimulator", "duration": 0.3, "entryType": "measure"}, {"startTime": 6766.51, "name": "lh:computed:NetworkAnalysis", "duration": 0.25, "entryType": "measure"}, {"startTime": 6768.63, "name": "lh:audit:largest-contentful-paint", "duration": 1.96, "entryType": "measure"}, {"startTime": 6768.79, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 1.31, "entryType": "measure"}, {"startTime": 6768.82, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 1.27, "entryType": "measure"}, {"startTime": 6770.8, "name": "lh:audit:first-meaningful-paint", "duration": 0.35, "entryType": "measure"}, {"startTime": 6771.24, "name": "lh:audit:speed-index", "duration": 99.2, "entryType": "measure"}, {"startTime": 6771.53, "name": "lh:computed:SpeedIndex", "duration": 98.4, "entryType": "measure"}, {"startTime": 6771.57, "name": "lh:computed:LanternSpeedIndex", "duration": 98.35, "entryType": "measure"}, {"startTime": 6771.63, "name": "lh:computed:Speedline", "duration": 96.94, "entryType": "measure"}, {"startTime": 6870.46, "name": "lh:audit:screenshot-thumbnails", "duration": 0.23, "entryType": "measure"}, {"startTime": 6870.7, "name": "lh:audit:final-screenshot", "duration": 0.54, "entryType": "measure"}, {"startTime": 6870.77, "name": "lh:computed:Screenshots", "duration": 0.46, "entryType": "measure"}, {"startTime": 6871.39, "name": "lh:audit:total-blocking-time", "duration": 2.63, "entryType": "measure"}, {"startTime": 6871.56, "name": "lh:computed:TotalBlockingTime", "duration": 1.98, "entryType": "measure"}, {"startTime": 6871.59, "name": "lh:computed:LanternTotalBlockingTime", "duration": 1.95, "entryType": "measure"}, {"startTime": 6871.62, "name": "lh:computed:LanternInteractive", "duration": 1, "entryType": "measure"}, {"startTime": 6874.14, "name": "lh:audit:max-potential-fid", "duration": 2.16, "entryType": "measure"}, {"startTime": 6874.34, "name": "lh:computed:MaxPotentialFID", "duration": 1.32, "entryType": "measure"}, {"startTime": 6874.37, "name": "lh:computed:LanternMaxPotentialFID", "duration": 1.29, "entryType": "measure"}, {"startTime": 6876.43, "name": "lh:audit:cumulative-layout-shift", "duration": 0.51, "entryType": "measure"}, {"startTime": 6877.08, "name": "lh:audit:errors-in-console", "duration": 1.83, "entryType": "measure"}, {"startTime": 6877.46, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.06, "entryType": "measure"}, {"startTime": 6879.06, "name": "lh:audit:server-response-time", "duration": 0.81, "entryType": "measure"}, {"startTime": 6880, "name": "lh:audit:interactive", "duration": 0.55, "entryType": "measure"}, {"startTime": 6880.16, "name": "lh:computed:Interactive", "duration": 0.03, "entryType": "measure"}, {"startTime": 6880.67, "name": "lh:audit:user-timings", "duration": 0.82, "entryType": "measure"}, {"startTime": 6880.82, "name": "lh:computed:UserTimings", "duration": 0.34, "entryType": "measure"}, {"startTime": 6881.61, "name": "lh:audit:critical-request-chains", "duration": 0.9, "entryType": "measure"}, {"startTime": 6881.79, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 0.32, "entryType": "measure"}, {"startTime": 6882.61, "name": "lh:audit:redirects", "duration": 0.76, "entryType": "measure"}, {"startTime": 6883.5, "name": "lh:audit:image-aspect-ratio", "duration": 0.5, "entryType": "measure"}, {"startTime": 6884.13, "name": "lh:audit:image-size-responsive", "duration": 0.74, "entryType": "measure"}, {"startTime": 6884.34, "name": "lh:computed:ImageRecords", "duration": 0.14, "entryType": "measure"}, {"startTime": 6885.01, "name": "lh:audit:deprecations", "duration": 0.52, "entryType": "measure"}, {"startTime": 6885.65, "name": "lh:audit:third-party-cookies", "duration": 0.52, "entryType": "measure"}, {"startTime": 6886.32, "name": "lh:audit:mainthread-work-breakdown", "duration": 3.84, "entryType": "measure"}, {"startTime": 6886.55, "name": "lh:computed:MainThreadTasks", "duration": 2.88, "entryType": "measure"}, {"startTime": 6890.32, "name": "lh:audit:bootup-time", "duration": 4.92, "entryType": "measure"}, {"startTime": 6891.07, "name": "lh:computed:TBTImpactTasks", "duration": 3.19, "entryType": "measure"}, {"startTime": 6895.35, "name": "lh:audit:uses-rel-preconnect", "duration": 4.78, "entryType": "measure"}, {"startTime": 6900.3, "name": "lh:audit:font-display", "duration": 1.35, "entryType": "measure"}, {"startTime": 6901.66, "name": "lh:audit:diagnostics", "duration": 0.33, "entryType": "measure"}, {"startTime": 6901.99, "name": "lh:audit:network-requests", "duration": 4.63, "entryType": "measure"}, {"startTime": 6902.09, "name": "lh:computed:EntityClassification", "duration": 1.01, "entryType": "measure"}, {"startTime": 6906.83, "name": "lh:audit:network-rtt", "duration": 0.79, "entryType": "measure"}, {"startTime": 6907.73, "name": "lh:audit:network-server-latency", "duration": 0.58, "entryType": "measure"}, {"startTime": 6908.32, "name": "lh:audit:main-thread-tasks", "duration": 0.2, "entryType": "measure"}, {"startTime": 6908.52, "name": "lh:audit:metrics", "duration": 1.54, "entryType": "measure"}, {"startTime": 6908.6, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 1.28, "entryType": "measure"}, {"startTime": 6908.75, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.13, "entryType": "measure"}, {"startTime": 6908.9, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.05, "entryType": "measure"}, {"startTime": 6908.98, "name": "lh:computed:LCPBreakdown", "duration": 0.53, "entryType": "measure"}, {"startTime": 6909.02, "name": "lh:computed:TimeToFirstByte", "duration": 0.08, "entryType": "measure"}, {"startTime": 6909.11, "name": "lh:computed:LCPImageRecord", "duration": 0.38, "entryType": "measure"}, {"startTime": 6910.08, "name": "lh:audit:resource-summary", "duration": 0.68, "entryType": "measure"}, {"startTime": 6910.19, "name": "lh:computed:ResourceSummary", "duration": 0.24, "entryType": "measure"}, {"startTime": 6911.04, "name": "lh:audit:third-party-summary", "duration": 1.83, "entryType": "measure"}, {"startTime": 6913.01, "name": "lh:audit:third-party-facades", "duration": 1.58, "entryType": "measure"}, {"startTime": 6914.69, "name": "lh:audit:largest-contentful-paint-element", "duration": 0.76, "entryType": "measure"}, {"startTime": 6915.56, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.42, "entryType": "measure"}, {"startTime": 6916.08, "name": "lh:audit:layout-shifts", "duration": 0.49, "entryType": "measure"}, {"startTime": 6916.65, "name": "lh:audit:long-tasks", "duration": 1.25, "entryType": "measure"}, {"startTime": 6918, "name": "lh:audit:non-composited-animations", "duration": 0.45, "entryType": "measure"}, {"startTime": 6918.57, "name": "lh:audit:unsized-images", "duration": 0.44, "entryType": "measure"}, {"startTime": 6919.13, "name": "lh:audit:valid-source-maps", "duration": 0.59, "entryType": "measure"}, {"startTime": 6919.81, "name": "lh:audit:prioritize-lcp-image", "duration": 0.34, "entryType": "measure"}, {"startTime": 6920.23, "name": "lh:audit:csp-xss", "duration": 0.65, "entryType": "measure"}, {"startTime": 6920.96, "name": "lh:audit:has-hsts", "duration": 0.54, "entryType": "measure"}, {"startTime": 6921.68, "name": "lh:audit:origin-isolation", "duration": 0.6, "entryType": "measure"}, {"startTime": 6922.37, "name": "lh:audit:clickjacking-mitigation", "duration": 0.57, "entryType": "measure"}, {"startTime": 6922.95, "name": "lh:audit:script-treemap-data", "duration": 10.28, "entryType": "measure"}, {"startTime": 6923.11, "name": "lh:computed:ModuleDuplication", "duration": 0.08, "entryType": "measure"}, {"startTime": 6923.2, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 6923.32, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 6923.41, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.04, "entryType": "measure"}, {"startTime": 6923.47, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 6923.51, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.22, "entryType": "measure"}, {"startTime": 6923.76, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.09, "entryType": "measure"}, {"startTime": 6923.88, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.31, "entryType": "measure"}, {"startTime": 6924.22, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.28, "entryType": "measure"}, {"startTime": 6924.53, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.61, "entryType": "measure"}, {"startTime": 6925.18, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.28, "entryType": "measure"}, {"startTime": 6926.49, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.35, "entryType": "measure"}, {"startTime": 6926.93, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 6927.03, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.94, "entryType": "measure"}, {"startTime": 6929.02, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.35, "entryType": "measure"}, {"startTime": 6929.42, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.09, "entryType": "measure"}, {"startTime": 6930.57, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.26, "entryType": "measure"}, {"startTime": 6931.88, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.07, "entryType": "measure"}, {"startTime": 6932, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.15, "entryType": "measure"}, {"startTime": 6933.42, "name": "lh:audit:accesskeys", "duration": 0.52, "entryType": "measure"}, {"startTime": 6934.05, "name": "lh:audit:aria-allowed-attr", "duration": 0.44, "entryType": "measure"}, {"startTime": 6934.65, "name": "lh:audit:aria-allowed-role", "duration": 0.47, "entryType": "measure"}, {"startTime": 6935.24, "name": "lh:audit:aria-command-name", "duration": 0.51, "entryType": "measure"}, {"startTime": 6935.88, "name": "lh:audit:aria-conditional-attr", "duration": 0.61, "entryType": "measure"}, {"startTime": 6936.59, "name": "lh:audit:aria-deprecated-role", "duration": 0.54, "entryType": "measure"}, {"startTime": 6937.25, "name": "lh:audit:aria-dialog-name", "duration": 0.63, "entryType": "measure"}, {"startTime": 6937.99, "name": "lh:audit:aria-hidden-body", "duration": 2.66, "entryType": "measure"}, {"startTime": 6940.78, "name": "lh:audit:aria-hidden-focus", "duration": 0.68, "entryType": "measure"}, {"startTime": 6941.57, "name": "lh:audit:aria-input-field-name", "duration": 0.68, "entryType": "measure"}, {"startTime": 6942.42, "name": "lh:audit:aria-meter-name", "duration": 0.68, "entryType": "measure"}, {"startTime": 6943.3, "name": "lh:audit:aria-progressbar-name", "duration": 0.77, "entryType": "measure"}, {"startTime": 6944.18, "name": "lh:audit:aria-prohibited-attr", "duration": 0.71, "entryType": "measure"}, {"startTime": 6945.01, "name": "lh:audit:aria-required-attr", "duration": 0.74, "entryType": "measure"}, {"startTime": 6945.9, "name": "lh:audit:aria-required-children", "duration": 1.01, "entryType": "measure"}, {"startTime": 6947.02, "name": "lh:audit:aria-required-parent", "duration": 0.84, "entryType": "measure"}, {"startTime": 6947.96, "name": "lh:audit:aria-roles", "duration": 0.8, "entryType": "measure"}, {"startTime": 6948.88, "name": "lh:audit:aria-text", "duration": 2.66, "entryType": "measure"}, {"startTime": 6951.67, "name": "lh:audit:aria-toggle-field-name", "duration": 0.86, "entryType": "measure"}, {"startTime": 6952.63, "name": "lh:audit:aria-tooltip-name", "duration": 0.94, "entryType": "measure"}, {"startTime": 6953.68, "name": "lh:audit:aria-treeitem-name", "duration": 0.93, "entryType": "measure"}, {"startTime": 6954.7, "name": "lh:audit:aria-valid-attr-value", "duration": 1.22, "entryType": "measure"}, {"startTime": 6956.05, "name": "lh:audit:aria-valid-attr", "duration": 0.95, "entryType": "measure"}, {"startTime": 6957.1, "name": "lh:audit:button-name", "duration": 0.94, "entryType": "measure"}, {"startTime": 6958.13, "name": "lh:audit:bypass", "duration": 0.94, "entryType": "measure"}, {"startTime": 6959.17, "name": "lh:audit:color-contrast", "duration": 2.19, "entryType": "measure"}, {"startTime": 6961.5, "name": "lh:audit:definition-list", "duration": 3.95, "entryType": "measure"}, {"startTime": 6965.58, "name": "lh:audit:dlitem", "duration": 1.06, "entryType": "measure"}, {"startTime": 6966.76, "name": "lh:audit:document-title", "duration": 2.01, "entryType": "measure"}, {"startTime": 6968.86, "name": "lh:audit:duplicate-id-aria", "duration": 1.01, "entryType": "measure"}, {"startTime": 6969.96, "name": "lh:audit:empty-heading", "duration": 2.22, "entryType": "measure"}, {"startTime": 6972.29, "name": "lh:audit:form-field-multiple-labels", "duration": 1.09, "entryType": "measure"}, {"startTime": 6973.48, "name": "lh:audit:frame-title", "duration": 1.1, "entryType": "measure"}, {"startTime": 6974.68, "name": "lh:audit:heading-order", "duration": 1.97, "entryType": "measure"}, {"startTime": 6976.75, "name": "lh:audit:html-has-lang", "duration": 4.61, "entryType": "measure"}, {"startTime": 6981.49, "name": "lh:audit:html-lang-valid", "duration": 1.22, "entryType": "measure"}, {"startTime": 6982.82, "name": "lh:audit:html-xml-lang-mismatch", "duration": 1.21, "entryType": "measure"}, {"startTime": 6984.15, "name": "lh:audit:identical-links-same-purpose", "duration": 1.14, "entryType": "measure"}, {"startTime": 6985.38, "name": "lh:audit:image-alt", "duration": 1.22, "entryType": "measure"}, {"startTime": 6986.7, "name": "lh:audit:image-redundant-alt", "duration": 1.3, "entryType": "measure"}, {"startTime": 6988.09, "name": "lh:audit:input-button-name", "duration": 1.49, "entryType": "measure"}, {"startTime": 6989.7, "name": "lh:audit:input-image-alt", "duration": 3.44, "entryType": "measure"}, {"startTime": 6993.25, "name": "lh:audit:label-content-name-mismatch", "duration": 1.31, "entryType": "measure"}, {"startTime": 6994.66, "name": "lh:audit:label", "duration": 1.38, "entryType": "measure"}, {"startTime": 6996.13, "name": "lh:audit:landmark-one-main", "duration": 1.99, "entryType": "measure"}, {"startTime": 6998.24, "name": "lh:audit:link-name", "duration": 1.4, "entryType": "measure"}, {"startTime": 6999.73, "name": "lh:audit:link-in-text-block", "duration": 1.36, "entryType": "measure"}, {"startTime": 7001.18, "name": "lh:audit:list", "duration": 1.47, "entryType": "measure"}, {"startTime": 7002.81, "name": "lh:audit:listitem", "duration": 4.21, "entryType": "measure"}, {"startTime": 7007.23, "name": "lh:audit:meta-refresh", "duration": 3.09, "entryType": "measure"}, {"startTime": 7010.53, "name": "lh:audit:meta-viewport", "duration": 3.54, "entryType": "measure"}, {"startTime": 7014.33, "name": "lh:audit:object-alt", "duration": 2.55, "entryType": "measure"}, {"startTime": 7017.05, "name": "lh:audit:select-name", "duration": 5.8, "entryType": "measure"}, {"startTime": 7022.97, "name": "lh:audit:skip-link", "duration": 1.51, "entryType": "measure"}, {"startTime": 7024.59, "name": "lh:audit:tabindex", "duration": 1.82, "entryType": "measure"}, {"startTime": 7026.51, "name": "lh:audit:table-duplicate-name", "duration": 1.72, "entryType": "measure"}, {"startTime": 7028.35, "name": "lh:audit:table-fake-caption", "duration": 1.8, "entryType": "measure"}, {"startTime": 7030.24, "name": "lh:audit:target-size", "duration": 1.63, "entryType": "measure"}, {"startTime": 7031.98, "name": "lh:audit:td-has-header", "duration": 1.97, "entryType": "measure"}, {"startTime": 7034.08, "name": "lh:audit:td-headers-attr", "duration": 2, "entryType": "measure"}, {"startTime": 7036.19, "name": "lh:audit:th-has-data-cells", "duration": 1.91, "entryType": "measure"}, {"startTime": 7038.21, "name": "lh:audit:valid-lang", "duration": 4.23, "entryType": "measure"}, {"startTime": 7042.57, "name": "lh:audit:video-caption", "duration": 1.91, "entryType": "measure"}, {"startTime": 7044.5, "name": "lh:audit:custom-controls-labels", "duration": 0.08, "entryType": "measure"}, {"startTime": 7044.58, "name": "lh:audit:custom-controls-roles", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.6, "name": "lh:audit:focus-traps", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.62, "name": "lh:audit:focusable-controls", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.63, "name": "lh:audit:interactive-element-affordance", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.65, "name": "lh:audit:logical-tab-order", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.66, "name": "lh:audit:managed-focus", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.67, "name": "lh:audit:offscreen-content-hidden", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.69, "name": "lh:audit:use-landmarks", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.7, "name": "lh:audit:visual-order-follows-dom", "duration": 0.01, "entryType": "measure"}, {"startTime": 7044.81, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.53, "entryType": "measure"}, {"startTime": 7046.46, "name": "lh:audit:total-byte-weight", "duration": 0.94, "entryType": "measure"}, {"startTime": 7047.52, "name": "lh:audit:offscreen-images", "duration": 1.82, "entryType": "measure"}, {"startTime": 7049.48, "name": "lh:audit:render-blocking-resources", "duration": 0.82, "entryType": "measure"}, {"startTime": 7049.77, "name": "lh:computed:UnusedCSS", "duration": 0.09, "entryType": "measure"}, {"startTime": 7049.87, "name": "lh:computed:NavigationInsights", "duration": 0.04, "entryType": "measure"}, {"startTime": 7049.94, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.04, "entryType": "measure"}, {"startTime": 7050.39, "name": "lh:audit:unminified-css", "duration": 1.19, "entryType": "measure"}, {"startTime": 7051.66, "name": "lh:audit:unminified-javascript", "duration": 23.43, "entryType": "measure"}, {"startTime": 7075.2, "name": "lh:audit:unused-css-rules", "duration": 1.26, "entryType": "measure"}, {"startTime": 7076.54, "name": "lh:audit:unused-javascript", "duration": 2, "entryType": "measure"}, {"startTime": 7078.62, "name": "lh:audit:modern-image-formats", "duration": 1.15, "entryType": "measure"}, {"startTime": 7079.85, "name": "lh:audit:uses-optimized-images", "duration": 1.44, "entryType": "measure"}, {"startTime": 7081.39, "name": "lh:audit:uses-text-compression", "duration": 1.2, "entryType": "measure"}, {"startTime": 7082.66, "name": "lh:audit:uses-responsive-images", "duration": 1.04, "entryType": "measure"}, {"startTime": 7083.77, "name": "lh:audit:efficient-animated-content", "duration": 1.05, "entryType": "measure"}, {"startTime": 7084.9, "name": "lh:audit:duplicated-javascript", "duration": 0.99, "entryType": "measure"}, {"startTime": 7085.98, "name": "lh:audit:legacy-javascript", "duration": 37.97, "entryType": "measure"}, {"startTime": 7124.07, "name": "lh:audit:doctype", "duration": 0.47, "entryType": "measure"}, {"startTime": 7124.64, "name": "lh:audit:charset", "duration": 0.5, "entryType": "measure"}, {"startTime": 7125.25, "name": "lh:audit:dom-size", "duration": 0.98, "entryType": "measure"}, {"startTime": 7126.34, "name": "lh:audit:geolocation-on-start", "duration": 0.42, "entryType": "measure"}, {"startTime": 7126.85, "name": "lh:audit:inspector-issues", "duration": 0.36, "entryType": "measure"}, {"startTime": 7127.31, "name": "lh:audit:no-document-write", "duration": 0.35, "entryType": "measure"}, {"startTime": 7127.73, "name": "lh:audit:js-libraries", "duration": 0.26, "entryType": "measure"}, {"startTime": 7128.09, "name": "lh:audit:notification-on-start", "duration": 0.36, "entryType": "measure"}, {"startTime": 7128.54, "name": "lh:audit:paste-preventing-inputs", "duration": 0.35, "entryType": "measure"}, {"startTime": 7128.97, "name": "lh:audit:uses-http2", "duration": 1.39, "entryType": "measure"}, {"startTime": 7130.48, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.43, "entryType": "measure"}, {"startTime": 7131.01, "name": "lh:audit:meta-description", "duration": 0.37, "entryType": "measure"}, {"startTime": 7131.47, "name": "lh:audit:http-status-code", "duration": 0.34, "entryType": "measure"}, {"startTime": 7131.91, "name": "lh:audit:font-size", "duration": 0.47, "entryType": "measure"}, {"startTime": 7132.47, "name": "lh:audit:link-text", "duration": 0.36, "entryType": "measure"}, {"startTime": 7132.92, "name": "lh:audit:crawlable-anchors", "duration": 0.35, "entryType": "measure"}, {"startTime": 7133.39, "name": "lh:audit:is-crawlable", "duration": 1.83, "entryType": "measure"}, {"startTime": 7135.34, "name": "lh:audit:robots-txt", "duration": 0.42, "entryType": "measure"}, {"startTime": 7135.87, "name": "lh:audit:hreflang", "duration": 0.43, "entryType": "measure"}, {"startTime": 7136.39, "name": "lh:audit:canonical", "duration": 0.41, "entryType": "measure"}, {"startTime": 7136.88, "name": "lh:audit:structured-data", "duration": 0.24, "entryType": "measure"}, {"startTime": 7137.25, "name": "lh:audit:bf-cache", "duration": 0.54, "entryType": "measure"}, {"startTime": 7137.89, "name": "lh:audit:cache-insight", "duration": 0.65, "entryType": "measure"}, {"startTime": 7138.64, "name": "lh:audit:cls-culprits-insight", "duration": 0.53, "entryType": "measure"}, {"startTime": 7139.29, "name": "lh:audit:document-latency-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 7139.73, "name": "lh:audit:dom-size-insight", "duration": 0.51, "entryType": "measure"}, {"startTime": 7140.34, "name": "lh:audit:duplicated-javascript-insight", "duration": 0.37, "entryType": "measure"}, {"startTime": 7140.8, "name": "lh:audit:font-display-insight", "duration": 0.37, "entryType": "measure"}, {"startTime": 7141.28, "name": "lh:audit:forced-reflow-insight", "duration": 0.37, "entryType": "measure"}, {"startTime": 7141.76, "name": "lh:audit:image-delivery-insight", "duration": 0.33, "entryType": "measure"}, {"startTime": 7142.18, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 0.34, "entryType": "measure"}, {"startTime": 7142.63, "name": "lh:audit:lcp-discovery-insight", "duration": 0.33, "entryType": "measure"}, {"startTime": 7143.05, "name": "lh:audit:lcp-phases-insight", "duration": 0.43, "entryType": "measure"}, {"startTime": 7143.58, "name": "lh:audit:legacy-javascript-insight", "duration": 0.65, "entryType": "measure"}, {"startTime": 7144.34, "name": "lh:audit:modern-http-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 7144.79, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.43, "entryType": "measure"}, {"startTime": 7145.34, "name": "lh:audit:render-blocking-insight", "duration": 0.44, "entryType": "measure"}, {"startTime": 7145.88, "name": "lh:audit:third-parties-insight", "duration": 3.17, "entryType": "measure"}, {"startTime": 7149.16, "name": "lh:audit:viewport-insight", "duration": 0.42, "entryType": "measure"}, {"startTime": 7149.59, "name": "lh:runner:generate", "duration": 0.26, "entryType": "measure"}], "total": 5849.12}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | failureTitle": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/audits/viewport.js | explanationNoTag": ["audits.viewport.explanation"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 1058.9672}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 3014.9344}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 1058.9672}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 3014.9344}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 232.50400000000033}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 108.24800000000027}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 0}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 45}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 0.0189}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 3.7336}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 3014.9344}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | failureTitle": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/lib/i18n/i18n.js | columnSource": ["audits[errors-in-console].details.headings[0].label"], "core/lib/i18n/i18n.js | columnDescription": ["audits[errors-in-console].details.headings[1].label", "audits[csp-xss].details.headings[0].label", "audits[has-hsts].details.headings[0].label", "audits[origin-isolation].details.headings[0].label"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 1.396}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits[bootup-time].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[legacy-javascript].details.headings[0].label", "audits[legacy-javascript-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/font-display.js | undeclaredFontOriginWarning": [{"values": {"fontCountForOrigin": 1, "fontOrigin": "http://localhost:3000"}, "path": "audits[font-display].warnings[0]"}], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[unused-javascript].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[dom-size].details.headings[1].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | title": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/csp-xss.js | columnDirective": ["audits[csp-xss].details.headings[1].label"], "core/audits/csp-xss.js | columnSeverity": ["audits[csp-xss].details.headings[2].label"], "core/lib/i18n/i18n.js | itemSeverityHigh": ["audits[csp-xss].details.items[0].severity", "audits[has-hsts].details.items[0].severity", "audits[origin-isolation].details.items[0].severity"], "core/audits/csp-xss.js | noCsp": ["audits[csp-xss].details.items[0].description"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/has-hsts.js | columnDirective": ["audits[has-hsts].details.headings[1].label"], "core/audits/has-hsts.js | columnSeverity": ["audits[has-hsts].details.headings[2].label"], "core/audits/has-hsts.js | noHsts": ["audits[has-hsts].details.items[0].description"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/origin-isolation.js | columnDirective": ["audits[origin-isolation].details.headings[1].label"], "core/audits/origin-isolation.js | columnSeverity": ["audits[origin-isolation].details.headings[2].label"], "core/audits/origin-isolation.js | noCoop": ["audits[origin-isolation].details.items[0].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | title": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | title": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | failureTitle": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/lib/i18n/i18n.js | columnFailingElem": ["audits[document-title].details.headings[0].label", "audits[html-has-lang].details.headings[0].label", "audits[landmark-one-main].details.headings[0].label"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | failureTitle": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | title": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 0}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 257748}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 69739}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 11135}, "path": "audits[legacy-javascript].displayValue"}, {"values": {"wastedBytes": 13894}, "path": "audits[legacy-javascript-insight].displayValue"}], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[unused-javascript].details.headings[2].label", "audits[legacy-javascript].details.headings[2].label"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 8}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | failureTitle": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | failureTitle": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/font-size.js | explanationViewport": ["audits[font-size].explanation"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | title": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | failureTitle": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "core/audits/bf-cache.js | displayValue": [{"values": {"itemCount": 1}, "path": "audits[bf-cache].displayValue"}], "core/audits/bf-cache.js | failureReasonColumn": ["audits[bf-cache].details.headings[0].label"], "core/audits/bf-cache.js | failureTypeColumn": ["audits[bf-cache].details.headings[1].label"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | HTTPStatusNotOK": ["audits[bf-cache].details.items[0].reason"], "core/audits/bf-cache.js | notActionableFailureType": ["audits[bf-cache].details.items[0].failureType"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": ["audits[document-latency-insight].details.items.noRedirects.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": [{"values": {"PH1": "1 ms"}, "path": "audits[document-latency-insight].details.items.serverResponseIsFast.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase": ["audits[lcp-phases-insight].details.items[0].headings[0].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[lcp-phases-insight].details.items[0].headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte": ["audits[lcp-phases-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay": ["audits[lcp-phases-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnWastedBytes": ["audits[legacy-javascript-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}