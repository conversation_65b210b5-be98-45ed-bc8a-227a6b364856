import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { routing } from './src/i18n/routing';

/**
 * Optimized Next.js Middleware for Internationalization
 *
 * This middleware provides:
 * - Automatic locale detection and routing with performance optimizations
 * - Reduced redirects through intelligent locale handling
 * - Browser language preference detection
 * - SEO-friendly URL structure
 * - Performance monitoring and caching headers
 */

// Create the base next-intl middleware
const intlMiddleware = createMiddleware(routing);

// Performance optimization: Cache locale detection results
const localeCache = new Map<string, string>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Detect user's preferred locale from Accept-Language header
 */
function detectPreferredLocale(request: NextRequest): string {
  const acceptLanguage = request.headers.get('accept-language');
  if (!acceptLanguage) return routing.defaultLocale;

  // Check cache first
  const cacheKey = acceptLanguage;
  const cached = localeCache.get(cacheKey);
  if (cached) return cached;

  // Parse Accept-Language header
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [locale, q = '1'] = lang.trim().split(';q=');
      // 确保locale存在且为字符串
      if (!locale || typeof locale !== 'string') {
        return { locale: '', quality: 0 };
      }
      return { locale: locale.toLowerCase(), quality: parseFloat(q) };
    })
    .filter(lang => lang.locale) // 过滤掉空的locale
    .sort((a, b) => b.quality - a.quality);

  // Find best matching locale
  for (const { locale } of languages) {
    const shortLocale = locale.split('-')[0];
    if (shortLocale && routing.locales.includes(shortLocale as any)) {
      localeCache.set(cacheKey, shortLocale);
      // Clean cache periodically
      setTimeout(() => localeCache.delete(cacheKey), CACHE_TTL);
      return shortLocale;
    }
  }

  return routing.defaultLocale;
}

/**
 * Enhanced middleware with performance optimizations
 */
export default function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Performance: Skip middleware for static assets and API routes
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/_vercel/') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon') ||
    pathname.startsWith('/robots') ||
    pathname.startsWith('/sitemap')
  ) {
    return NextResponse.next();
  }

  // Performance: Handle root path efficiently
  if (pathname === '/') {
    const preferredLocale = detectPreferredLocale(request);
    const response = NextResponse.redirect(
      new URL(`/${preferredLocale}`, request.url),
      { status: 302 } // Temporary redirect for better caching
    );

    // Add performance headers
    response.headers.set('Cache-Control', 'public, max-age=300'); // 5 minutes
    response.headers.set('Vary', 'Accept-Language');

    return response;
  }

  // Use next-intl middleware for all other routes
  const response = intlMiddleware(request);

  // Add performance headers to all responses
  if (response) {
    response.headers.set('X-Locale-Optimized', 'true');

    // Add caching headers for locale-specific content
    if (pathname.match(/^\/(en|zh)\//)) {
      response.headers.set('Cache-Control', 'public, max-age=3600'); // 1 hour
      response.headers.set('Vary', 'Accept-Language');
    }
  }

  return response;
}

export const config = {
  // Optimized matcher pattern for better performance
  matcher: [
    // Match all pathnames except for static assets and API routes
    '/((?!api|_next|_vercel|favicon|robots|sitemap|.*\\..*).*)',
    // Include root path for locale detection
    '/',
  ],
};
