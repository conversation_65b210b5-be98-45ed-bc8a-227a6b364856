# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage
coverage/

# Misc
.DS_Store
*.pem

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
next-env.d.ts
*.tsbuildinfo

# Config files
.eslintrc.js
next.config.mjs
postcss.config.mjs
tailwind.config.ts

# Documentation
*.md

# IDE
.vscode/
.idea/
