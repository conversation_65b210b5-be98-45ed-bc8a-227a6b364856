# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage
coverage/

# Misc
.DS_Store
*.pem

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
next-env.d.ts
*.tsbuildinfo

# Documentation that should not be formatted
CHANGELOG.md
LICENSE

# Config files that have specific formatting
.vscode/
.idea/
