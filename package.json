{"name": "tucsenberg-web-stable", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "eslint --ext .js,.jsx,.ts,.tsx .", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "format": "prettier --write . && eslint --ext .js,.jsx,.ts,.tsx . --fix", "type-check": "tsc --noEmit", "quality-check": "npm run type-check && npm run lint:strict && npm run prettier", "quality-gate": "npm run type-check:strict && npm run lint:strict && npm run test:coverage && npm run test:a11y && npm run test:performance", "type-check:strict": "tsc --noEmit --strict --noUnusedLocals --noUnusedParameters", "type-coverage": "type-coverage --at-least 95 --detail", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":80,\"functions\":80,\"lines\":80,\"statements\":80}}'", "test:a11y": "jest --testPathPattern=a11y", "test:performance": "jest --testPathPattern=performance", "test:components": "jest --testPathPattern=components", "lighthouse:ci": "lhci autorun", "lighthouse:collect": "lhci collect", "lighthouse:assert": "lhci assert", "config-check": "node scripts/config-check.js", "config-validate": "npm run config-check && npm run type-check", "build-analyze": "node scripts/build-analyze.js", "build-stats": "npm run build && npm run build-analyze", "analyze": "ANALYZE=true npm run build", "performance:check": "node scripts/performance-check.js", "performance:budget": "cat performance-budget.json", "performance:monitor": "npm run performance:check && echo 'Performance monitoring completed'", "prepare": "husky install", "pre-commit": "lint-staged"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.15", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "framer-motion": "11.5.6", "geist": "^1.4.2", "lucide-react": "0.446.0", "next": "14.2.18", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "18.2.0", "react-dom": "18.2.0", "tailwind-merge": "2.5.2", "tailwindcss": "3.4.14", "tailwindcss-animate": "^1.0.7", "typescript": "5.6.3", "web-vitals": "3.5.2", "zod": "3.23.8"}, "devDependencies": {"@lhci/cli": "^0.15.1", "@next/bundle-analyzer": "^15.4.2", "@svgr/webpack": "8.1.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.0", "@types/jest-axe": "^3.5.9", "@types/node": "20.17.6", "@types/react": "18.2.79", "@types/react-dom": "18.2.25", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "10.4.20", "eslint": "8.57.1", "eslint-config-next": "14.2.18", "eslint-config-prettier": "9.1.0", "eslint-plugin-react-you-might-not-need-an-effect": "^0.4.1", "husky": "8.0.3", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "15.2.10", "postcss": "8.4.47", "prettier": "3.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}