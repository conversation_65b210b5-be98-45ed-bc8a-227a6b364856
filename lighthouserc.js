const performanceBudget = require('./performance-budget.json');

module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000',
        'http://localhost:3000/en',
        'http://localhost:3000/zh',
      ],
      numberOfRuns: 3,
      startServerCommand: 'npm run start',
      startServerReadyPattern: 'Ready|ready|started',
      startServerReadyTimeout: 30000,
      settings: {
        chromeFlags: '--no-sandbox --disable-dev-shm-usage',
        // Performance budget integration
        budgets: performanceBudget.budgets,
      },
    },
    assert: {
      preset: 'lighthouse:recommended',
      assertions: {
        // Performance assertions based on budget configuration
        'first-contentful-paint': [
          'error',
          { maxNumericValue: performanceBudget.budgets[0].timings[0].budget },
        ],
        'largest-contentful-paint': [
          'error',
          { maxNumericValue: performanceBudget.budgets[0].timings[1].budget },
        ],
        interactive: [
          'error',
          { maxNumericValue: performanceBudget.budgets[0].timings[2].budget },
        ],
        'total-blocking-time': [
          'error',
          { maxNumericValue: performanceBudget.budgets[0].timings[3].budget },
        ],
        'cumulative-layout-shift': [
          'error',
          { maxNumericValue: performanceBudget.budgets[0].timings[4].budget },
        ],
        'speed-index': [
          'error',
          { maxNumericValue: performanceBudget.budgets[0].timings[5].budget },
        ],

        // Category scores based on budget
        'categories:performance': [
          'error',
          { minScore: performanceBudget.lighthouse.performance.budget / 100 },
        ],
        'categories:accessibility': [
          'error',
          { minScore: performanceBudget.lighthouse.accessibility.budget / 100 },
        ],
        'categories:best-practices': [
          'error',
          {
            minScore:
              performanceBudget.lighthouse['best-practices'].budget / 100,
          },
        ],
        'categories:seo': [
          'error',
          { minScore: performanceBudget.lighthouse.seo.budget / 100 },
        ],

        // Resource size assertions based on budget (in bytes)
        'resource-summary:script:size': [
          'warn',
          {
            maxNumericValue:
              performanceBudget.budgets[0].resourceSizes[0].budget * 1024,
          },
        ],
        'resource-summary:font:size': [
          'warn',
          {
            maxNumericValue:
              performanceBudget.budgets[0].resourceSizes[1].budget * 1024,
          },
        ],
        'resource-summary:stylesheet:size': [
          'warn',
          {
            maxNumericValue:
              performanceBudget.budgets[0].resourceSizes[2].budget * 1024,
          },
        ],
        'resource-summary:image:size': [
          'warn',
          {
            maxNumericValue:
              performanceBudget.budgets[0].resourceSizes[3].budget * 1024,
          },
        ],
        'resource-summary:total:size': [
          'warn',
          {
            maxNumericValue:
              performanceBudget.budgets[0].resourceSizes[7].budget * 1024,
          },
        ],

        // Disable some audits that might not be relevant for our B2B template
        'offscreen-images': 'off',
        'uses-webp-images': 'off',
        'unused-css-rules': 'off',
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
};
