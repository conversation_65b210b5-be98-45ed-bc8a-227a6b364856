# Tucsenberg Web Stable

> Enterprise B2B website template built with stable, production-verified technology stack

## 🚀 Tech Stack (Stable 2025)

### Core Framework

- **Next.js 14.2.18** - React full-stack framework with stable App Router
- **React 18.2.0** - UI library with 2+ years production verification
- **TypeScript 5.6.3** - Type-safe JavaScript with strict mode enabled
- **Tailwind CSS 3.4.14** - Mature atomic CSS framework

### UI & Design System

- **shadcn/ui (New York style)** - Modern UI component library
- **@radix-ui/react-\*** - Accessible component primitives
- **framer-motion 11.5.6** - Production-ready animation library
- **Lucide React** - Modern icon library
- **Inter & JetBrains Mono** - Professional typography

### Development Tools

- **ESLint 8** - Code linting with traditional configuration
- **Prettier** - Code formatting
- **Vitest** - Modern testing framework (5-10x faster than Jest)
- **Playwright** - End-to-end testing

## 📋 Project Features

- ✅ **Internationalization** - English/Chinese support with next-intl
- ✅ **Theme System** - Light/dark mode with OKLCH color space
- ✅ **Responsive Design** - Mobile-first approach
- ✅ **Type Safety** - Strict TypeScript configuration
- ✅ **Performance Optimized** - Core Web Vitals 2025 compliant
- ✅ **Accessibility** - WCAG 2.1 AA level compliance
- ✅ **Enterprise Ready** - Security audited, CI/CD ready

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd tucsenberg-web-stable
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start development server**

   ```bash
   npm run dev
   ```

5. **Open in browser**
   ```
   http://localhost:3000
   ```

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler check

## 🏗️ Project Structure

```
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # Reusable UI components
│   ├── lib/                 # Utility functions and configurations
│   └── styles/              # Global styles and themes
├── docs/                    # Project documentation
├── public/                  # Static assets
├── .vscode/                 # VS Code configuration
└── package.json             # Dependencies and scripts
```

## 🎯 Performance Benchmarks

- **Development server startup**: 3-5 seconds
- **Hot reload speed**: < 1 second
- **Production build time**: 2-4 minutes (medium project)
- **First contentful paint**: < 2 seconds
- **Page transitions**: < 500ms
- **Lighthouse score**: 90+ (all metrics)

## 🔒 Security & Quality

- **Dependency audit**: 0 high-risk vulnerabilities
- **Code coverage**: >85% target
- **TypeScript strict mode**: Enabled
- **ESLint rules**: Enterprise-grade configuration
- **Security headers**: Configured in next.config.mjs

## 📚 Documentation

- [Stable Tech Stack Guide](./docs/technology/稳定技术栈.md)
- [Task Planning](./docs/date/tasks.json)
- [Development Guidelines](./docs/development-guidelines.md)

## 🤝 Contributing

1. Follow the established code style and conventions
2. Ensure all tests pass before submitting PR
3. Update documentation for new features
4. Maintain backward compatibility

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ using stable, production-verified technologies**
