{"common": {"loading": "加载中...", "error": "发生错误", "retry": "重试", "cancel": "取消", "save": "保存", "edit": "编辑", "delete": "删除", "confirm": "确认", "back": "返回", "next": "下一步", "previous": "上一步", "close": "关闭", "search": "搜索", "filter": "筛选", "sort": "排序", "export": "导出", "import": "导入"}, "navigation": {"home": "首页", "products": "产品", "blog": "博客", "about": "关于", "contact": "联系", "language": "语言", "theme": "主题", "menu": "菜单"}, "theme": {"light": "浅色", "dark": "深色", "system": "系统", "toggle": "切换主题"}, "language": {"english": "English", "chinese": "中文", "switch": "切换语言"}, "HomePage": {"title": "欢迎来到 Tucsenberg Web Stable", "welcome": "欢迎来到 Tucsenberg Web Stable", "subtitle": "基于 Next.js 14、TypeScript 和 Tailwind CSS 构建的现代、稳定、可扩展的 Web 应用程序。", "description": "基于 Next.js 14、TypeScript 和 Tailwind CSS 构建的现代化、稳定且可扩展的网络应用程序。", "techStack": {"nextjs": "具有稳定 App Router 的 React 全栈框架", "typescript": "启用严格模式的类型安全 JavaScript", "tailwind": "用于快速 UI 开发的成熟原子 CSS 框架", "stableTitle": "稳定技术栈", "stable": "经过生产验证的技术组合，确保可靠性"}, "features": {"title": "核心特性", "performance": "高性能", "performanceDesc": "针对速度和效率进行优化", "scalable": "可扩展架构", "scalableDesc": "随着您的需求而增长", "secure": "安全第一", "secureDesc": "企业级安全措施", "modern": "现代技术栈", "modernDesc": "最新技术和最佳实践"}, "cta": {"title": "准备开始了吗？", "description": "探索我们的功能，了解为什么 Tucsenberg Web Stable 是您下一个项目的完美选择。", "button": "开始使用"}, "metadata": {"title": "Tucsenberg Web Stable - 现代网络开发平台", "description": "使用我们基于 Next.js、TypeScript 和 Tailwind CSS 的现代开发平台构建快速、安全且可扩展的网络应用程序。"}}, "ProductsPage": {"title": "我们的产品", "description": "探索我们的创新解决方案系列，旨在帮助您构建更好的网络应用程序。", "underConstruction": {"title": "即将推出", "message": "我们正在努力为您带来令人惊叹的产品。敬请期待更新！"}, "comingSoon": {"title": "令人兴奋的产品即将推出", "description": "我们正在开发尖端工具和服务，以增强您的开发体验。", "notifyButton": "有货时通知我"}, "metadata": {"title": "产品 - Tucsenberg Web Stable", "description": "探索我们创新的网络开发产品和解决方案，旨在加速您的项目交付。"}}, "BlogPage": {"title": "博客与见解", "description": "了解网络开发的最新趋势、教程和见解。", "underConstruction": {"title": "博客即将推出", "message": "我们正在为开发者准备有价值的内容和见解。请稍后查看！"}, "categories": {"title": "分类", "technology": "技术", "design": "设计", "business": "商业", "tutorials": "教程", "news": "新闻"}, "newsletter": {"title": "订阅我们的新闻通讯", "description": "获取最新文章和见解，直接发送到您的收件箱。", "emailPlaceholder": "输入您的邮箱地址", "subscribeButton": "订阅", "comingSoon": "新闻通讯功能即将推出"}, "metadata": {"title": "博客 - Tucsenberg Web Stable", "description": "阅读我们关于网络开发、设计趋势和技术见解的最新文章。"}}, "AboutPage": {"title": "关于我们", "hero": {"title": "构建网络开发的未来", "description": "我们致力于创造创新解决方案，赋能开发者和企业构建卓越的网络体验。"}, "underConstruction": {"title": "页面开发中", "message": "我们正在精心制作我们的故事，很快将分享更多关于我们旅程的信息。"}, "mission": {"title": "我们的使命", "description": "为开发者提供创建出色网络应用程序所需的工具、知识和平台，让这些应用程序产生真正的影响。", "values": {"innovation": "创新 - 突破可能性的边界", "quality": "质量 - 在每个细节中追求卓越", "sustainability": "可持续性 - 为长远发展而构建"}}, "team": {"title": "认识我们的团队", "comingSoon": "团队简介即将推出"}, "stats": {"title": "我们的影响", "projects": "项目", "clients": "满意客户", "experience": "年经验", "awards": "奖项"}, "contact": {"title": "让我们一起合作", "description": "准备开始您的下一个项目？我们很乐意听到您的声音，讨论如何帮助实现您的愿景。", "button": "联系我们"}, "placeholders": {"image": "图片占位符"}, "metadata": {"title": "关于我们 - Tucsenberg Web Stable", "description": "了解我们革新网络开发的使命，认识 Tucsenberg Web Stable 背后的团队。"}}, "ContactPage": {"title": "联系我们", "description": "与我们的团队取得联系。我们在这里帮助解答您的问题和项目需求。", "underConstruction": {"title": "联系表单即将推出", "message": "我们正在设置联系系统。目前，请通过邮件或电话联系我们。"}, "info": {"title": "联系方式", "address": {"title": "地址", "value": "创新街123号\n科技城，TC 12345\n美国"}, "phone": {"title": "电话", "value": "+****************"}, "email": {"title": "邮箱", "value": "<EMAIL>"}, "hours": {"title": "营业时间", "value": "周一至周五：上午9:00 - 下午6:00\n周六：上午10:00 - 下午4:00\n周日：休息"}}, "form": {"title": "发送消息", "fields": {"name": "姓名", "email": "邮箱地址", "subject": "主题", "message": "消息"}, "placeholders": {"name": "输入您的姓名", "email": "输入您的邮箱地址", "subject": "这是关于什么的？", "message": "告诉我们更多关于您的项目或问题..."}, "submit": "发送消息", "comingSoon": "联系表单功能即将推出"}, "map": {"title": "找到我们", "placeholder": "交互式地图即将推出"}, "metadata": {"title": "联系我们 - Tucsenberg Web Stable", "description": "联系我们的团队获取支持、咨询或讨论您的下一个网络开发项目。"}}, "Header": {"brand": "<PERSON><PERSON><PERSON>", "nav": {"home": "首页", "products": "产品", "blog": "博客", "about": "关于", "contact": "联系", "components": "组件"}, "actions": {"language": "语言", "theme": "主题", "toggleTheme": "切换主题", "toggleMenu": "切换菜单"}}, "Theme": {"toggle": "切换主题", "light": "浅色", "dark": "深色", "system": "跟随系统"}, "Footer": {"brand": "<PERSON><PERSON><PERSON>", "description": "使用现代化、稳定且可扩展的解决方案构建网络开发的未来。", "sections": {"company": {"title": "公司", "about": "关于我们", "blog": "博客", "contact": "联系我们"}, "products": {"title": "产品", "all": "所有产品", "featured": "精选产品", "new": "新品发布"}, "support": {"title": "支持", "help": "帮助中心", "documentation": "文档", "faq": "常见问题"}, "legal": {"title": "法律", "privacy": "隐私政策", "terms": "服务条款", "cookies": "<PERSON><PERSON>政策"}}, "newsletter": {"title": "保持更新", "description": "订阅我们的新闻通讯，获取最新更新和见解。", "placeholder": "输入您的邮箱", "subscribe": "订阅", "comingSoon": "即将推出"}, "copyright": "© {year} Tucsenberg. 保留所有权利。", "links": {"privacy": "隐私", "terms": "条款", "sitemap": "网站地图"}}, "pages": {"home": {"title": "开始编辑", "welcome": "欢迎使用 Tucsenberg Web Stable", "subtitle": "基于 Next.js、TypeScript 和 Tailwind CSS 构建的现代化、稳定的网络开发平台"}}, "footer": {"copyright": "© 2025 Tucsenberg Web Stable. 保留所有权利。", "builtWith": "使用稳定、生产验证的技术用❤️构建"}, "ComponentsPage": {"title": "UI 组件", "description": "探索我们全面的 UI 组件集合及其使用示例。"}, "metadata": {"defaultTitle": "Tucsenberg Web Stable", "defaultDescription": "基于稳定技术栈构建的企业级B2B网站模板", "keywords": "Next.js, React, TypeScript, Tailwind CSS, 企业级, B2B, 模板"}}