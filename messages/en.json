{"common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import"}, "navigation": {"home": "Home", "products": "Products", "blog": "Blog", "about": "About", "contact": "Contact", "language": "Language", "theme": "Theme", "menu": "<PERSON><PERSON>"}, "theme": {"light": "Light", "dark": "Dark", "system": "System", "toggle": "Toggle theme"}, "language": {"english": "English", "chinese": "中文", "switch": "Switch language"}, "HomePage": {"title": "Welcome to Tucsenberg Web Stable", "welcome": "Welcome to Tucsenberg Web Stable", "subtitle": "A modern, stable, and scalable web application built with Next.js 14, TypeScript, and Tailwind CSS.", "description": "A modern, stable, and scalable web application built with Next.js 14, TypeScript, and Tailwind CSS.", "techStack": {"nextjs": "React full-stack framework with stable App Router", "typescript": "Type-safe JavaScript with strict mode enabled", "tailwind": "Mature atomic CSS framework for rapid UI development", "stableTitle": "Stable Tech Stack", "stable": "Production-verified technology combination for reliability"}, "features": {"title": "Key Features", "performance": "High Performance", "performanceDesc": "Optimized for speed and efficiency", "scalable": "Scalable Architecture", "scalableDesc": "Built to grow with your needs", "secure": "Security First", "secureDesc": "Enterprise-grade security measures", "modern": "Modern Stack", "modernDesc": "Latest technologies and best practices"}, "cta": {"title": "Ready to Get Started?", "description": "Explore our features and see what makes Tucsenberg Web Stable the perfect choice for your next project.", "button": "Get Started"}, "metadata": {"title": "Tucsenberg Web Stable - Modern Web Development Platform", "description": "Build fast, secure, and scalable web applications with our modern development platform powered by Next.js, TypeScript, and Tailwind CSS."}}, "ProductsPage": {"title": "Our Products", "description": "Discover our range of innovative solutions designed to help you build better web applications.", "underConstruction": {"title": "Coming Soon", "message": "We're working hard to bring you amazing products. Stay tuned for updates!"}, "comingSoon": {"title": "Exciting Products on the Way", "description": "We're developing cutting-edge tools and services to enhance your development experience.", "notifyButton": "Notify Me When Available"}, "metadata": {"title": "Products - Tucsenberg Web Stable", "description": "Explore our innovative web development products and solutions designed to accelerate your project delivery."}}, "BlogPage": {"title": "Blog & Insights", "description": "Stay updated with the latest trends, tutorials, and insights in web development.", "underConstruction": {"title": "Blog Coming Soon", "message": "We're preparing valuable content and insights for developers. Check back soon!"}, "categories": {"title": "Categories", "technology": "Technology", "design": "Design", "business": "Business", "tutorials": "Tutorials", "news": "News"}, "newsletter": {"title": "Subscribe to Our Newsletter", "description": "Get the latest articles and insights delivered directly to your inbox.", "emailPlaceholder": "Enter your email address", "subscribeButton": "Subscribe", "comingSoon": "Newsletter functionality coming soon"}, "metadata": {"title": "Blog - Tucsenberg Web Stable", "description": "Read our latest articles on web development, design trends, and technology insights."}}, "AboutPage": {"title": "About Us", "hero": {"title": "Building the Future of Web Development", "description": "We are passionate about creating innovative solutions that empower developers and businesses to build exceptional web experiences."}, "underConstruction": {"title": "Page Under Development", "message": "We're crafting our story and will share more about our journey soon."}, "mission": {"title": "Our Mission", "description": "To provide developers with the tools, knowledge, and platform they need to create outstanding web applications that make a difference.", "values": {"innovation": "Innovation - Pushing the boundaries of what's possible", "quality": "Quality - Delivering excellence in every detail", "sustainability": "Sustainability - Building for the long term"}}, "team": {"title": "Meet Our Team", "comingSoon": "Team profiles coming soon"}, "stats": {"title": "Our Impact", "projects": "Projects", "clients": "Happy Clients", "experience": "Years Experience", "awards": "Awards"}, "contact": {"title": "Let's Work Together", "description": "Ready to start your next project? We'd love to hear from you and discuss how we can help bring your vision to life.", "button": "Get In Touch"}, "placeholders": {"image": "Image placeholder"}, "metadata": {"title": "About Us - Tucsenberg Web Stable", "description": "Learn about our mission to revolutionize web development and meet the team behind Tucsenberg Web Stable."}}, "ContactPage": {"title": "Contact Us", "description": "Get in touch with our team. We're here to help with your questions and project needs.", "underConstruction": {"title": "Contact Form Coming Soon", "message": "We're setting up our contact system. For now, please reach out via email or phone."}, "info": {"title": "Get in Touch", "address": {"title": "Address", "value": "123 Innovation Street\nTech City, TC 12345\nUnited States"}, "phone": {"title": "Phone", "value": "+****************"}, "email": {"title": "Email", "value": "<EMAIL>"}, "hours": {"title": "Business Hours", "value": "Monday - Friday: 9:00 AM - 6:00 PM\nSaturday: 10:00 AM - 4:00 PM\nSunday: Closed"}}, "form": {"title": "Send us a Message", "fields": {"name": "Full Name", "email": "Email Address", "subject": "Subject", "message": "Message"}, "placeholders": {"name": "Enter your full name", "email": "Enter your email address", "subject": "What's this about?", "message": "Tell us more about your project or question..."}, "submit": "Send Message", "comingSoon": "Contact form functionality coming soon"}, "map": {"title": "Find Us", "placeholder": "Interactive map coming soon"}, "metadata": {"title": "Contact Us - Tucsenberg Web Stable", "description": "Contact our team for support, questions, or to discuss your next web development project."}}, "Header": {"brand": "<PERSON><PERSON><PERSON>", "nav": {"home": "Home", "products": "Products", "blog": "Blog", "about": "About", "contact": "Contact", "components": "Components"}, "actions": {"language": "Language", "theme": "Theme", "toggleTheme": "Toggle theme", "toggleMenu": "Toggle menu"}}, "Theme": {"toggle": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "Footer": {"brand": "<PERSON><PERSON><PERSON>", "description": "Building the future of web development with modern, stable, and scalable solutions.", "sections": {"company": {"title": "Company", "about": "About Us", "blog": "Blog", "contact": "Contact"}, "products": {"title": "Products", "all": "All Products", "featured": "Featured", "new": "New Releases"}, "support": {"title": "Support", "help": "Help Center", "documentation": "Documentation", "faq": "FAQ"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>"}}, "newsletter": {"title": "Stay Updated", "description": "Subscribe to our newsletter for the latest updates and insights.", "placeholder": "Enter your email", "subscribe": "Subscribe", "comingSoon": "Coming soon"}, "copyright": "© {year} <PERSON><PERSON><PERSON>. All rights reserved.", "links": {"privacy": "Privacy", "terms": "Terms", "sitemap": "Sitemap"}}, "pages": {"home": {"title": "Get started by editing", "welcome": "Welcome to Tucsenberg Web Stable", "subtitle": "A modern, stable web development platform built with Next.js, TypeScript, and Tailwind CSS"}}, "footer": {"copyright": "© 2025 Tucsenberg Web Stable. All rights reserved.", "builtWith": "Built with ❤️ using stable, production-verified technologies"}, "ComponentsPage": {"title": "UI Components", "description": "Explore our comprehensive collection of UI components and their usage examples."}, "metadata": {"defaultTitle": "Tucsenberg Web Stable", "defaultDescription": "Enterprise B2B website template built with stable tech stack", "keywords": "Next.js, React, TypeScript, Tailwind CSS, Enterprise, B2B, Template"}}