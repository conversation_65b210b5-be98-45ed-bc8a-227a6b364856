{"lighthouseVersion": "12.6.1", "requestedUrl": "http://localhost:3000/", "mainDocumentUrl": "http://localhost:3000/en", "finalDisplayedUrl": "http://localhost:3000/en", "finalUrl": "http://localhost:3000/en", "fetchTime": "2025-07-20T08:52:46.892Z", "gatherMode": "navigation", "runWarnings": ["The page may not be loading as expected because your test URL (http://localhost:3000/) was redirected to http://localhost:3000/en. Try testing the second URL directly."], "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2847, "credits": {"axe-core": "4.10.3"}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "notApplicable"}, "viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 922.13715, "numericUnit": "millisecond", "displayValue": "0.9 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "numeric", "numericValue": 10185.137149999999, "numericUnit": "millisecond", "displayValue": "10.2 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 1326.7649040245865, "numericUnit": "millisecond", "displayValue": "1.3 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3000, "items": [{"timing": 375, "timestamp": 349587119726, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 750, "timestamp": 349587494726, "data": "data:image/jpeg;base64,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"}, {"timing": 1125, "timestamp": 349587869726, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAIDAQEBAAAAAAAAAAAAAAECAwQFBgcI/8QAQBAAAQMDAwIEBQMCAwUIAwAAAQACAwQFEQYSIRMxB0FRYRQicYGRFSMyQqEWUrEIM2Jy8CQ0gpKiwdHxVbLh/8QAFwEBAQEBAAAAAAAAAAAAAAAAAAECA//EACERAQEBAAICAQUBAAAAAAAAAAABEQIhEjFBAyJRYXET/9oADAMBAAIRAxEAPwD9CZHqmR6qMp9lROR6pkeqj7J9kROR6pkeqj7J9kE5HqmR6qPsn2QTkeqZHqo+yfZBOR6pkeqj7IgnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoK/dPuq5HumR7oLfdaVzutJbBF8XJIHSktjZFE+V7sDJw1gJwB3OOFt5HuuFqW1VdfJDPbqhsM7IZqZ25zmfJIGnLXN5a4OYw+4yOM5AdmWqgi6nUnjaY273guGWt9T7KKGsp6+jhqqKZk1PK0PZIw5DgRkFeTtejm0l7rK6peKmSoMjjUPlk34exjS0x52EfIOfQNGMjK72mKB9p0/b7dN0i+lgZAXxAgP2tA3Y8icZxz9Sg2q+50VvpKuqrKqKKCkYZJ3ud/u2gZyfssrKunf09lRE4yjLMPHzfT1XkqvSUlXQ6nojPFBS3eCaJrQDLskk35l5AI/kCW5IyMjHZXu2mqy5V1sqHS08LKV1O8xsLsNMU292MAbtwwPm4bycHKD0lbdKGhifLV1kMTGENcXPHBJwAfuthtRC5zGtmjLnjcwBwy4eo9QvMVGk4H2G4UcLKVlXVVclWZzD/ACc6cygO8zxhv0Cw1mkm1moH3OVsbZJImMa+OeRrqYtY9uGAYa4fOcZAxl3fOAHqKWvpquaeKmnbK+AgSBvIaSMjnskdfSyT1EDZ2dWndtlaTgt+VrvxhzefdcDQ+nZNPQ1LZvgwZWwtDaWMsb+3E2PJz5nbn24HOMnUrtJtqr7cayrhpJ6Oqe+SRrI8TyNdStgMJdnG04Lu/fb6ZQeyDmuLgHAlpwQD2WM1EIdI0zRh0Yy8bhlo9T6Li6Lt9Xb7FH+quElzqHGeqeAAS8gAA4yCQ1rG5z/SuFPocPmukrRA6eqlfK2aWSR+8OnZMYnx527fkDCRztA475D18F0o6hkL4JxI2aV8LC1pOXMLg7y7AtIz27eoVY7vQSw1UsVXFIylcWzbDuLCO4wOc/6rz9Fp2qo6K0RRsod1HX1Fa4M3RtAkdKdjBtPYS9+P49ueMzrBKy26go4m0rm3J8j43Oy3bvaAQ4YOcHJHr7d0HpG1ELnMZ1Wh7xuawnDiPp3UxTRTA9KVj8HB2kHC8dLpKSpukNwmlgiqI46OMOYC5zBA+Uu2uIH8hJjt691Hh5ZKuzS3I1dNDAx7KaGPptDeoIotpeQCeT/7Y5xkh3hqWzm7SWwV0fx8c7aZ0ODkSOjMob282NJz249V0RV05YHiohLDn5g8Y47/AIXzTUPhhLdtTV12iuxpDVzyveI2HdtdR/Ds5z/Jji9wPo8hc2DwjqI7BTW9tXbo3Q1wri4QOc17mxCMM25GGOx84Hccc90H1enutDU3CpoIKuKSspg0zRNd8zA7OM/gq1dXw0UlKyXqOfUSiJgYwuOT5nHZvHJ7Dhect+l/0/W12vtLHbmtuEDGn9jEscrc5IeO7XZBPmSAtfUVrr7jVaRE9FFU11FWMqamuia1kcQax28NDnbxuO3AGfc8IPZMmike9jJWOezhzQQS36+i06m80FNeKS1T1Gyvq2udDFsd84aCTzjA4BOCfJee0dpaXT99vtYJacU1xlMwgiaSQ8ve5zi53zc7/wCOSAckYzhbGorRdbhqWw11DU0MVHbpXSSMlje6R+9jmO2kEAfK44yO/twg7Vvu9BcauvpqKpZNPQyCGoa0H9t5GcE9s4PksUt/tsN6itMtQ5ldLxGx0Tw152l20PxtJ2gnGc4Xl9MaNrtLtvptV4dUyVpidAa6JrgwtaA5z9jWlxPPOee5yck9Cpst1qddUt2qKqhltVLGW09M+N/Uhe5pD5AQcFxzjJBw3IGMkkO3er3b7JHTyXSpEDKiZlPGSxzt0jjho4Bxk8ZPCxx6gtcl+ksrKtpuTAXOh2uHZrXEB2MEhr2EgHIDgfNcLV9jvl+03FQMrbbHVCtZUPkfC/YWRziWNoAdnPysBOeecY4xq23R1ZB4gv1LVVlI4yRuD2RxPDnF0UTC3lxaGgxZBxuOQCeEHvPun3VMhMhBf7p91TITI9EDJTJVM+x/CA+x/CJq+SmSq59j+E+x/CC2UyVX7H8J9j+EFslMlV+x/CfY/hUWyUyVX7H8J9j+FBbJUZKj7H8KPsfwgtynKr9j+Ez7H8Ii3KHKpkeh/CZHv+EFslMlVyPQ/hRx7/hBfJTJVOPf8Jx7/hUXyUyVTj3/AAnHv+EF8lMlU49/wnHv+EF8lMlU49/wnHv+EF8lMlUBHv8AhMj3/CgvkpkqmR7/AITI9/wgvkpkqmR7/hMj3/CDKApAUqQMqqjCkDKnCsAiK49lOPZWwpwoKYU4V8LWuNZT26hnrKyTp00LS+R+0naB3OByisuFGOey0qC82+4Wf9VpKpklv2ueZsEABudxwRnjBVbBfbbqCkfVWeqbUwMeY3ODS3DgAcYIB7EIN8hVI5XMsGpLTqF1SLPWNqvhiGylrHANJzjkgZ7HsusRyqjHhQQuZT6itVTqCpscFW110pmdSWDY4FreOc4x/UPPzXVKYaooVioVxNQoKlFcNQiEJn3Q0RQVr0lfR1ck0dJV088kJ2ytikDjGeeHAdjwe/omGtlAgKlMNQilRlTAwiAomKIpRTEQilFBmAUgKQFYBFQArgIArAIqAFOFKkBBGFr3CkirqCopJ27oZ43RPHqHDB/1W1tTaivz9Zri+z+GOq7NM4sq4qkU4Ye/7nyuaPsx62tGSTaIGrrZWOLJWW5lWwHt1NgHH/ikaPsu7qDQVzrdevqIacGy1NTFUTP6jRjA+bjOc8u8vNZ/FfRt3vV3grbJB1hLT/D1Deo1mAHbhncRnOfL/KoPN6cu0mgvCaCtpoY3XK61TjD1RwB23O9gG5H/ADLaZra+afvFp/WL9Zb3QVsgjnZRuZvpicc/L3Az3Oc4I44XrtdaHfd9G2+2250fxVtDOj1OGvAbtIP17/ULjUFpvtdcqGObRmnrZTRuHxU0sUUnUHGdgbyD3x/qqObYnMg/2gdUzynbGyg3OPoA2ElaNn1H4g62p7nedM1NvobdSyujgo5Ig985ADtpcQecEc5AyfLuvV27S1xHizf7tV0mLRW0fQbL1GneS2MEbQdw/i7yXmLDZteaFpLnZbDaqa5UtTK6SlreuxvScQG7i1x9ADg8Z8yqjo611/f9OaEtE1XaoqfVNxk+HEDnB0bHDu/gnvluGk8F3PbniV+q9daBvtj/AMaVluutqucnSf8ADxhjoHcZwQ0ZxuB5zkAjjuu1rjQuo9SaHtHxdfDU6pt0pqN21rGPJPLBgADGG845I8s8cW76e1t4i3uxM1RZ6ay2q2y9WZzZmyGZ3GQ0Bx77cD0yTk9le06ZtQas1nUeMNy0ppuroY4fhGviNVEC2A7GOc/IG5x5IA7ZcPILj6W1X4malmvemqKrtMN5s88jam5SxjDsEtaxrQ0tyXNcd23sBxnv6+j0reY/H2u1I+jxZZKIQsqOqzl+xgxtzu7g+Sjwv0rebH4g67ud0o+hRXOr6tJJ1WO6jepIc4aSRw4dwO6uVNjz+k/F+vPhLfL/AH2CGa6Wuf4VuwbGzvdt2EgdsF3OPJq81L4naws1lt+pKzUumbpBO6N89kgdGJ4Y3eQwd24diMnae+cFdbSXhVe6jwx1Zp+9UzLfW11YKmjc+Vj25aAWklhOASMHzwey17VpvWkdtt9kZ4daVhqabZFLd6tkErJWNGNxaPmJIAyeT34GeGU6fdPiX3bTnxVlnbHJWUnVpJ3tyGl7MscR5jkHC/PfgFDfKXX+sqqoutM2ho6p7ru3ojNS4dbDmHHygOBdgY44X6Po4WUtJBTxMijjiY2NrImbGNAGAGt8h6DyXxjQ2lNUaZ8RtUQVdljrNN6hqJHy1zKlgMUZ6hHyk7iTvAIx9Mq2dxJeq5dk1b4ma+orxqHSdTbbdaKOV8dPRywtfJUbQHbckHnBHOQMnA8yvpPg7rc690bHc5omQ10MhpqpjM7eoADlufIhwPtyPJfMtPWDxL8OrdeNOads9NdrfVzPko68VLGGAuAbuLXEc4DTjsCDglfSPBXRMug9Fst1ZIySvnmdU1JjOWteQAGg+gDR98qSVbY98DhMplMLWM6kcphQmSpi6sgUAqVMXRERTBtAK4CgK4WWgBWAQBWARUYUgKQFcBQVwsNZUwUVLJUVcjYoIxlz3dgtjCx1NPDVQPgqY2SwvGHMeMg/UKjhHV9g/wDykH9//hdG2XKjusDprfUMnia7YXM7A4Bx/cLxF8s1vj8QrPTR0VO2lkiy+JsYDXH5+48+wXR1BVP09e7FQWaKnpaStqAJ2RxNAdlzG59jgoY9gQqELyupb3X0Os7Jb6aRraWqx1WlgJPzEd/Jcusv2ob/AKjr7Zpd9LSU9CdstTOzducDjHY+YOOPLurGce8IVHBeNGo79b9G3a4Xq1Njr6HiPH8JuQN2M5wO59uy81pzUurrk+hrKWvsdzjqHjrW5j2xy07fM84Ofz91qVLH1QhVK+Y661vcxq//AA1p+stttkiiElTX17gGxkgENGeM4LfI9/LBK19I6/udZJqSzXWWgqLpbKOSpgraJwdFO0N7+mcub29wQMLUsZsr6muVqG/2vTtC2svdbFR0zniMSSZwXEEgcfQr4tbNYeI990DU362zW2GG3GR88kkY6lQG/MQ1uNoAbj0JOcFaPi5qWTV/gNZrxPA2Cea4NbIxhy3c0StJHscZx5Z81fLpPHt+iI3tkja9h3McA4EeYKkr4JqTWniDoqks+oLxFan6fqjHEbfECZIQW5Ac/Gd+0HkEjIPC6/iP4lXCLVdv0xpWttVvmqacVM1zuLw2OFrmlzQM8ZLQO4Odw481rYmV9jCL434d+I11rNR3jS+oKu119dSUrqmmuNucHRTgAEg4OM/MOwHYri+Fer/EbW9HBX/F2ymtFDWj46pljDX1EY2ufExoaQMN5zxy4c8K+UPGvvqL4NpLXHiJrirul+01Da22CgldGy3ztIkqsDdtD8Eh+0jnIblw4PK9P/s/ayu+tdOXOtvs0cs0NYYY9kYZhmxpxx7kqSymWPqaKFKtiakFFVSs2NaspVQpWaqUQKcLLTdAVgFACuFhtICthGhWaoJAU4RWAQQApwpwpwg8pdrVVz61tlfFCXUsMe18m4cH5vLOfMLBruz19bPbK+1xtlqKKTf0yQM8gg8+7f7r1MNZTTzyQQzxvmj/AJsByW+XKoK6kdWOpG1ERqR3iDvmHGeyo8HVWu/XPVVnutbQshihcA6NkjT02g5yTnknJ7egWP8ATL3pfU1yrbRbxc6Cvd1HRiUMc12SfP0JPl2K99LXUjK1lI+ojFU8ZbEXfMR9PsVmKsia8Zb4tWSacuL7g6iNzlJNNTysBYxueWuLe+RkDk+WfNfNrvo68Xp1BDS6Ugstwika6avhqGtjI9QwHjnB4yeF9tuNyore6FtdVRQGY7YxI7G8+g/IWwVucdY8sfH9b6NroNcHUVFZKbUNHUxBlTRTOaHBwaG7m7uP6WnIye/qs+mdPXN8F/qajS1msgnpJYKOGmY01B3N7OeDtx28hz6Y5+qOXnLlrPTdurTSVt6oYagHa5jpRlp9Hen3W5xnti8r6eG0Hpa82vwdvFkrqIxXOeOqEcHUYdxezDeQccn3Xj7voDU0/gTadPxWtzrvDcHTyU/WjBazMnO7dt/qHn5r7+yqgkpWVMc8Tqd4Dmyh4LXA9iD2Vg4OaHNILSMgg5BW/wDOVjzuvgGt7D4ia1t9o0tcLJS0lHSysdPdG1DXMl2gtDw3ORwSdvJJ9FteI/hzXU+t7ZqWy2Gl1FQx0zKartk72tLtjNjXDdx/Hb6kFvY5X3Urli/Wp18dZm3CmN1a3e6kDx1A3Gc7foQVb9OfJPqX4fNdAadujrrdLjWaKsWm6Q07o6OKFjHVZc5uDmRpwB3zkA8j0Wz4AaUumm9AVtq1JRGlnnrZX9IyNfmN0bG5y0keRX1IrmWG/Wq/08lRZLhTV0Eb+m98Dw8NdgHBI88EKzhInna+IaCsOv8AQ77xp/TsdnrrNLVF7bnJUtIpCQAXuYHZyGhpLSO47kd+h/slNI0XeiTuH6kQHev7bF6a5+C2lq2urKinlutuZWuLqinoqsxxS57gtIPHJ47ei9vpfT1s0vZobXZKYU9HFkhuSS5x7ucTySVmcLK3ecsdZSFCLdjKURFixYkKVVXXOtxAVuVXzVlmtN8cq4CqFkaubokBXCgBWA5UEgK2ECkIGEwpwpQeQ2m3a7J4EVZGSfbj/wCW/wB15+klcy9017OQyetfGQfJpAx/Zx/C9Zq+31NT8JUUMbn1ETiPl74P/wBf3WpXWSU6OgpooiauMtlDR33E8/2J/Co5lE9s+tbxdJgehb4nDjuMDHH2Dlz5NVXuWklucEtqjpWEkUj5P3XNB/Ofx9F6PTtlmFjuUdcwxVVcXh5PcAjA/uSfuvLwWmsoaV9HLpeGuqmuOypc4Fjhnz/+wqjQ8SLoy5W3S1ygjcQ+Vz+n55Bblv5BC2bjqnUunL3QHUcFEbZWv2AU+SYeR5+oyPXPktnW2nrlUW3T8Fto4mTQPc6QU4/biJLTnnyzlc7UNBqPWN3tlHcLT+nUVJLvmm6gcHdskfYYAGe/K1NTp9GukU01vqoqV/TnfE5sb/8AK4ggH8r852F1g0vR1to8Q9M1LKyokdtrXQiQlp4+VxPGDzlmc5X6NucMtRb6qGlmNPPJE5kco7xuIIDvseV8hddtf0FrqbNdNMC9yO3sZVucHMcDnGQBgj6447rpY5SvOa9tbLd4KbNJXYXDTjp+rUPqCDJzJHtYzDRjDwSQcHkr1ugptYUHhbBO2kobpV/CwfpdNFIIx0ixuOoXYGQOeDz2z5rlUPh1eqfwTvFjkDDdqyYVbKfeCG7TGdme24hn0yRz5rSutu1jqDwaFiistRba63dCAxmbBrYWNIOO2OQ04zzj7KyWXf0lssz9tSq8StW6Tv1qZqqr07X0tdKI56Wgk3TUgJAJOOxGfPIOCM+a5mo6m8U/+0rcG6bp6ee6zUbYofiSRFHmFpL345wADx5nC5uptFXe622zfoPh+6zMpZ2dcmQPnmdjk5POwY8+5cPRep1NZNX0HjlW6psFldXUkVM0Br5BG2oHSa1zA7yd5jju1O77/K9T1+HY8PNeai/x9WaL11DSC5sjM1PUUrcNkGN2PQjbkg4HYgrwvgXqmDRng3qa91ETpuhX7Y4gcdSRzIw0Z8hk8n0yvVeH+ndTX/xYqtb6qtbrNFDB0aWlc8OcSW7MeuAC4kkDJIx7ef0N4Y32u8GdR6eudK+23KeubU0wnxhxa1mM47A4Iz5d1fu9/wBT7fX8SfFjWNmoKHUV4qtL11qqnsL7ZR1ANVCxwyDgHggepdjzx5foeiqY6yjgqoDmGeNsjD6tcMj/AFX5ttGnby6ht1pHhFam3SIsiqLjXPzA5oGC87TnJ7nDj54BzhfpGhp2UlFT00bGRshjbG1kYw1oAwAB6LXDflOefDOpUKVqswRSOUws1pAVx2VcKwXOtwwpRSsq6DeyuFRqyBcXVcKzVUK7QgspCgKwQSERCgFVPZWUO7KjGVjd3WQqjlqM1jcsTllcsbl0jnWJyxOWVyxldY5ViKo5XcqOXWOdVUHlSoWmVcIpTyUtagilMLFagERThS1uQUomFi1qA7qUAUrFagEUosNOi1XaqBXC5Oi4V2rGCrtQXCsFRWBQWRAiDBWvLKchmd7ztaB3yf8A+ZKx0jyYnsc1zXRuLcO747j+y2iAcZHbsqnGScclUcgVNT8N1TK3iATY2f2+il9VKKkfO3HW6XRx82P82f7/AEXTLG4xtGMYxjyWNzG79+1u7tuxytRmvOz18zHMqBKyV3wskvSaMBhy3g8845/BUPraxz4YWzMBknEfULWkgGNzsYDiPIY+q7wjY1xc1jQ49yB3WMRRsADI2NAORhoGD6rpHOuKyrqzWgmVhh+KdTmPZzjaSDn1yFmu1U+O2PnpJYQ47dj3uG05cBwc4zg8c4zhb742HI2jk54Hn6rBFSww0jKZjB0GMEYYeRtAxhdY5WvLNuUpr6SeSueII4KrqsMYAc6Nze4BIJAPkfI47laNVdaqWPpvq2sLJ6V4mc1g29RzsghriMfKDgnPPK9qYImtY0RMDWfxAaMN+nosQpKdrCxtPEGHu0MGP+uStxi15aS7VUTpph/2p9JFVBjmDDZ9oiIOB6Elp9wV0tP1lVVPqWVMjZGsDHMkBjB+YHIIY5wxwCCe+fbK7LIY4w0RxsaGjDQ1oGB7KsUEcILYYmRtJyQxoAJ9VWdjxr9QXKKTpO6b8n4APazk1m8jJHk3bh2FldeK6epq2EtZEH1EBjOwFoY12HA7t5ccA/xxh3tk+uEUec9Nmd2/+I7+v1VTTwmUy9KPqkbS/aMkemVmtyvOaUr6iU0dPLVR1jJKFs5exoHRPygNJyc5ye/Pyn7enVIaeKHd0Yo49xy7Y0DP1WRS1UYUqcJgrNreIVlAClZtaEUoFmtJREWWo6AVwsYVwuTa4VwqAqwQXClVClBYKVUKSUGGqmMYYGAF73bRnsOCefwtFtXM0FuA6Qvf5OcAGkccD3XQkY2RhbIA5p8isJpKcsDOk3aCSAPfuqjWFc9zmx9PbK4sIa7yaRkn6jDh+Fqm4TSw7hGWNe1r2uDXDb8w4ORycHy911OjE17HiNocxu1pA7D0WEUlO0uLYWAnvx75/wBVqJWka+TqtcY2dB8r4W8ncHN3cn2+UrSfdqmOmZJJFCXSxNmjDXHABc0EH/zDn6rrPpIOq6URNErs/NjnkYytantlLTwCNsLD8rWuOO+OR/flbjnXObc6plTtnjhMbZjA8szknYXgj2xgYWj+vVTqEVLaZu2Rsb4ste0Dc9rdpJGCcOzkeh+/o3U0O8u6bd2/qZx/VjGfrjhagtlEzdspohuxkY9DuGPTnldI53HKfW3F1ZTQ5pmFtaYJsAkPb0TIMenB/I9Fe7XGrp6yWGkjpnCKm+JcZnluQCQWjH07+S6klJTvcXPiaXdQS5894AAd9cDH0WnW2ekra01FXEyYGIRbHtyMAkn7HPI9luOfTisvNSZ68QvaXOqIxCyVj3lrTAx5AawEnk+wGfsudV3q7Vlvr6mllhghdQU08bC0l0Zkc8Ow71+U/wBu3K9dUWuhqHOdLTRuc5+9xxgl23bn/wAox9FUWigbG1gpIgwRCANAwNgOQ36AqkxyW3WqpXbXRxOpaeoiopTvcZHPfsG4ZzwC8dySRk59Yjut0mioTHFRtfWSObGHFxDQ1rjk+pO3t7rtSW6jfWtq3U8ZqRgiTHOQMA/XBxlXZSU7OjtiYOiSY8D+OQQcfYlRqOVY7rVVk0LayGCNtTStqohE4ktHGQ4nufmHb3+p7mFhhpIITEYomMMUfSYQP4s4+Ue3AWdYtakSmFKLOtihSUUWIUhApWaoiIordBVwqBWCw6LhXCoFYKCwKsCqKUF8plVB9VOUBQUJUFVK5jK+QTSCWP8AaE/REgI4JxgY+p7qlTcg2GEta5plgMwII+XG3j/1LZ+AgE5k+fJf1C0uO3d64WD9KpQ7OJD8pYAZCQ1pIJA9OwWojXjrql1whgEbHRPdNudnBAY8Af6qLld46CUxzRknczGD3a7dl32DHH7e63TRwtkZI0OD2Oc4EH/McuH0JWKqoaepm6s0e5/TdFnP9Lu//XufVbjnXGud6niidJSRNeQDta93yuAlDN3bPOeFZl1nZWzR1FP+wKllP1GvHyOcxhAx3PzOIz7hb77TRmnZAY3dNkIgb8xyGgg9/XIByjrdTlpDg5xdMyckuOS9oaAf/SFuVixzbtcpqC7QA4NCIi+fjlg3AB+fQZ59snyXNbqjoUreuxks5fO4jqNj/bZK5gxnu7AGB7ckL08lJDJUGaRgc8xmI55BaTkgj7LmRadoIKaKCnE8LY2vY0xzOa7a925wJzk8/ceS1rOMQvZkrvg4KVzqgxmdgc/a10W3h+ccZcduO/c9lhgu9TJo2K6VMbYah8LZCIiCOccjI9+3K6bLTSsrBVNa/wCI3bt+8kn5du0/8OAOO2RnvytOtsDHWoUNJLJFGGCJofI5zWs3AnAzyQBgZ7JqYtSXf4m8VFD0BH0sjLpWh5xjnZ32nPDhn7LrALl1NkikklnjmnZUlsnSe6RzhC54wXNGf7fjC6sbQxjWgk4GMk5P5UtakAFOFKLNrUiFKkJhZ1rEKcIijWIwpwiKBhMIiNRtqwVFYLDS4KsCqKQoMmVKxq2UFlWXf0z0i0P8i4ZCZUqjn0NdI9sLqsxjruc2PYDjLc5ySfQFV/Wad3LGTOYGCVzw3hrDnDj7cFVraBxtrKOnJyX56pIBYN2SfxkfdUmtr5a6pxI6Klkgjh2sx8wBdkduOCB9yqjZ/UYDV9AB/wDLZvx8u7vtz6/2WlSXVz+rJVOp2Rxxl8jASJIfZzT3+o9PNZ2WqGOsdUMwNzzIW7G/yI/zYz79+6wyWhs3/eqqebEToW52ggOxk5A5PA5ViVaa7MjiY6Slq2vfJ02xmP5nEgkY5x5evHnhYXXdjHBhhnlle6TayKPJwxwBzz/xD/rAWwbe57oXz1U0zopRK3IaAMNLcYA9yVpz2uX9QgkpqiSFrWzlz27Scvcw4wR24P4C1GbFrfd2V9xmp4IZDA2COdlRxteH5wMdx2/se3Gc9yrhQwiQwTzZONsTQSB5kkkAflaYtD6JmbXM6OTpRU/7mCNrHE7u38sOf7HKzXe1R3J8D5JCx0O7b8jXD5gBnDgRkY4PktaxY1Xaho+q1sbKiSMtieZWM+RrZP4k85x9uFjkvrH3GlpqaGV0c1Q6nM7mfIS1jy4NPqCzHPvjOFkgsFPBTSQtllLXwwwknGcRdj27nPKRWJkVXBIyqnEEFRJUsg+XaHvDt3OM4+dxAz5/RWVMY73WXGkraGKkNJ0qqbojqscS07HOzwRx8v8AdRV32Om+Ihmjlimjhke2R8f7cjmNy7aM5OO/OMjsV0q2hjq5qOR7nNNLN1mhvmdjm4PthxXHl0pSy1E0rp5B1DOSGsYCeqHA5djJxuOMnjhPIkZjfYoKuthrAWCGR7WOaMhwZCyQ598OOPZqNv0E8lMynDwZOi52+PsJN2G9+HfKc98cd1lrLBTVfW6kkoMtU2qJGOCGBhb27FoIP/MUp7DTwbtskpLqz4zJI4d5N7fxClq4xQ6koZGbnsqYmmn+JjL4j+6zgHaBkkguaMeeRjI5Wa0XR1wra2F1PJT/AA/T+WVuH5cCeeSPIdvVYptO08sNJGZp2/DUhpI3NIyASwh3b+QMbSPJblttnwdTVVElTLUTVO3e6QNAG0YGAAMLOtY30CnCn7qNIwqq/wB1H3QQFJHopAUqaKBThWRTVZh/ZSq5UgqNLAqwKplSCguCpyqZUoLZU5CplMoLZQlVyoRE5UFFUkogSqFSe6grSWOZW3CZlYaWhpPip2RiWQGQRhjSSG8kHJO08e3JC05L7I6qjp6WglfMYG1D4pXiJ4a4kbWg/wAnDacjgDjnlbdXQ1IuD6ygqIopJIhFI2WMvaQ0ktIwRgjc76rTudlqq6mEE1ZBM3pBm+emDnsfzmRhBGCePoQFqVMZTdZXXWWkp6TqshexkrhM0PbuAO4MPdo3DJz64BwusuLX2aesq4nvngLIpY5I5HQZnjDSCWh4I4dg5OOxI5XcTUxQorEKMJq4hMKwCKaYjBU4RSpq4qpU4TCmriMKMK2EwggIpwoUURThMILqVAUoGVKIDlFSmfdEQTlMqEQTlQiwSVlNFVxUslRCyplBMcLngPeB3Ib3KIz5VT3WNtTA6pfTMmidUMaHPiDxvaD2JHcBZFRChSSoBBGRgj2QCoWvR11JXGcUdTBUGnldBN0pA7pyN7sdjs4ZHB5WympiqFYGVtLJVyUkdTA6qjG58LZAXtHqW9wFBrqQVwojVQfGlnUFP1B1NmcbtvfHummNhMLHVVENJA+eqmjhhYMukkcGtaPcngKKSpp6ynZPSTxTwP5bJE8Oa76EcJpjLhMLCK2lNaaMVMBqwzeYBIN4b67e+PdY6e50FVVS01NW0s1TF/vIo5Wuez6tByFDG0ilAioRWUIIRArIKorKAghFZMIJRFKAmURAypyoRFTlMqERA8r4f4xWiuvXixpqKzVBprvBbKmqopc4Amje1zQfZ2C0+zivuC5lRYbbU6gpL3NT7rpSRPghm3uGxj/5Dbnac+4RXweg8Q3/AKxrnVtHROFfTWGlbJRyNOYKhsj2Oa4d8NdyfYL1WkLlrWruTqCvrbm2irba+VtwraKmidSVHGHRhjiHxnJ4cD2HK+hU+jtP091u9yitkIq7vH0q4kuLZ24xhzCdvPOcDnzyuZQeGmlLfT1sNNbHhtXTmkkc6qme8QHvExxeXMb7NICDwXh3qzUOv4L0ypu7Le2228UxFNExxqpju/7WNzeGENwAOMk+gXpf9n2Cam8J7E6qr5Kls8ZfEyRrWiFuSOm3ABIyCcnJ5K9fSaVstHcqK4UlCyGroqP4CF8b3NDYPKMtBw4DHGQcLm0Xh9pq3UlNT0Ft2RUlb+o00bqiVzIqgAgOaC44HJ+UfL7IPnbr7qn9K1J/h4NM0erJ6aY0lPAKgUrQC7pMcA2STty7c4+/l73wsvkl809UvqLnPcKimrJKaR1TRfCTxEYPTlYON4DhkgAHPbuudp3w4gfYbjTaybS19ZX3SW7PNIZIm08r8Y6T8h4xjvkHnC9fp3T9s03bjRWalFPTmR0rhvc9z3u7uc5xLnOOBySTwiPkVBDadAeLOqKqnglFJTacFbPhxklnkMxLnEnkucfP/QLzPh5qG01Pi7ZLzW3ZlRe7zTVAqw1j9sEj3RiGnblo4a0Yz6557L9CR2K2x6imvrKfF1lpxSPn3u5iDtwbtzt7+eMq01lt898prxLBuuVNC+CKbe4bWPILhtzg5IHJGUHA8SabTM1jhr9YPY+1W6cVPSedzJZAC0NLMHf3OG+ZXM8ILJNaLZd7jPQx2alu9YayntbQGijj2ta0OAADXOxktHAyB6rqXDw20pcbJS2irtZfbqaofVQxNqZWbJXklztzXg93HzwM8JR+G+laOzXC1Q26Q0FeYzUxSVk8m8sO5vLnkjB9CM+aD59W2y2ac8cLtNF1qeGbStVXVk7HF8pcagl7wT5gAYHYYAXltC0TrLcvDO4XO0UFqoJnvgpbjRNb8TXPlYREKgAnaHA5PL+f8q/Qc+nLTUagN7no2yXI0ZoDK5ziDAXbiwsztIz54z7rk2bw60pZrrHcLdaI46mEuMO6WR7IC7v02OcWsz/wgIr1alMIUQUYUogAYRMJhAyiYTCAowFKnCAiIgnKlVRFWRVRBZFVEFkVUQWUZUIglQiIiFwdX32o07Qw17LbNX0LJMVhpzmSCLB/cazHzgHGQDkDJ5xhd57GvY5jxlrhgj1C+W6u8I7HVUMNNpqw2ajmmlDJ6yVhc6ni/qdGzkOf5DPAznlB9Kt9ZTXGhgrKGeOopZ2CSKWM5a9pGQQVsLlaV0/b9LWGks9nh6NFTN2tBOS4k5LifMkkkrrIIRSiAiIgIiICIiAiIgIiICIiCVKhEVKhEQEREEooRAUqEQSiha1yq/gqN85DTtLR87treSBknBwOUG0i5EN9pXNjEzmte9xaDG7qM/kG5Dsdsn+x9Csv6zSGR0YMnVDg0R7DucTuxgf+Bx+gyg6ShaEV0jlirJGQzbaYAnczaXZYHcA89iO6q26s/T/iJIy2QMyY85wdm/GceiDpKPJaMF0p5GOMj2ROBcAxzhucAMkgdysIvdO6SEMjncyWKSYOEZPDNueO5zvHZB1UXPju1NL0+l1Hl+eGsLtuHbSTjtz/AO57Aq1ruMVxg6kIPHDh3DT6Z9f/AJQbqlQiApUIgIiIJRQiCVCIglFCIJRSiCEUogjCKUQRhFKIIRHgljgCQSO47heWtsVyo7JE3FQHspqcbABkPJ/cJ+Ukkefc/flB6lY54WTx7JMlu5ruPUEEf3C4NH+rTyM68lREz5AcMaMjMm4nI74DPyoYb1LM2MyPiLoRhxjBAPS5J4wD1PLPpxhB0zZ6TrCRrXNIcXYB45duI/OT9z5FYobFRwuDouox7S0scDyzG7GPs9w5znPK0IJL28SSVIfHH8sgYxgLgx72/L2/kxrXf+Yd1klZc5qV5aZS8dIxNkAaHfvE5dgZ/gGZ+/CDrQ0MMUcrGmQtlaGu3OLicN25yec4A/Cwy2mmkdlxl27dpYHnaflLM49dpI/HosU0le2zRSRtldWtwHsIGXHlp9sAnPHkAufNFdJWESGbMbXtEgaA9wa6Lngf1YecDug7sVHFFFJGzcGvJJIcQefQ+S0/0Kj2EEPJcHhxz/IO25yMY/ob5eXuc69jjrYakNmMxp5HVTyHgfKetlmPPlpJWKE3B9XEZTVOjiqck7Q3c0tkGNuOwJZzkjscjlB0YrVTxFhjfMxzc5LX43Au3YOPLPpjzHZZbfbqe3hwpWljXY3NzwSPP6+/sFwYX3N4Y+p+Na1smcsjBcAY+3LRkbvPH9kP61HvLQ9j5Hlzi1u4b9kWMcH5c7/Qe4QepTC4l8bcJKyF1DCHMpWiYuc8t3ku/i0AHcdrXDnH8wudJX3Ewzyxy1Gxs8kbj024aBOGNDOOTjcPP38kHrEWpanTOpSZzKfndsMrQ15bnjIH/wB4xnlbiCEwpRBCKUQQilEEIpRBGUypRBGUypRBGUypRBGUypRBGUypRBGUypRBGUypRByKS+QTn5gBlu4CN3UI+bbggDIJPl9fRbQulIQcSnI2ZGx2QXOLWjGO+QRjyxysQs9O1rek+WJ7WhoewjPDtwPbGQSfs4qv6JTiaOQSTgt2FwDhiQtcXAu475ce2O/0QTT3yimigeZHRmWNsga9hGNwJAJxgHAP/RC3qedlRAyaLcY3jLS5paSPoeVzYrBSRvjc0yHYwR87eQAQ3nGeAccegzldSGNsMLI2Z2saGjPoEFsplSiCMqMDGMDHfCsiCMplSiCMplSiCMplSiCMplSiCMplSmEBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBETKCUUoghFKIIRSiCEUoghFKIMFbVQ0VHPV1T+nTwRulkfgna1oyTx7BeIh8X9CTTthi1BE6VxDQ3oS5ye39C995L5rptjXeOetmkcG3UI448noPpKL4Lp+6Xur1HafDyaurzXWe7z1VfWGpeZJqGPEkO92ckSdVjcZ/p5Xmqq+VEdk13dDrG6w6iteop4rZQC4Ha9nVaGsEJP7jTlwxggBvGOch+n15vVGuNN6VqIqe/3anpKiVu9sRDnv25xuLWgkD3PHB9F8b1neL5W6s1g2ou5tM1sbF+nl97dQx07TGHCXohhE4c7IOSf8vHC7t4mEGrorrTastNm1a6zwfqFLcoc0VVHgnMb3FrgA7d/Ek4xkIPsVsr6S6W+Cut1RFU0c7A+KaJ25r2nzBWyvz9b9QR3qzeHtJKTpLTVyfWfFC2ymkikljJ2NbIMFjHu3uxkE5xz3W7eJ/i6fRdrsGsr1WUFRfZ6OWvZUOZK6MMdmPqYHUA7B/PqDxwH2alu1JVXeutkL5DWUTY3ztMT2taHgluHEbXdj2Jx5rclkZDE+SVzWRsBc5zjgADuSV+edcXG52WPxVo6G8XRkVrorOyke6rkc+HO0OcDnO539R7u81vNfHUanrbLY9TXS+2mv09NV1xNxdKYJWkbHte0/IXdiwYGD2wg+50FZTXCjhq6GeKopZmh8csTg5j2nsQRwQs6+feAUVJF4U2E0VU+o3wNdLuqDL05NoywZJ2AcfKMAenK+hIIRSiCEUoghFKIIRSiCFKKUBERAREQEREBERAREQFrRUNJFXTVsVLAysna1ks7YwJJGt/iHO7kDJxnstlEHkdI6NNlv13v1zuBul7uWyN9SYGwtjhZ/GNrQTjyyc84HosOkPD62WGpuVVVR0txrKm61F0gqJaVokpTKWksY45PG3uMZ9F5/wAWq29xansNPb5tS09pNPUSVT7HS9aR8mWCNpy1zR/UeccZXrPDe50d00hS1FBda66Rh0jHz14a2oDw87mSNAADm9sY7Ad+6g6txsNoudZT1dytVBV1VPzDNPTskfHzn5XEZHPPCm8WK03tsTbza6G4NhJdGKqnZLsJ7kbgcdgvg1l8Qbpep6fUMmortbaee4dCOH9MElsgi6uxsM0gAPUcMHeDxuHby+7ampbpW2aensVwjt1fIWhtU+ES9MZG4hp4JxnGeMoM9wtFtuNu+AuFvpKqhwB8PNC18fHb5SMceShlmtjI6KNluomx0J3UrRA3FOcYzGMfLxxxhfOPCTUFVctU3+3096rrxY6SKJ0Ut0Y2KrZM7O9uzaxxZjBBc0egzyvd33Ven7BURwXy9W+3zSN3sZU1DYy5ucZAJ7ZQaGudF0WqNPXi3xCCgq7o2Js9bHTtdI8Rva5u7sXYAwMnjK7dss9utb6h9voKSllqXb53wQtYZnf5nEDk9+SvEeHfiRSX+8VWn7hX2ue8w7pIJrfOJIKyHPD24JLXgfyYTkYyMjt9GQaVrtdvtMMkVroaWiikkMr2U8TY2uee7iGgZJwOfZbqIqCIiAiIgIiICIiAiIgJhSigjCKUQRhFKIIwilEEJhSiCEwpRB5nVVivVwr6Ou0/qKS0zwMfHJDJT/EU8zXY5dHub8wxw4H2WhpbRdXpqioae33txJrpq+6ySUrS64Pkznz/AG+dvbPDQF7VEHyp3hPUCilsUOonRaOkrPjDbBRtMjR1Op0hMXfw3c/xz5ZXqrzp693Klu0LdUVFGZ545qCSmp2sdRhuMsdz+61xHIOODherRB4bS2ia2h1jU6o1DexdbvJRCgj6NIKaKOEP3/xDnFzieck/+y9jUUVLUuDqinhlcBgGRgcQPuthEHktGaMgsFXW3OtnbX3ytd+7VdIRtjjz8sUTB/Bg44zknkk+XrMKUQQmFKIIRSiCMIpRBGEUogjCKUQQpwiICKUREIpRBCKUQQilEELivrqynuVSKiSB9JF0zhkJDwHuIBLtxHy4yTj17LtrXq6OKphnjcNvXZ05HNAy5vPHI9z+UVzBf4zKP2H9D9omUOHaV5aw47nOAfbKvQX2GqhdKYKljPkLS2F79wcCR/Ee3PkOOeVtttlKKw1JiaZNrGgEDDQzOMD7rPT01PAXfDxMYTgHaPTsP79vdBzH3sNbPmnc18cnTDHuDXO5dzjvztJGAcj740/8RvbI6R1OXQSGNsDW/wAnF0fUJd6ccfVdhtDQOj6jYYSx37gcAMefIP8A4nfk+qv8BSGNrBTxbG4LQGgYw3aMfbj6IMdTXBlqkq4hh/w7p2MkGDw3PI7+YytV18iZVyQyRODWv29QHcP5hhJxxwXDz4HfGF0ZaKmmZGyWCJ7IxhoLRho9B7cBYzRUW+RroIS6Zrg4ED5mk/Nx6Enn6oNAXxrpoI44cmZm9hLxj+LnDd/lBDfP180pLlVbDFNG2as3AdJrDFtBaTzuJB7EAgkH7FdJ9DSvk6j6eJz8YyWj0Lf9CR9CqfptGGFnw0W0nPbzxj/Q4+iDnXC9upGPldTuEUb3tIJBL9sTnkDnjsBk58/qtiluUs9eKY02wt3CXLwdpAYRjHfIePRZq230dVTPo5WMaJWPwG4DgC3aSPs7H3WanpaWPBgijG1xOWjz7Hn7Y+yDmy3+COudSGKQztcGFvuXhrR9wS76ArFTXirmbDspmSb3MaMO2l26IvPB/jyPUrrmnpTUbjFEZ8iTOBuzgtDvwSMqY6Snjc10cMbS3AbhuMYBA/sSPugmkmbU0sM7AQ2VjXgHuARlZVEbGRxtZG0NY0BrWjgADyVkRCKUQQilEEIpRBCKUQSiIqp9kUoghFKhA+yKUQQilEGheKeWpoTHDvDt7T8rtpwCD59x7cZ9QtCitUzJGGZrRmaOaTZI7BxBsxycnDhnnuu6pQeafaKyWllgqB1XyQCNshncBGOjtII8/nyfvnuAty5W+apomRwbonMppGsAkLdshDdh4Plgrsog87La60OkbGSY9z+hiYt6RJaQ73xh3H27ErDWWivlme+A9J/zh0nWJ6jTMx2APL5GkeXp25XqFCDhvt1abNT0/VeZGSlzwX5c5mXYbngcZbx7Yye61xarixnUZJuqBgN6k7sEdAtwSP8Ajwcjnz7r0qIPICy3Lc9zwX4EzYf3ywx7mxbTx5BzHnue+eStyW1V5rYntmcImybgGPxtPVLiT9WkD7Y88r0aIOHZLfVUtY6Wq+ZxgbG+TqF3UeHEl2PIHI/08gu2pRBCIiAilEEfZFKhARSiCPsn2UogIpRQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQETCYQETCYQETCYQETCYQFSeMTQyRlzmh7S3cw4Iz5g+RV8JhB8Jp9I7/GSr027Umqv0yOyMr2j9Xl39UzbD82e2PJd2XxWnotYUNprqWy/D1dx/TWR012bPWRkktbJJE1uA0kDI3ZGV7aLSsUfiDPqsVMhnltzbcafaNoaJN+7PfPlheOt3g+yhNup2ahrHWu3XRt0pqM08Qw8Pc/D5AN7/AORAJPAPY8YDzcOoZ6irohb3VtG1viBLb5wa2SXrtaxxP8j8rCcftj5Rhds+MJipLT8TamtrX3SagusLZcigZFMyN0pOOR+7CfL+Xsu3R+GNLTSte24zuLdSSajwYx/vHtI6X/Lz37rHWeE9sqr3q64yVk4OoKYwGING2mc4M3SM9XF0bHc+YQcer1x+s3uyF9BI2mbqiW1Us0NY+MSiONwMrg3h7Sdw2nI4VKbxXvT6SC7TabphYf1Y2madtaeq13W6YkDNmC3t55zn6r0tD4bUdFZ9H2+Gtm2adqvi2PLBmoeQ7du9Ml5Ko3wzpW6Pdp/9Qn6Trp+qdbpjdu6wl2Y9MjGUHmdea9vlfYddDTVpBtVmjnoZri2s6U7Z2x5c6NgHZhcCTuBxyOeF39aaortMeBr7/SOMlxittOWSSDeepJsZvOe+C/dz3wsd/wDCplxqNQsodQXG22u/F0tdQwNYWPmLcF4cRubkgFwB+bGDxwvYVOm6Gt0h/hy4sNTb3UjaSQH5S9oaG547HjPHYoPH2TQF4tFTZrlbtYXiqq2vY65R3GoM8FVGR84azsx2f4kdvdeH1ZNc6LUt/qNX3fVtjxWbrTdKAOktsFNkdPqMZxnOQ4PHOV7y1eGU0VbaDe9U3W8260StmoaKobG0Me0YY6R7RukLR2yq3rwxnuD7vTU+qrtSWK7zOmrLc1kbwS/G9rJHAuY047DgZQed8RX3V2qIqm9VGp36Sdb2Glq9MuIayfu+SVrCXkYwW9249eV9G8Oaptbom0zsvjb810XFxEfTM4BIBc3Jw4djnnIOcFci5eH87bi2r0tqO4afzSxUckEMbJonRxjDCGvB2uA4yPQL0GitM0WkNM0VktZkdTUzT88rtz3uc4uc5x9SSSg7aJhMICJhMICJhMICJhMICJhMIJREQEREBERAREQEREBERB84m8TJ6W503x+lbtR2Kpr226K51Do2Zlc8saTCTvDCRw7HbyX0ckAEkgAd18F1xJe9SagtctBpTUFFq2318fR+IJmtLY2vJMpef2zlpPLQHg8DsF7/AFsL5q3Teo7FpyKW01rJW0nxde10cc8Tmhz3RObkkEEtzj180EaF8SKPWOp7xa7fRTMpaFgkhrXO+SrYXuZvYMfx3McAc84WS9eIMVs13bNNC0V8grJxTGvIDIGSGPqbWk8vIbgnHAyOc8LxOhrTq7T/AImXGWvs9ritNNZYYMW2OcsfHHvLI4C8AOkz3BPYjzXf1eK7UF68MbrTWm4xRiuNTURSwO30rXQn/egZ2cnHPmg6euvEmi0rqWx2MUctdWXGaNkvSeAKWN7wxr38Hu44A88H7+8X521do3xBo5H1hhsl0qbhqClrHzwiokmY1j/2muAbhsLB3x2BJyvvdVS1FdZn0s9S+kqpodj56N2DG8jlzC4Hse2Qg1rlqK22y9221V0zoKq4h4pi9hEb3Nx8m/tuIOQ3OTgrrr4h4ieH13uQotP0N+1RcpK53UfU1ssRpaNjC3MjiIwS/kbWtIOecgBfZbXSvobbS0slTPVvhjbGZ5yDJIQMbnEADJQbSIiAiIgIiICIiAiIgIiIJwmERAwmERAwmERAwmERAwmERAwmFjqZmU1PLNMdscbS9xxnAAyVr/qNLvgb1cGeMzMJBA2gA5J8uD5oNzCYWnHcqKSWOKOrge+RnUYGyA7m5xkevPCyuq6dj9jqiFr8E4LwDxnP4wfwUGfCYWA1dOKX4o1EIpsburvGzHrnssX6nQ5lHxlOOiGmTMgG0O/iT9fJBuYTC1Zq+jgcRNV08ZAyQ+QDA455PuPyFY1dO14Y+ZjHF/TaHOA3OwDgZ790GxhMLUq7hBSvDZi/ONx2RucGN9XYBwODyfQ+iqbnTNM2XPDYQS95jds474djBI9Ag3cJhYaaoZUxb42yNGcYkjcwj7EArMgYTCIgYTCKEE4TCIgYTCIgYTCJlBCK33T7oKorfdPugqitj3Ty7oKorfdPugqit90IODg8+WUGtcKf4ugqabds60bo92M4yCM4XLFh2yTPZVOy8vDN7d2xrs4b37Aud9iB5LHS11dBTvNTNBJtjdM55YRyXODWgA8AYH/XKpTXutqaRskcdO1zZBE/dk5JlMYIwT7HufTPmgzxWR8QBbUt6gycujLueoJB3dnGR68+ys6yb46kSzgyTNA3NjxtIlfJ65x8wGM+Xda0d9rJZ4IWQQ7z8jySAC7e9mRkg4BZnHJ5x9UeoZZGF7I4g1zOqwkkbWZDMu9MO3E/8IQdBtscy2/DRzBji8yFwDiCS7J7uz989/wtemsj6eKBsdRGTDsI3xEglrHM5+bkYd68EdysM12rHU1V0uiDBTzSGRgJ3luQCzJx39c9iPdZK+vq5rAailkZT1D52REFu4xbpGsLTz/IZ+np6oMsNkjjjYwyNftcDkxjkCHp47/f+3ukFofBNHIyaJxDQx3Uh3ZADRxzwfl9/L0WrLe6puWRNp3PdI2NvJPT/ebHh/ud2R27Ee66FwrZ6aVwaYdkcBle54PJ5AAx2Gf+vMBkqqWofUPmpalsLpIxG7dHvIwSQW8jB+Y98jt99eS1yOqppmzxM3tc07Yf557bxnDsfQH39dCPUFSQzc2nBDS5zM/NJiQswwAkZOAe59PcXjutS74dnWhbmSNrg8/uPDpCPlHbGBjt69sch1bXRuooZGGRrg55cGtaWtYMAYaCTgcZ+63FyLjcHwfFRNna2o6sQhZxuLXFo4Hnzu/ut61yvnpN8rtzupI3PsHuA/sAg2UVvun3QVRWx7p90FUVvun3QVRW+6fdBVFb7qUBERUFHoiIJREQFHqiIJREQQeylEQE9ERACjyREE+aeqIg0GgfrknA/wC7t/8A2ct/zREEeinzREBPREQR6qSiICjyREE+aIiAiIiv/9k="}, {"timing": 1500, "timestamp": ************, "data": "data:image/jpeg;base64,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"}, {"timing": 1875, "timestamp": ************, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAIDAQEBAAAAAAAAAAAAAAECAwQFBgcI/8QAQBAAAQMDAwIEBQMCAwUIAwAAAQACAwQFEQYSIRMxB0FRYRQicYGRFSMyQqEWUrEIM2Jy8CQ0gpKiwdHxVbLh/8QAFwEBAQEBAAAAAAAAAAAAAAAAAAECA//EACERAQEBAAICAQUBAAAAAAAAAAABEQIhEjFBAyJRYXET/9oADAMBAAIRAxEAPwD9CZHqmR6qMp9lROR6pkeqj7J9kROR6pkeqj7J9kE5HqmR6qPsn2QTkeqZHqo+yfZBOR6pkeqj7IgnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoK/dPuq5HumR7oLfdaVzutJbBF8XJIHSktjZFE+V7sDJw1gJwB3OOFt5HuuFqW1VdfJDPbqhsM7IZqZ25zmfJIGnLXN5a4OYw+4yOM5AdmWqgi6nUnjaY273guGWt9T7KKGsp6+jhqqKZk1PK0PZIw5DgRkFeTtejm0l7rK6peKmSoMjjUPlk34exjS0x52EfIOfQNGMjK72mKB9p0/b7dN0i+lgZAXxAgP2tA3Y8icZxz9Sg2q+50VvpKuqrKqKKCkYZJ3ud/u2gZyfssrKunf09lRE4yjLMPHzfT1XkqvSUlXQ6nojPFBS3eCaJrQDLskk35l5AI/kCW5IyMjHZXu2mqy5V1sqHS08LKV1O8xsLsNMU292MAbtwwPm4bycHKD0lbdKGhifLV1kMTGENcXPHBJwAfuthtRC5zGtmjLnjcwBwy4eo9QvMVGk4H2G4UcLKVlXVVclWZzD/ACc6cygO8zxhv0Cw1mkm1moH3OVsbZJImMa+OeRrqYtY9uGAYa4fOcZAxl3fOAHqKWvpquaeKmnbK+AgSBvIaSMjnskdfSyT1EDZ2dWndtlaTgt+VrvxhzefdcDQ+nZNPQ1LZvgwZWwtDaWMsb+3E2PJz5nbn24HOMnUrtJtqr7cayrhpJ6Oqe+SRrI8TyNdStgMJdnG04Lu/fb6ZQeyDmuLgHAlpwQD2WM1EIdI0zRh0Yy8bhlo9T6Li6Lt9Xb7FH+quElzqHGeqeAAS8gAA4yCQ1rG5z/SuFPocPmukrRA6eqlfK2aWSR+8OnZMYnx527fkDCRztA475D18F0o6hkL4JxI2aV8LC1pOXMLg7y7AtIz27eoVY7vQSw1UsVXFIylcWzbDuLCO4wOc/6rz9Fp2qo6K0RRsod1HX1Fa4M3RtAkdKdjBtPYS9+P49ueMzrBKy26go4m0rm3J8j43Oy3bvaAQ4YOcHJHr7d0HpG1ELnMZ1Wh7xuawnDiPp3UxTRTA9KVj8HB2kHC8dLpKSpukNwmlgiqI46OMOYC5zBA+Uu2uIH8hJjt691Hh5ZKuzS3I1dNDAx7KaGPptDeoIotpeQCeT/7Y5xkh3hqWzm7SWwV0fx8c7aZ0ODkSOjMob282NJz249V0RV05YHiohLDn5g8Y47/AIXzTUPhhLdtTV12iuxpDVzyveI2HdtdR/Ds5z/Jji9wPo8hc2DwjqI7BTW9tXbo3Q1wri4QOc17mxCMM25GGOx84Hccc90H1enutDU3CpoIKuKSspg0zRNd8zA7OM/gq1dXw0UlKyXqOfUSiJgYwuOT5nHZvHJ7Dhect+l/0/W12vtLHbmtuEDGn9jEscrc5IeO7XZBPmSAtfUVrr7jVaRE9FFU11FWMqamuia1kcQax28NDnbxuO3AGfc8IPZMmike9jJWOezhzQQS36+i06m80FNeKS1T1Gyvq2udDFsd84aCTzjA4BOCfJee0dpaXT99vtYJacU1xlMwgiaSQ8ve5zi53zc7/wCOSAckYzhbGorRdbhqWw11DU0MVHbpXSSMlje6R+9jmO2kEAfK44yO/twg7Vvu9BcauvpqKpZNPQyCGoa0H9t5GcE9s4PksUt/tsN6itMtQ5ldLxGx0Tw152l20PxtJ2gnGc4Xl9MaNrtLtvptV4dUyVpidAa6JrgwtaA5z9jWlxPPOee5yck9Cpst1qddUt2qKqhltVLGW09M+N/Uhe5pD5AQcFxzjJBw3IGMkkO3er3b7JHTyXSpEDKiZlPGSxzt0jjho4Bxk8ZPCxx6gtcl+ksrKtpuTAXOh2uHZrXEB2MEhr2EgHIDgfNcLV9jvl+03FQMrbbHVCtZUPkfC/YWRziWNoAdnPysBOeecY4xq23R1ZB4gv1LVVlI4yRuD2RxPDnF0UTC3lxaGgxZBxuOQCeEHvPun3VMhMhBf7p91TITI9EDJTJVM+x/CA+x/CJq+SmSq59j+E+x/CC2UyVX7H8J9j+EFslMlV+x/CfY/hUWyUyVX7H8J9j+FBbJUZKj7H8KPsfwgtynKr9j+Ez7H8Ii3KHKpkeh/CZHv+EFslMlVyPQ/hRx7/hBfJTJVOPf8Jx7/hUXyUyVTj3/AAnHv+EF8lMlU49/wnHv+EF8lMlU49/wnHv+EF8lMlUBHv8AhMj3/CgvkpkqmR7/AITI9/wgvkpkqmR7/hMj3/CDKApAUqQMqqjCkDKnCsAiK49lOPZWwpwoKYU4V8LWuNZT26hnrKyTp00LS+R+0naB3OByisuFGOey0qC82+4Wf9VpKpklv2ueZsEABudxwRnjBVbBfbbqCkfVWeqbUwMeY3ODS3DgAcYIB7EIN8hVI5XMsGpLTqF1SLPWNqvhiGylrHANJzjkgZ7HsusRyqjHhQQuZT6itVTqCpscFW110pmdSWDY4FreOc4x/UPPzXVKYaooVioVxNQoKlFcNQiEJn3Q0RQVr0lfR1ck0dJV088kJ2ytikDjGeeHAdjwe/omGtlAgKlMNQilRlTAwiAomKIpRTEQilFBmAUgKQFYBFQArgIArAIqAFOFKkBBGFr3CkirqCopJ27oZ43RPHqHDB/1W1tTaivz9Zri+z+GOq7NM4sq4qkU4Ye/7nyuaPsx62tGSTaIGrrZWOLJWW5lWwHt1NgHH/ikaPsu7qDQVzrdevqIacGy1NTFUTP6jRjA+bjOc8u8vNZ/FfRt3vV3grbJB1hLT/D1Deo1mAHbhncRnOfL/KoPN6cu0mgvCaCtpoY3XK61TjD1RwB23O9gG5H/ADLaZra+afvFp/WL9Zb3QVsgjnZRuZvpicc/L3Az3Oc4I44XrtdaHfd9G2+2250fxVtDOj1OGvAbtIP17/ULjUFpvtdcqGObRmnrZTRuHxU0sUUnUHGdgbyD3x/qqObYnMg/2gdUzynbGyg3OPoA2ElaNn1H4g62p7nedM1NvobdSyujgo5Ig985ADtpcQecEc5AyfLuvV27S1xHizf7tV0mLRW0fQbL1GneS2MEbQdw/i7yXmLDZteaFpLnZbDaqa5UtTK6SlreuxvScQG7i1x9ADg8Z8yqjo611/f9OaEtE1XaoqfVNxk+HEDnB0bHDu/gnvluGk8F3PbniV+q9daBvtj/AMaVluutqucnSf8ADxhjoHcZwQ0ZxuB5zkAjjuu1rjQuo9SaHtHxdfDU6pt0pqN21rGPJPLBgADGG845I8s8cW76e1t4i3uxM1RZ6ay2q2y9WZzZmyGZ3GQ0Bx77cD0yTk9le06ZtQas1nUeMNy0ppuroY4fhGviNVEC2A7GOc/IG5x5IA7ZcPILj6W1X4malmvemqKrtMN5s88jam5SxjDsEtaxrQ0tyXNcd23sBxnv6+j0reY/H2u1I+jxZZKIQsqOqzl+xgxtzu7g+Sjwv0rebH4g67ud0o+hRXOr6tJJ1WO6jepIc4aSRw4dwO6uVNjz+k/F+vPhLfL/AH2CGa6Wuf4VuwbGzvdt2EgdsF3OPJq81L4naws1lt+pKzUumbpBO6N89kgdGJ4Y3eQwd24diMnae+cFdbSXhVe6jwx1Zp+9UzLfW11YKmjc+Vj25aAWklhOASMHzwey17VpvWkdtt9kZ4daVhqabZFLd6tkErJWNGNxaPmJIAyeT34GeGU6fdPiX3bTnxVlnbHJWUnVpJ3tyGl7MscR5jkHC/PfgFDfKXX+sqqoutM2ho6p7ru3ojNS4dbDmHHygOBdgY44X6Po4WUtJBTxMijjiY2NrImbGNAGAGt8h6DyXxjQ2lNUaZ8RtUQVdljrNN6hqJHy1zKlgMUZ6hHyk7iTvAIx9Mq2dxJeq5dk1b4ma+orxqHSdTbbdaKOV8dPRywtfJUbQHbckHnBHOQMnA8yvpPg7rc690bHc5omQ10MhpqpjM7eoADlufIhwPtyPJfMtPWDxL8OrdeNOads9NdrfVzPko68VLGGAuAbuLXEc4DTjsCDglfSPBXRMug9Fst1ZIySvnmdU1JjOWteQAGg+gDR98qSVbY98DhMplMLWM6kcphQmSpi6sgUAqVMXRERTBtAK4CgK4WWgBWAQBWARUYUgKQFcBQVwsNZUwUVLJUVcjYoIxlz3dgtjCx1NPDVQPgqY2SwvGHMeMg/UKjhHV9g/wDykH9//hdG2XKjusDprfUMnia7YXM7A4Bx/cLxF8s1vj8QrPTR0VO2lkiy+JsYDXH5+48+wXR1BVP09e7FQWaKnpaStqAJ2RxNAdlzG59jgoY9gQqELyupb3X0Os7Jb6aRraWqx1WlgJPzEd/Jcusv2ob/AKjr7Zpd9LSU9CdstTOzducDjHY+YOOPLurGce8IVHBeNGo79b9G3a4Xq1Njr6HiPH8JuQN2M5wO59uy81pzUurrk+hrKWvsdzjqHjrW5j2xy07fM84Ofz91qVLH1QhVK+Y661vcxq//AA1p+stttkiiElTX17gGxkgENGeM4LfI9/LBK19I6/udZJqSzXWWgqLpbKOSpgraJwdFO0N7+mcub29wQMLUsZsr6muVqG/2vTtC2svdbFR0zniMSSZwXEEgcfQr4tbNYeI990DU362zW2GG3GR88kkY6lQG/MQ1uNoAbj0JOcFaPi5qWTV/gNZrxPA2Cea4NbIxhy3c0StJHscZx5Z81fLpPHt+iI3tkja9h3McA4EeYKkr4JqTWniDoqks+oLxFan6fqjHEbfECZIQW5Ac/Gd+0HkEjIPC6/iP4lXCLVdv0xpWttVvmqacVM1zuLw2OFrmlzQM8ZLQO4Odw481rYmV9jCL434d+I11rNR3jS+oKu119dSUrqmmuNucHRTgAEg4OM/MOwHYri+Fer/EbW9HBX/F2ymtFDWj46pljDX1EY2ufExoaQMN5zxy4c8K+UPGvvqL4NpLXHiJrirul+01Da22CgldGy3ztIkqsDdtD8Eh+0jnIblw4PK9P/s/ayu+tdOXOtvs0cs0NYYY9kYZhmxpxx7kqSymWPqaKFKtiakFFVSs2NaspVQpWaqUQKcLLTdAVgFACuFhtICthGhWaoJAU4RWAQQApwpwpwg8pdrVVz61tlfFCXUsMe18m4cH5vLOfMLBruz19bPbK+1xtlqKKTf0yQM8gg8+7f7r1MNZTTzyQQzxvmj/AJsByW+XKoK6kdWOpG1ERqR3iDvmHGeyo8HVWu/XPVVnutbQshihcA6NkjT02g5yTnknJ7egWP8ATL3pfU1yrbRbxc6Cvd1HRiUMc12SfP0JPl2K99LXUjK1lI+ojFU8ZbEXfMR9PsVmKsia8Zb4tWSacuL7g6iNzlJNNTysBYxueWuLe+RkDk+WfNfNrvo68Xp1BDS6Ugstwika6avhqGtjI9QwHjnB4yeF9tuNyore6FtdVRQGY7YxI7G8+g/IWwVucdY8sfH9b6NroNcHUVFZKbUNHUxBlTRTOaHBwaG7m7uP6WnIye/qs+mdPXN8F/qajS1msgnpJYKOGmY01B3N7OeDtx28hz6Y5+qOXnLlrPTdurTSVt6oYagHa5jpRlp9Hen3W5xnti8r6eG0Hpa82vwdvFkrqIxXOeOqEcHUYdxezDeQccn3Xj7voDU0/gTadPxWtzrvDcHTyU/WjBazMnO7dt/qHn5r7+yqgkpWVMc8Tqd4Dmyh4LXA9iD2Vg4OaHNILSMgg5BW/wDOVjzuvgGt7D4ia1t9o0tcLJS0lHSysdPdG1DXMl2gtDw3ORwSdvJJ9FteI/hzXU+t7ZqWy2Gl1FQx0zKartk72tLtjNjXDdx/Hb6kFvY5X3Urli/Wp18dZm3CmN1a3e6kDx1A3Gc7foQVb9OfJPqX4fNdAadujrrdLjWaKsWm6Q07o6OKFjHVZc5uDmRpwB3zkA8j0Wz4AaUumm9AVtq1JRGlnnrZX9IyNfmN0bG5y0keRX1IrmWG/Wq/08lRZLhTV0Eb+m98Dw8NdgHBI88EKzhInna+IaCsOv8AQ77xp/TsdnrrNLVF7bnJUtIpCQAXuYHZyGhpLSO47kd+h/slNI0XeiTuH6kQHev7bF6a5+C2lq2urKinlutuZWuLqinoqsxxS57gtIPHJ47ei9vpfT1s0vZobXZKYU9HFkhuSS5x7ucTySVmcLK3ecsdZSFCLdjKURFixYkKVVXXOtxAVuVXzVlmtN8cq4CqFkaubokBXCgBWA5UEgK2ECkIGEwpwpQeQ2m3a7J4EVZGSfbj/wCW/wB15+klcy9017OQyetfGQfJpAx/Zx/C9Zq+31NT8JUUMbn1ETiPl74P/wBf3WpXWSU6OgpooiauMtlDR33E8/2J/Co5lE9s+tbxdJgehb4nDjuMDHH2Dlz5NVXuWklucEtqjpWEkUj5P3XNB/Ofx9F6PTtlmFjuUdcwxVVcXh5PcAjA/uSfuvLwWmsoaV9HLpeGuqmuOypc4Fjhnz/+wqjQ8SLoy5W3S1ygjcQ+Vz+n55Bblv5BC2bjqnUunL3QHUcFEbZWv2AU+SYeR5+oyPXPktnW2nrlUW3T8Fto4mTQPc6QU4/biJLTnnyzlc7UNBqPWN3tlHcLT+nUVJLvmm6gcHdskfYYAGe/K1NTp9GukU01vqoqV/TnfE5sb/8AK4ggH8r852F1g0vR1to8Q9M1LKyokdtrXQiQlp4+VxPGDzlmc5X6NucMtRb6qGlmNPPJE5kco7xuIIDvseV8hddtf0FrqbNdNMC9yO3sZVucHMcDnGQBgj6447rpY5SvOa9tbLd4KbNJXYXDTjp+rUPqCDJzJHtYzDRjDwSQcHkr1ugptYUHhbBO2kobpV/CwfpdNFIIx0ixuOoXYGQOeDz2z5rlUPh1eqfwTvFjkDDdqyYVbKfeCG7TGdme24hn0yRz5rSutu1jqDwaFiistRba63dCAxmbBrYWNIOO2OQ04zzj7KyWXf0lssz9tSq8StW6Tv1qZqqr07X0tdKI56Wgk3TUgJAJOOxGfPIOCM+a5mo6m8U/+0rcG6bp6ee6zUbYofiSRFHmFpL345wADx5nC5uptFXe622zfoPh+6zMpZ2dcmQPnmdjk5POwY8+5cPRep1NZNX0HjlW6psFldXUkVM0Br5BG2oHSa1zA7yd5jju1O77/K9T1+HY8PNeai/x9WaL11DSC5sjM1PUUrcNkGN2PQjbkg4HYgrwvgXqmDRng3qa91ETpuhX7Y4gcdSRzIw0Z8hk8n0yvVeH+ndTX/xYqtb6qtbrNFDB0aWlc8OcSW7MeuAC4kkDJIx7ef0N4Y32u8GdR6eudK+23KeubU0wnxhxa1mM47A4Iz5d1fu9/wBT7fX8SfFjWNmoKHUV4qtL11qqnsL7ZR1ANVCxwyDgHggepdjzx5foeiqY6yjgqoDmGeNsjD6tcMj/AFX5ttGnby6ht1pHhFam3SIsiqLjXPzA5oGC87TnJ7nDj54BzhfpGhp2UlFT00bGRshjbG1kYw1oAwAB6LXDflOefDOpUKVqswRSOUws1pAVx2VcKwXOtwwpRSsq6DeyuFRqyBcXVcKzVUK7QgspCgKwQSERCgFVPZWUO7KjGVjd3WQqjlqM1jcsTllcsbl0jnWJyxOWVyxldY5ViKo5XcqOXWOdVUHlSoWmVcIpTyUtagilMLFagERThS1uQUomFi1qA7qUAUrFagEUosNOi1XaqBXC5Oi4V2rGCrtQXCsFRWBQWRAiDBWvLKchmd7ztaB3yf8A+ZKx0jyYnsc1zXRuLcO747j+y2iAcZHbsqnGScclUcgVNT8N1TK3iATY2f2+il9VKKkfO3HW6XRx82P82f7/AEXTLG4xtGMYxjyWNzG79+1u7tuxytRmvOz18zHMqBKyV3wskvSaMBhy3g8845/BUPraxz4YWzMBknEfULWkgGNzsYDiPIY+q7wjY1xc1jQ49yB3WMRRsADI2NAORhoGD6rpHOuKyrqzWgmVhh+KdTmPZzjaSDn1yFmu1U+O2PnpJYQ47dj3uG05cBwc4zg8c4zhb742HI2jk54Hn6rBFSww0jKZjB0GMEYYeRtAxhdY5WvLNuUpr6SeSueII4KrqsMYAc6Nze4BIJAPkfI47laNVdaqWPpvq2sLJ6V4mc1g29RzsghriMfKDgnPPK9qYImtY0RMDWfxAaMN+nosQpKdrCxtPEGHu0MGP+uStxi15aS7VUTpph/2p9JFVBjmDDZ9oiIOB6Elp9wV0tP1lVVPqWVMjZGsDHMkBjB+YHIIY5wxwCCe+fbK7LIY4w0RxsaGjDQ1oGB7KsUEcILYYmRtJyQxoAJ9VWdjxr9QXKKTpO6b8n4APazk1m8jJHk3bh2FldeK6epq2EtZEH1EBjOwFoY12HA7t5ccA/xxh3tk+uEUec9Nmd2/+I7+v1VTTwmUy9KPqkbS/aMkemVmtyvOaUr6iU0dPLVR1jJKFs5exoHRPygNJyc5ye/Pyn7enVIaeKHd0Yo49xy7Y0DP1WRS1UYUqcJgrNreIVlAClZtaEUoFmtJREWWo6AVwsYVwuTa4VwqAqwQXClVClBYKVUKSUGGqmMYYGAF73bRnsOCefwtFtXM0FuA6Qvf5OcAGkccD3XQkY2RhbIA5p8isJpKcsDOk3aCSAPfuqjWFc9zmx9PbK4sIa7yaRkn6jDh+Fqm4TSw7hGWNe1r2uDXDb8w4ORycHy911OjE17HiNocxu1pA7D0WEUlO0uLYWAnvx75/wBVqJWka+TqtcY2dB8r4W8ncHN3cn2+UrSfdqmOmZJJFCXSxNmjDXHABc0EH/zDn6rrPpIOq6URNErs/NjnkYytantlLTwCNsLD8rWuOO+OR/flbjnXObc6plTtnjhMbZjA8szknYXgj2xgYWj+vVTqEVLaZu2Rsb4ste0Dc9rdpJGCcOzkeh+/o3U0O8u6bd2/qZx/VjGfrjhagtlEzdspohuxkY9DuGPTnldI53HKfW3F1ZTQ5pmFtaYJsAkPb0TIMenB/I9Fe7XGrp6yWGkjpnCKm+JcZnluQCQWjH07+S6klJTvcXPiaXdQS5894AAd9cDH0WnW2ekra01FXEyYGIRbHtyMAkn7HPI9luOfTisvNSZ68QvaXOqIxCyVj3lrTAx5AawEnk+wGfsudV3q7Vlvr6mllhghdQU08bC0l0Zkc8Ow71+U/wBu3K9dUWuhqHOdLTRuc5+9xxgl23bn/wAox9FUWigbG1gpIgwRCANAwNgOQ36AqkxyW3WqpXbXRxOpaeoiopTvcZHPfsG4ZzwC8dySRk59Yjut0mioTHFRtfWSObGHFxDQ1rjk+pO3t7rtSW6jfWtq3U8ZqRgiTHOQMA/XBxlXZSU7OjtiYOiSY8D+OQQcfYlRqOVY7rVVk0LayGCNtTStqohE4ktHGQ4nufmHb3+p7mFhhpIITEYomMMUfSYQP4s4+Ue3AWdYtakSmFKLOtihSUUWIUhApWaoiIordBVwqBWCw6LhXCoFYKCwKsCqKUF8plVB9VOUBQUJUFVK5jK+QTSCWP8AaE/REgI4JxgY+p7qlTcg2GEta5plgMwII+XG3j/1LZ+AgE5k+fJf1C0uO3d64WD9KpQ7OJD8pYAZCQ1pIJA9OwWojXjrql1whgEbHRPdNudnBAY8Af6qLld46CUxzRknczGD3a7dl32DHH7e63TRwtkZI0OD2Oc4EH/McuH0JWKqoaepm6s0e5/TdFnP9Lu//XufVbjnXGud6niidJSRNeQDta93yuAlDN3bPOeFZl1nZWzR1FP+wKllP1GvHyOcxhAx3PzOIz7hb77TRmnZAY3dNkIgb8xyGgg9/XIByjrdTlpDg5xdMyckuOS9oaAf/SFuVixzbtcpqC7QA4NCIi+fjlg3AB+fQZ59snyXNbqjoUreuxks5fO4jqNj/bZK5gxnu7AGB7ckL08lJDJUGaRgc8xmI55BaTkgj7LmRadoIKaKCnE8LY2vY0xzOa7a925wJzk8/ceS1rOMQvZkrvg4KVzqgxmdgc/a10W3h+ccZcduO/c9lhgu9TJo2K6VMbYah8LZCIiCOccjI9+3K6bLTSsrBVNa/wCI3bt+8kn5du0/8OAOO2RnvytOtsDHWoUNJLJFGGCJofI5zWs3AnAzyQBgZ7JqYtSXf4m8VFD0BH0sjLpWh5xjnZ32nPDhn7LrALl1NkikklnjmnZUlsnSe6RzhC54wXNGf7fjC6sbQxjWgk4GMk5P5UtakAFOFKLNrUiFKkJhZ1rEKcIijWIwpwiKBhMIiNRtqwVFYLDS4KsCqKQoMmVKxq2UFlWXf0z0i0P8i4ZCZUqjn0NdI9sLqsxjruc2PYDjLc5ySfQFV/Wad3LGTOYGCVzw3hrDnDj7cFVraBxtrKOnJyX56pIBYN2SfxkfdUmtr5a6pxI6Klkgjh2sx8wBdkduOCB9yqjZ/UYDV9AB/wDLZvx8u7vtz6/2WlSXVz+rJVOp2Rxxl8jASJIfZzT3+o9PNZ2WqGOsdUMwNzzIW7G/yI/zYz79+6wyWhs3/eqqebEToW52ggOxk5A5PA5ViVaa7MjiY6Slq2vfJ02xmP5nEgkY5x5evHnhYXXdjHBhhnlle6TayKPJwxwBzz/xD/rAWwbe57oXz1U0zopRK3IaAMNLcYA9yVpz2uX9QgkpqiSFrWzlz27Scvcw4wR24P4C1GbFrfd2V9xmp4IZDA2COdlRxteH5wMdx2/se3Gc9yrhQwiQwTzZONsTQSB5kkkAflaYtD6JmbXM6OTpRU/7mCNrHE7u38sOf7HKzXe1R3J8D5JCx0O7b8jXD5gBnDgRkY4PktaxY1Xaho+q1sbKiSMtieZWM+RrZP4k85x9uFjkvrH3GlpqaGV0c1Q6nM7mfIS1jy4NPqCzHPvjOFkgsFPBTSQtllLXwwwknGcRdj27nPKRWJkVXBIyqnEEFRJUsg+XaHvDt3OM4+dxAz5/RWVMY73WXGkraGKkNJ0qqbojqscS07HOzwRx8v8AdRV32Om+Ihmjlimjhke2R8f7cjmNy7aM5OO/OMjsV0q2hjq5qOR7nNNLN1mhvmdjm4PthxXHl0pSy1E0rp5B1DOSGsYCeqHA5djJxuOMnjhPIkZjfYoKuthrAWCGR7WOaMhwZCyQ598OOPZqNv0E8lMynDwZOi52+PsJN2G9+HfKc98cd1lrLBTVfW6kkoMtU2qJGOCGBhb27FoIP/MUp7DTwbtskpLqz4zJI4d5N7fxClq4xQ6koZGbnsqYmmn+JjL4j+6zgHaBkkguaMeeRjI5Wa0XR1wra2F1PJT/AA/T+WVuH5cCeeSPIdvVYptO08sNJGZp2/DUhpI3NIyASwh3b+QMbSPJblttnwdTVVElTLUTVO3e6QNAG0YGAAMLOtY30CnCn7qNIwqq/wB1H3QQFJHopAUqaKBThWRTVZh/ZSq5UgqNLAqwKplSCguCpyqZUoLZU5CplMoLZQlVyoRE5UFFUkogSqFSe6grSWOZW3CZlYaWhpPip2RiWQGQRhjSSG8kHJO08e3JC05L7I6qjp6WglfMYG1D4pXiJ4a4kbWg/wAnDacjgDjnlbdXQ1IuD6ygqIopJIhFI2WMvaQ0ktIwRgjc76rTudlqq6mEE1ZBM3pBm+emDnsfzmRhBGCePoQFqVMZTdZXXWWkp6TqshexkrhM0PbuAO4MPdo3DJz64BwusuLX2aesq4nvngLIpY5I5HQZnjDSCWh4I4dg5OOxI5XcTUxQorEKMJq4hMKwCKaYjBU4RSpq4qpU4TCmriMKMK2EwggIpwoUURThMILqVAUoGVKIDlFSmfdEQTlMqEQTlQiwSVlNFVxUslRCyplBMcLngPeB3Ib3KIz5VT3WNtTA6pfTMmidUMaHPiDxvaD2JHcBZFRChSSoBBGRgj2QCoWvR11JXGcUdTBUGnldBN0pA7pyN7sdjs4ZHB5WympiqFYGVtLJVyUkdTA6qjG58LZAXtHqW9wFBrqQVwojVQfGlnUFP1B1NmcbtvfHummNhMLHVVENJA+eqmjhhYMukkcGtaPcngKKSpp6ynZPSTxTwP5bJE8Oa76EcJpjLhMLCK2lNaaMVMBqwzeYBIN4b67e+PdY6e50FVVS01NW0s1TF/vIo5Wuez6tByFDG0ilAioRWUIIRArIKorKAghFZMIJRFKAmURAypyoRFTlMqERA8r4f4xWiuvXixpqKzVBprvBbKmqopc4Amje1zQfZ2C0+zivuC5lRYbbU6gpL3NT7rpSRPghm3uGxj/5Dbnac+4RXweg8Q3/AKxrnVtHROFfTWGlbJRyNOYKhsj2Oa4d8NdyfYL1WkLlrWruTqCvrbm2irba+VtwraKmidSVHGHRhjiHxnJ4cD2HK+hU+jtP091u9yitkIq7vH0q4kuLZ24xhzCdvPOcDnzyuZQeGmlLfT1sNNbHhtXTmkkc6qme8QHvExxeXMb7NICDwXh3qzUOv4L0ypu7Le2228UxFNExxqpju/7WNzeGENwAOMk+gXpf9n2Cam8J7E6qr5Kls8ZfEyRrWiFuSOm3ABIyCcnJ5K9fSaVstHcqK4UlCyGroqP4CF8b3NDYPKMtBw4DHGQcLm0Xh9pq3UlNT0Ft2RUlb+o00bqiVzIqgAgOaC44HJ+UfL7IPnbr7qn9K1J/h4NM0erJ6aY0lPAKgUrQC7pMcA2STty7c4+/l73wsvkl809UvqLnPcKimrJKaR1TRfCTxEYPTlYON4DhkgAHPbuudp3w4gfYbjTaybS19ZX3SW7PNIZIm08r8Y6T8h4xjvkHnC9fp3T9s03bjRWalFPTmR0rhvc9z3u7uc5xLnOOBySTwiPkVBDadAeLOqKqnglFJTacFbPhxklnkMxLnEnkucfP/QLzPh5qG01Pi7ZLzW3ZlRe7zTVAqw1j9sEj3RiGnblo4a0Yz6557L9CR2K2x6imvrKfF1lpxSPn3u5iDtwbtzt7+eMq01lt898prxLBuuVNC+CKbe4bWPILhtzg5IHJGUHA8SabTM1jhr9YPY+1W6cVPSedzJZAC0NLMHf3OG+ZXM8ILJNaLZd7jPQx2alu9YayntbQGijj2ta0OAADXOxktHAyB6rqXDw20pcbJS2irtZfbqaofVQxNqZWbJXklztzXg93HzwM8JR+G+laOzXC1Q26Q0FeYzUxSVk8m8sO5vLnkjB9CM+aD59W2y2ac8cLtNF1qeGbStVXVk7HF8pcagl7wT5gAYHYYAXltC0TrLcvDO4XO0UFqoJnvgpbjRNb8TXPlYREKgAnaHA5PL+f8q/Qc+nLTUagN7no2yXI0ZoDK5ziDAXbiwsztIz54z7rk2bw60pZrrHcLdaI46mEuMO6WR7IC7v02OcWsz/wgIr1alMIUQUYUogAYRMJhAyiYTCAowFKnCAiIgnKlVRFWRVRBZFVEFkVUQWUZUIglQiIiFwdX32o07Qw17LbNX0LJMVhpzmSCLB/cazHzgHGQDkDJ5xhd57GvY5jxlrhgj1C+W6u8I7HVUMNNpqw2ajmmlDJ6yVhc6ni/qdGzkOf5DPAznlB9Kt9ZTXGhgrKGeOopZ2CSKWM5a9pGQQVsLlaV0/b9LWGks9nh6NFTN2tBOS4k5LifMkkkrrIIRSiAiIgIiICIiAiIgIiICIiCVKhEVKhEQEREEooRAUqEQSiha1yq/gqN85DTtLR87treSBknBwOUG0i5EN9pXNjEzmte9xaDG7qM/kG5Dsdsn+x9Csv6zSGR0YMnVDg0R7DucTuxgf+Bx+gyg6ShaEV0jlirJGQzbaYAnczaXZYHcA89iO6q26s/T/iJIy2QMyY85wdm/GceiDpKPJaMF0p5GOMj2ROBcAxzhucAMkgdysIvdO6SEMjncyWKSYOEZPDNueO5zvHZB1UXPju1NL0+l1Hl+eGsLtuHbSTjtz/AO57Aq1ruMVxg6kIPHDh3DT6Z9f/AJQbqlQiApUIgIiIJRQiCVCIglFCIJRSiCEUogjCKUQRhFKIIRHgljgCQSO47heWtsVyo7JE3FQHspqcbABkPJ/cJ+Ukkefc/flB6lY54WTx7JMlu5ruPUEEf3C4NH+rTyM68lREz5AcMaMjMm4nI74DPyoYb1LM2MyPiLoRhxjBAPS5J4wD1PLPpxhB0zZ6TrCRrXNIcXYB45duI/OT9z5FYobFRwuDouox7S0scDyzG7GPs9w5znPK0IJL28SSVIfHH8sgYxgLgx72/L2/kxrXf+Yd1klZc5qV5aZS8dIxNkAaHfvE5dgZ/gGZ+/CDrQ0MMUcrGmQtlaGu3OLicN25yec4A/Cwy2mmkdlxl27dpYHnaflLM49dpI/HosU0le2zRSRtldWtwHsIGXHlp9sAnPHkAufNFdJWESGbMbXtEgaA9wa6Lngf1YecDug7sVHFFFJGzcGvJJIcQefQ+S0/0Kj2EEPJcHhxz/IO25yMY/ob5eXuc69jjrYakNmMxp5HVTyHgfKetlmPPlpJWKE3B9XEZTVOjiqck7Q3c0tkGNuOwJZzkjscjlB0YrVTxFhjfMxzc5LX43Au3YOPLPpjzHZZbfbqe3hwpWljXY3NzwSPP6+/sFwYX3N4Y+p+Na1smcsjBcAY+3LRkbvPH9kP61HvLQ9j5Hlzi1u4b9kWMcH5c7/Qe4QepTC4l8bcJKyF1DCHMpWiYuc8t3ku/i0AHcdrXDnH8wudJX3Ewzyxy1Gxs8kbj024aBOGNDOOTjcPP38kHrEWpanTOpSZzKfndsMrQ15bnjIH/wB4xnlbiCEwpRBCKUQQilEEIpRBGUypRBGUypRBGUypRBGUypRBGUypRBGUypRBGUypRByKS+QTn5gBlu4CN3UI+bbggDIJPl9fRbQulIQcSnI2ZGx2QXOLWjGO+QRjyxysQs9O1rek+WJ7WhoewjPDtwPbGQSfs4qv6JTiaOQSTgt2FwDhiQtcXAu475ce2O/0QTT3yimigeZHRmWNsga9hGNwJAJxgHAP/RC3qedlRAyaLcY3jLS5paSPoeVzYrBSRvjc0yHYwR87eQAQ3nGeAccegzldSGNsMLI2Z2saGjPoEFsplSiCMqMDGMDHfCsiCMplSiCMplSiCMplSiCMplSiCMplSmEBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBETKCUUoghFKIIRSiCEUoghFKIMFbVQ0VHPV1T+nTwRulkfgna1oyTx7BeIh8X9CTTthi1BE6VxDQ3oS5ye39C995L5rptjXeOetmkcG3UI448noPpKL4Lp+6Xur1HafDyaurzXWe7z1VfWGpeZJqGPEkO92ckSdVjcZ/p5Xmqq+VEdk13dDrG6w6iteop4rZQC4Ha9nVaGsEJP7jTlwxggBvGOch+n15vVGuNN6VqIqe/3anpKiVu9sRDnv25xuLWgkD3PHB9F8b1neL5W6s1g2ou5tM1sbF+nl97dQx07TGHCXohhE4c7IOSf8vHC7t4mEGrorrTastNm1a6zwfqFLcoc0VVHgnMb3FrgA7d/Ek4xkIPsVsr6S6W+Cut1RFU0c7A+KaJ25r2nzBWyvz9b9QR3qzeHtJKTpLTVyfWfFC2ymkikljJ2NbIMFjHu3uxkE5xz3W7eJ/i6fRdrsGsr1WUFRfZ6OWvZUOZK6MMdmPqYHUA7B/PqDxwH2alu1JVXeutkL5DWUTY3ztMT2taHgluHEbXdj2Jx5rclkZDE+SVzWRsBc5zjgADuSV+edcXG52WPxVo6G8XRkVrorOyke6rkc+HO0OcDnO539R7u81vNfHUanrbLY9TXS+2mv09NV1xNxdKYJWkbHte0/IXdiwYGD2wg+50FZTXCjhq6GeKopZmh8csTg5j2nsQRwQs6+feAUVJF4U2E0VU+o3wNdLuqDL05NoywZJ2AcfKMAenK+hIIRSiCEUoghFKIIRSiCFKKUBERAREQEREBERAREQFrRUNJFXTVsVLAysna1ks7YwJJGt/iHO7kDJxnstlEHkdI6NNlv13v1zuBul7uWyN9SYGwtjhZ/GNrQTjyyc84HosOkPD62WGpuVVVR0txrKm61F0gqJaVokpTKWksY45PG3uMZ9F5/wAWq29xansNPb5tS09pNPUSVT7HS9aR8mWCNpy1zR/UeccZXrPDe50d00hS1FBda66Rh0jHz14a2oDw87mSNAADm9sY7Ad+6g6txsNoudZT1dytVBV1VPzDNPTskfHzn5XEZHPPCm8WK03tsTbza6G4NhJdGKqnZLsJ7kbgcdgvg1l8Qbpep6fUMmortbaee4dCOH9MElsgi6uxsM0gAPUcMHeDxuHby+7ampbpW2aensVwjt1fIWhtU+ES9MZG4hp4JxnGeMoM9wtFtuNu+AuFvpKqhwB8PNC18fHb5SMceShlmtjI6KNluomx0J3UrRA3FOcYzGMfLxxxhfOPCTUFVctU3+3096rrxY6SKJ0Ut0Y2KrZM7O9uzaxxZjBBc0egzyvd33Ven7BURwXy9W+3zSN3sZU1DYy5ucZAJ7ZQaGudF0WqNPXi3xCCgq7o2Js9bHTtdI8Rva5u7sXYAwMnjK7dss9utb6h9voKSllqXb53wQtYZnf5nEDk9+SvEeHfiRSX+8VWn7hX2ue8w7pIJrfOJIKyHPD24JLXgfyYTkYyMjt9GQaVrtdvtMMkVroaWiikkMr2U8TY2uee7iGgZJwOfZbqIqCIiAiIgIiICIiAiIgJhSigjCKUQRhFKIIwilEEJhSiCEwpRB5nVVivVwr6Ou0/qKS0zwMfHJDJT/EU8zXY5dHub8wxw4H2WhpbRdXpqioae33txJrpq+6ySUrS64Pkznz/AG+dvbPDQF7VEHyp3hPUCilsUOonRaOkrPjDbBRtMjR1Op0hMXfw3c/xz5ZXqrzp693Klu0LdUVFGZ545qCSmp2sdRhuMsdz+61xHIOODherRB4bS2ia2h1jU6o1DexdbvJRCgj6NIKaKOEP3/xDnFzieck/+y9jUUVLUuDqinhlcBgGRgcQPuthEHktGaMgsFXW3OtnbX3ytd+7VdIRtjjz8sUTB/Bg44zknkk+XrMKUQQmFKIIRSiCMIpRBGEUogjCKUQQpwiICKUREIpRBCKUQQilEELivrqynuVSKiSB9JF0zhkJDwHuIBLtxHy4yTj17LtrXq6OKphnjcNvXZ05HNAy5vPHI9z+UVzBf4zKP2H9D9omUOHaV5aw47nOAfbKvQX2GqhdKYKljPkLS2F79wcCR/Ee3PkOOeVtttlKKw1JiaZNrGgEDDQzOMD7rPT01PAXfDxMYTgHaPTsP79vdBzH3sNbPmnc18cnTDHuDXO5dzjvztJGAcj740/8RvbI6R1OXQSGNsDW/wAnF0fUJd6ccfVdhtDQOj6jYYSx37gcAMefIP8A4nfk+qv8BSGNrBTxbG4LQGgYw3aMfbj6IMdTXBlqkq4hh/w7p2MkGDw3PI7+YytV18iZVyQyRODWv29QHcP5hhJxxwXDz4HfGF0ZaKmmZGyWCJ7IxhoLRho9B7cBYzRUW+RroIS6Zrg4ED5mk/Nx6Enn6oNAXxrpoI44cmZm9hLxj+LnDd/lBDfP180pLlVbDFNG2as3AdJrDFtBaTzuJB7EAgkH7FdJ9DSvk6j6eJz8YyWj0Lf9CR9CqfptGGFnw0W0nPbzxj/Q4+iDnXC9upGPldTuEUb3tIJBL9sTnkDnjsBk58/qtiluUs9eKY02wt3CXLwdpAYRjHfIePRZq230dVTPo5WMaJWPwG4DgC3aSPs7H3WanpaWPBgijG1xOWjz7Hn7Y+yDmy3+COudSGKQztcGFvuXhrR9wS76ArFTXirmbDspmSb3MaMO2l26IvPB/jyPUrrmnpTUbjFEZ8iTOBuzgtDvwSMqY6Snjc10cMbS3AbhuMYBA/sSPugmkmbU0sM7AQ2VjXgHuARlZVEbGRxtZG0NY0BrWjgADyVkRCKUQQilEEIpRBCKUQSiIqp9kUoghFKhA+yKUQQilEGheKeWpoTHDvDt7T8rtpwCD59x7cZ9QtCitUzJGGZrRmaOaTZI7BxBsxycnDhnnuu6pQeafaKyWllgqB1XyQCNshncBGOjtII8/nyfvnuAty5W+apomRwbonMppGsAkLdshDdh4Plgrsog87La60OkbGSY9z+hiYt6RJaQ73xh3H27ErDWWivlme+A9J/zh0nWJ6jTMx2APL5GkeXp25XqFCDhvt1abNT0/VeZGSlzwX5c5mXYbngcZbx7Yye61xarixnUZJuqBgN6k7sEdAtwSP8Ajwcjnz7r0qIPICy3Lc9zwX4EzYf3ywx7mxbTx5BzHnue+eStyW1V5rYntmcImybgGPxtPVLiT9WkD7Y88r0aIOHZLfVUtY6Wq+ZxgbG+TqF3UeHEl2PIHI/08gu2pRBCIiAilEEfZFKhARSiCPsn2UogIpRQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQETCYQETCYQETCYQETCYQFSeMTQyRlzmh7S3cw4Iz5g+RV8JhB8Jp9I7/GSr027Umqv0yOyMr2j9Xl39UzbD82e2PJd2XxWnotYUNprqWy/D1dx/TWR012bPWRkktbJJE1uA0kDI3ZGV7aLSsUfiDPqsVMhnltzbcafaNoaJN+7PfPlheOt3g+yhNup2ahrHWu3XRt0pqM08Qw8Pc/D5AN7/AORAJPAPY8YDzcOoZ6irohb3VtG1viBLb5wa2SXrtaxxP8j8rCcftj5Rhds+MJipLT8TamtrX3SagusLZcigZFMyN0pOOR+7CfL+Xsu3R+GNLTSte24zuLdSSajwYx/vHtI6X/Lz37rHWeE9sqr3q64yVk4OoKYwGING2mc4M3SM9XF0bHc+YQcer1x+s3uyF9BI2mbqiW1Us0NY+MSiONwMrg3h7Sdw2nI4VKbxXvT6SC7TabphYf1Y2madtaeq13W6YkDNmC3t55zn6r0tD4bUdFZ9H2+Gtm2adqvi2PLBmoeQ7du9Ml5Ko3wzpW6Pdp/9Qn6Trp+qdbpjdu6wl2Y9MjGUHmdea9vlfYddDTVpBtVmjnoZri2s6U7Z2x5c6NgHZhcCTuBxyOeF39aaortMeBr7/SOMlxittOWSSDeepJsZvOe+C/dz3wsd/wDCplxqNQsodQXG22u/F0tdQwNYWPmLcF4cRubkgFwB+bGDxwvYVOm6Gt0h/hy4sNTb3UjaSQH5S9oaG547HjPHYoPH2TQF4tFTZrlbtYXiqq2vY65R3GoM8FVGR84azsx2f4kdvdeH1ZNc6LUt/qNX3fVtjxWbrTdKAOktsFNkdPqMZxnOQ4PHOV7y1eGU0VbaDe9U3W8260StmoaKobG0Me0YY6R7RukLR2yq3rwxnuD7vTU+qrtSWK7zOmrLc1kbwS/G9rJHAuY047DgZQed8RX3V2qIqm9VGp36Sdb2Glq9MuIayfu+SVrCXkYwW9249eV9G8Oaptbom0zsvjb810XFxEfTM4BIBc3Jw4djnnIOcFci5eH87bi2r0tqO4afzSxUckEMbJonRxjDCGvB2uA4yPQL0GitM0WkNM0VktZkdTUzT88rtz3uc4uc5x9SSSg7aJhMICJhMICJhMICJhMICJhMIJREQEREBERAREQEREBERB84m8TJ6W503x+lbtR2Kpr226K51Do2Zlc8saTCTvDCRw7HbyX0ckAEkgAd18F1xJe9SagtctBpTUFFq2318fR+IJmtLY2vJMpef2zlpPLQHg8DsF7/AFsL5q3Teo7FpyKW01rJW0nxde10cc8Tmhz3RObkkEEtzj180EaF8SKPWOp7xa7fRTMpaFgkhrXO+SrYXuZvYMfx3McAc84WS9eIMVs13bNNC0V8grJxTGvIDIGSGPqbWk8vIbgnHAyOc8LxOhrTq7T/AImXGWvs9ritNNZYYMW2OcsfHHvLI4C8AOkz3BPYjzXf1eK7UF68MbrTWm4xRiuNTURSwO30rXQn/egZ2cnHPmg6euvEmi0rqWx2MUctdWXGaNkvSeAKWN7wxr38Hu44A88H7+8X521do3xBo5H1hhsl0qbhqClrHzwiokmY1j/2muAbhsLB3x2BJyvvdVS1FdZn0s9S+kqpodj56N2DG8jlzC4Hse2Qg1rlqK22y9221V0zoKq4h4pi9hEb3Nx8m/tuIOQ3OTgrrr4h4ieH13uQotP0N+1RcpK53UfU1ssRpaNjC3MjiIwS/kbWtIOecgBfZbXSvobbS0slTPVvhjbGZ5yDJIQMbnEADJQbSIiAiIgIiICIiAiIgIiIJwmERAwmERAwmERAwmERAwmERAwmFjqZmU1PLNMdscbS9xxnAAyVr/qNLvgb1cGeMzMJBA2gA5J8uD5oNzCYWnHcqKSWOKOrge+RnUYGyA7m5xkevPCyuq6dj9jqiFr8E4LwDxnP4wfwUGfCYWA1dOKX4o1EIpsburvGzHrnssX6nQ5lHxlOOiGmTMgG0O/iT9fJBuYTC1Zq+jgcRNV08ZAyQ+QDA455PuPyFY1dO14Y+ZjHF/TaHOA3OwDgZ790GxhMLUq7hBSvDZi/ONx2RucGN9XYBwODyfQ+iqbnTNM2XPDYQS95jds474djBI9Ag3cJhYaaoZUxb42yNGcYkjcwj7EArMgYTCIgYTCKEE4TCIgYTCIgYTCJlBCK33T7oKorfdPugqitj3Ty7oKorfdPugqit90IODg8+WUGtcKf4ugqabds60bo92M4yCM4XLFh2yTPZVOy8vDN7d2xrs4b37Aud9iB5LHS11dBTvNTNBJtjdM55YRyXODWgA8AYH/XKpTXutqaRskcdO1zZBE/dk5JlMYIwT7HufTPmgzxWR8QBbUt6gycujLueoJB3dnGR68+ys6yb46kSzgyTNA3NjxtIlfJ65x8wGM+Xda0d9rJZ4IWQQ7z8jySAC7e9mRkg4BZnHJ5x9UeoZZGF7I4g1zOqwkkbWZDMu9MO3E/8IQdBtscy2/DRzBji8yFwDiCS7J7uz989/wtemsj6eKBsdRGTDsI3xEglrHM5+bkYd68EdysM12rHU1V0uiDBTzSGRgJ3luQCzJx39c9iPdZK+vq5rAailkZT1D52REFu4xbpGsLTz/IZ+np6oMsNkjjjYwyNftcDkxjkCHp47/f+3ukFofBNHIyaJxDQx3Uh3ZADRxzwfl9/L0WrLe6puWRNp3PdI2NvJPT/ebHh/ud2R27Ee66FwrZ6aVwaYdkcBle54PJ5AAx2Gf+vMBkqqWofUPmpalsLpIxG7dHvIwSQW8jB+Y98jt99eS1yOqppmzxM3tc07Yf557bxnDsfQH39dCPUFSQzc2nBDS5zM/NJiQswwAkZOAe59PcXjutS74dnWhbmSNrg8/uPDpCPlHbGBjt69sch1bXRuooZGGRrg55cGtaWtYMAYaCTgcZ+63FyLjcHwfFRNna2o6sQhZxuLXFo4Hnzu/ut61yvnpN8rtzupI3PsHuA/sAg2UVvun3QVRWx7p90FUVvun3QVRW+6fdBVFb7qUBERUFHoiIJREQFHqiIJREQQeylEQE9ERACjyREE+aeqIg0GgfrknA/wC7t/8A2ct/zREEeinzREBPREQR6qSiICjyREE+aIiAiIiv/9k="}, {"timing": 2250, "timestamp": ************, "data": "data:image/jpeg;base64,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"}, {"timing": 2625, "timestamp": ************, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAIDAQEBAAAAAAAAAAAAAAECAwQFBgcI/8QAQBAAAQMDAwIEBQMCAwUIAwAAAQACAwQFEQYSIRMxB0FRYRQicYGRFSMyQqEWUrEIM2Jy8CQ0gpKiwdHxVbLh/8QAFwEBAQEBAAAAAAAAAAAAAAAAAAECA//EACERAQEBAAICAQUBAAAAAAAAAAABEQIhEjFBAyJRYXET/9oADAMBAAIRAxEAPwD9CZHqmR6qMp9lROR6pkeqj7J9kROR6pkeqj7J9kE5HqmR6qPsn2QTkeqZHqo+yfZBOR6pkeqj7IgnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoJyPVMj1UZTKCcj1TI9VGUygnI9UyPVRlMoK/dPuq5HumR7oLfdaVzutJbBF8XJIHSktjZFE+V7sDJw1gJwB3OOFt5HuuFqW1VdfJDPbqhsM7IZqZ25zmfJIGnLXN5a4OYw+4yOM5AdmWqgi6nUnjaY273guGWt9T7KKGsp6+jhqqKZk1PK0PZIw5DgRkFeTtejm0l7rK6peKmSoMjjUPlk34exjS0x52EfIOfQNGMjK72mKB9p0/b7dN0i+lgZAXxAgP2tA3Y8icZxz9Sg2q+50VvpKuqrKqKKCkYZJ3ud/u2gZyfssrKunf09lRE4yjLMPHzfT1XkqvSUlXQ6nojPFBS3eCaJrQDLskk35l5AI/kCW5IyMjHZXu2mqy5V1sqHS08LKV1O8xsLsNMU292MAbtwwPm4bycHKD0lbdKGhifLV1kMTGENcXPHBJwAfuthtRC5zGtmjLnjcwBwy4eo9QvMVGk4H2G4UcLKVlXVVclWZzD/ACc6cygO8zxhv0Cw1mkm1moH3OVsbZJImMa+OeRrqYtY9uGAYa4fOcZAxl3fOAHqKWvpquaeKmnbK+AgSBvIaSMjnskdfSyT1EDZ2dWndtlaTgt+VrvxhzefdcDQ+nZNPQ1LZvgwZWwtDaWMsb+3E2PJz5nbn24HOMnUrtJtqr7cayrhpJ6Oqe+SRrI8TyNdStgMJdnG04Lu/fb6ZQeyDmuLgHAlpwQD2WM1EIdI0zRh0Yy8bhlo9T6Li6Lt9Xb7FH+quElzqHGeqeAAS8gAA4yCQ1rG5z/SuFPocPmukrRA6eqlfK2aWSR+8OnZMYnx527fkDCRztA475D18F0o6hkL4JxI2aV8LC1pOXMLg7y7AtIz27eoVY7vQSw1UsVXFIylcWzbDuLCO4wOc/6rz9Fp2qo6K0RRsod1HX1Fa4M3RtAkdKdjBtPYS9+P49ueMzrBKy26go4m0rm3J8j43Oy3bvaAQ4YOcHJHr7d0HpG1ELnMZ1Wh7xuawnDiPp3UxTRTA9KVj8HB2kHC8dLpKSpukNwmlgiqI46OMOYC5zBA+Uu2uIH8hJjt691Hh5ZKuzS3I1dNDAx7KaGPptDeoIotpeQCeT/7Y5xkh3hqWzm7SWwV0fx8c7aZ0ODkSOjMob282NJz249V0RV05YHiohLDn5g8Y47/AIXzTUPhhLdtTV12iuxpDVzyveI2HdtdR/Ds5z/Jji9wPo8hc2DwjqI7BTW9tXbo3Q1wri4QOc17mxCMM25GGOx84Hccc90H1enutDU3CpoIKuKSspg0zRNd8zA7OM/gq1dXw0UlKyXqOfUSiJgYwuOT5nHZvHJ7Dhect+l/0/W12vtLHbmtuEDGn9jEscrc5IeO7XZBPmSAtfUVrr7jVaRE9FFU11FWMqamuia1kcQax28NDnbxuO3AGfc8IPZMmike9jJWOezhzQQS36+i06m80FNeKS1T1Gyvq2udDFsd84aCTzjA4BOCfJee0dpaXT99vtYJacU1xlMwgiaSQ8ve5zi53zc7/wCOSAckYzhbGorRdbhqWw11DU0MVHbpXSSMlje6R+9jmO2kEAfK44yO/twg7Vvu9BcauvpqKpZNPQyCGoa0H9t5GcE9s4PksUt/tsN6itMtQ5ldLxGx0Tw152l20PxtJ2gnGc4Xl9MaNrtLtvptV4dUyVpidAa6JrgwtaA5z9jWlxPPOee5yck9Cpst1qddUt2qKqhltVLGW09M+N/Uhe5pD5AQcFxzjJBw3IGMkkO3er3b7JHTyXSpEDKiZlPGSxzt0jjho4Bxk8ZPCxx6gtcl+ksrKtpuTAXOh2uHZrXEB2MEhr2EgHIDgfNcLV9jvl+03FQMrbbHVCtZUPkfC/YWRziWNoAdnPysBOeecY4xq23R1ZB4gv1LVVlI4yRuD2RxPDnF0UTC3lxaGgxZBxuOQCeEHvPun3VMhMhBf7p91TITI9EDJTJVM+x/CA+x/CJq+SmSq59j+E+x/CC2UyVX7H8J9j+EFslMlV+x/CfY/hUWyUyVX7H8J9j+FBbJUZKj7H8KPsfwgtynKr9j+Ez7H8Ii3KHKpkeh/CZHv+EFslMlVyPQ/hRx7/hBfJTJVOPf8Jx7/hUXyUyVTj3/AAnHv+EF8lMlU49/wnHv+EF8lMlU49/wnHv+EF8lMlUBHv8AhMj3/CgvkpkqmR7/AITI9/wgvkpkqmR7/hMj3/CDKApAUqQMqqjCkDKnCsAiK49lOPZWwpwoKYU4V8LWuNZT26hnrKyTp00LS+R+0naB3OByisuFGOey0qC82+4Wf9VpKpklv2ueZsEABudxwRnjBVbBfbbqCkfVWeqbUwMeY3ODS3DgAcYIB7EIN8hVI5XMsGpLTqF1SLPWNqvhiGylrHANJzjkgZ7HsusRyqjHhQQuZT6itVTqCpscFW110pmdSWDY4FreOc4x/UPPzXVKYaooVioVxNQoKlFcNQiEJn3Q0RQVr0lfR1ck0dJV088kJ2ytikDjGeeHAdjwe/omGtlAgKlMNQilRlTAwiAomKIpRTEQilFBmAUgKQFYBFQArgIArAIqAFOFKkBBGFr3CkirqCopJ27oZ43RPHqHDB/1W1tTaivz9Zri+z+GOq7NM4sq4qkU4Ye/7nyuaPsx62tGSTaIGrrZWOLJWW5lWwHt1NgHH/ikaPsu7qDQVzrdevqIacGy1NTFUTP6jRjA+bjOc8u8vNZ/FfRt3vV3grbJB1hLT/D1Deo1mAHbhncRnOfL/KoPN6cu0mgvCaCtpoY3XK61TjD1RwB23O9gG5H/ADLaZra+afvFp/WL9Zb3QVsgjnZRuZvpicc/L3Az3Oc4I44XrtdaHfd9G2+2250fxVtDOj1OGvAbtIP17/ULjUFpvtdcqGObRmnrZTRuHxU0sUUnUHGdgbyD3x/qqObYnMg/2gdUzynbGyg3OPoA2ElaNn1H4g62p7nedM1NvobdSyujgo5Ig985ADtpcQecEc5AyfLuvV27S1xHizf7tV0mLRW0fQbL1GneS2MEbQdw/i7yXmLDZteaFpLnZbDaqa5UtTK6SlreuxvScQG7i1x9ADg8Z8yqjo611/f9OaEtE1XaoqfVNxk+HEDnB0bHDu/gnvluGk8F3PbniV+q9daBvtj/AMaVluutqucnSf8ADxhjoHcZwQ0ZxuB5zkAjjuu1rjQuo9SaHtHxdfDU6pt0pqN21rGPJPLBgADGG845I8s8cW76e1t4i3uxM1RZ6ay2q2y9WZzZmyGZ3GQ0Bx77cD0yTk9le06ZtQas1nUeMNy0ppuroY4fhGviNVEC2A7GOc/IG5x5IA7ZcPILj6W1X4malmvemqKrtMN5s88jam5SxjDsEtaxrQ0tyXNcd23sBxnv6+j0reY/H2u1I+jxZZKIQsqOqzl+xgxtzu7g+Sjwv0rebH4g67ud0o+hRXOr6tJJ1WO6jepIc4aSRw4dwO6uVNjz+k/F+vPhLfL/AH2CGa6Wuf4VuwbGzvdt2EgdsF3OPJq81L4naws1lt+pKzUumbpBO6N89kgdGJ4Y3eQwd24diMnae+cFdbSXhVe6jwx1Zp+9UzLfW11YKmjc+Vj25aAWklhOASMHzwey17VpvWkdtt9kZ4daVhqabZFLd6tkErJWNGNxaPmJIAyeT34GeGU6fdPiX3bTnxVlnbHJWUnVpJ3tyGl7MscR5jkHC/PfgFDfKXX+sqqoutM2ho6p7ru3ojNS4dbDmHHygOBdgY44X6Po4WUtJBTxMijjiY2NrImbGNAGAGt8h6DyXxjQ2lNUaZ8RtUQVdljrNN6hqJHy1zKlgMUZ6hHyk7iTvAIx9Mq2dxJeq5dk1b4ma+orxqHSdTbbdaKOV8dPRywtfJUbQHbckHnBHOQMnA8yvpPg7rc690bHc5omQ10MhpqpjM7eoADlufIhwPtyPJfMtPWDxL8OrdeNOads9NdrfVzPko68VLGGAuAbuLXEc4DTjsCDglfSPBXRMug9Fst1ZIySvnmdU1JjOWteQAGg+gDR98qSVbY98DhMplMLWM6kcphQmSpi6sgUAqVMXRERTBtAK4CgK4WWgBWAQBWARUYUgKQFcBQVwsNZUwUVLJUVcjYoIxlz3dgtjCx1NPDVQPgqY2SwvGHMeMg/UKjhHV9g/wDykH9//hdG2XKjusDprfUMnia7YXM7A4Bx/cLxF8s1vj8QrPTR0VO2lkiy+JsYDXH5+48+wXR1BVP09e7FQWaKnpaStqAJ2RxNAdlzG59jgoY9gQqELyupb3X0Os7Jb6aRraWqx1WlgJPzEd/Jcusv2ob/AKjr7Zpd9LSU9CdstTOzducDjHY+YOOPLurGce8IVHBeNGo79b9G3a4Xq1Njr6HiPH8JuQN2M5wO59uy81pzUurrk+hrKWvsdzjqHjrW5j2xy07fM84Ofz91qVLH1QhVK+Y661vcxq//AA1p+stttkiiElTX17gGxkgENGeM4LfI9/LBK19I6/udZJqSzXWWgqLpbKOSpgraJwdFO0N7+mcub29wQMLUsZsr6muVqG/2vTtC2svdbFR0zniMSSZwXEEgcfQr4tbNYeI990DU362zW2GG3GR88kkY6lQG/MQ1uNoAbj0JOcFaPi5qWTV/gNZrxPA2Cea4NbIxhy3c0StJHscZx5Z81fLpPHt+iI3tkja9h3McA4EeYKkr4JqTWniDoqks+oLxFan6fqjHEbfECZIQW5Ac/Gd+0HkEjIPC6/iP4lXCLVdv0xpWttVvmqacVM1zuLw2OFrmlzQM8ZLQO4Odw481rYmV9jCL434d+I11rNR3jS+oKu119dSUrqmmuNucHRTgAEg4OM/MOwHYri+Fer/EbW9HBX/F2ymtFDWj46pljDX1EY2ufExoaQMN5zxy4c8K+UPGvvqL4NpLXHiJrirul+01Da22CgldGy3ztIkqsDdtD8Eh+0jnIblw4PK9P/s/ayu+tdOXOtvs0cs0NYYY9kYZhmxpxx7kqSymWPqaKFKtiakFFVSs2NaspVQpWaqUQKcLLTdAVgFACuFhtICthGhWaoJAU4RWAQQApwpwpwg8pdrVVz61tlfFCXUsMe18m4cH5vLOfMLBruz19bPbK+1xtlqKKTf0yQM8gg8+7f7r1MNZTTzyQQzxvmj/AJsByW+XKoK6kdWOpG1ERqR3iDvmHGeyo8HVWu/XPVVnutbQshihcA6NkjT02g5yTnknJ7egWP8ATL3pfU1yrbRbxc6Cvd1HRiUMc12SfP0JPl2K99LXUjK1lI+ojFU8ZbEXfMR9PsVmKsia8Zb4tWSacuL7g6iNzlJNNTysBYxueWuLe+RkDk+WfNfNrvo68Xp1BDS6Ugstwika6avhqGtjI9QwHjnB4yeF9tuNyore6FtdVRQGY7YxI7G8+g/IWwVucdY8sfH9b6NroNcHUVFZKbUNHUxBlTRTOaHBwaG7m7uP6WnIye/qs+mdPXN8F/qajS1msgnpJYKOGmY01B3N7OeDtx28hz6Y5+qOXnLlrPTdurTSVt6oYagHa5jpRlp9Hen3W5xnti8r6eG0Hpa82vwdvFkrqIxXOeOqEcHUYdxezDeQccn3Xj7voDU0/gTadPxWtzrvDcHTyU/WjBazMnO7dt/qHn5r7+yqgkpWVMc8Tqd4Dmyh4LXA9iD2Vg4OaHNILSMgg5BW/wDOVjzuvgGt7D4ia1t9o0tcLJS0lHSysdPdG1DXMl2gtDw3ORwSdvJJ9FteI/hzXU+t7ZqWy2Gl1FQx0zKartk72tLtjNjXDdx/Hb6kFvY5X3Urli/Wp18dZm3CmN1a3e6kDx1A3Gc7foQVb9OfJPqX4fNdAadujrrdLjWaKsWm6Q07o6OKFjHVZc5uDmRpwB3zkA8j0Wz4AaUumm9AVtq1JRGlnnrZX9IyNfmN0bG5y0keRX1IrmWG/Wq/08lRZLhTV0Eb+m98Dw8NdgHBI88EKzhInna+IaCsOv8AQ77xp/TsdnrrNLVF7bnJUtIpCQAXuYHZyGhpLSO47kd+h/slNI0XeiTuH6kQHev7bF6a5+C2lq2urKinlutuZWuLqinoqsxxS57gtIPHJ47ei9vpfT1s0vZobXZKYU9HFkhuSS5x7ucTySVmcLK3ecsdZSFCLdjKURFixYkKVVXXOtxAVuVXzVlmtN8cq4CqFkaubokBXCgBWA5UEgK2ECkIGEwpwpQeQ2m3a7J4EVZGSfbj/wCW/wB15+klcy9017OQyetfGQfJpAx/Zx/C9Zq+31NT8JUUMbn1ETiPl74P/wBf3WpXWSU6OgpooiauMtlDR33E8/2J/Co5lE9s+tbxdJgehb4nDjuMDHH2Dlz5NVXuWklucEtqjpWEkUj5P3XNB/Ofx9F6PTtlmFjuUdcwxVVcXh5PcAjA/uSfuvLwWmsoaV9HLpeGuqmuOypc4Fjhnz/+wqjQ8SLoy5W3S1ygjcQ+Vz+n55Bblv5BC2bjqnUunL3QHUcFEbZWv2AU+SYeR5+oyPXPktnW2nrlUW3T8Fto4mTQPc6QU4/biJLTnnyzlc7UNBqPWN3tlHcLT+nUVJLvmm6gcHdskfYYAGe/K1NTp9GukU01vqoqV/TnfE5sb/8AK4ggH8r852F1g0vR1to8Q9M1LKyokdtrXQiQlp4+VxPGDzlmc5X6NucMtRb6qGlmNPPJE5kco7xuIIDvseV8hddtf0FrqbNdNMC9yO3sZVucHMcDnGQBgj6447rpY5SvOa9tbLd4KbNJXYXDTjp+rUPqCDJzJHtYzDRjDwSQcHkr1ugptYUHhbBO2kobpV/CwfpdNFIIx0ixuOoXYGQOeDz2z5rlUPh1eqfwTvFjkDDdqyYVbKfeCG7TGdme24hn0yRz5rSutu1jqDwaFiistRba63dCAxmbBrYWNIOO2OQ04zzj7KyWXf0lssz9tSq8StW6Tv1qZqqr07X0tdKI56Wgk3TUgJAJOOxGfPIOCM+a5mo6m8U/+0rcG6bp6ee6zUbYofiSRFHmFpL345wADx5nC5uptFXe622zfoPh+6zMpZ2dcmQPnmdjk5POwY8+5cPRep1NZNX0HjlW6psFldXUkVM0Br5BG2oHSa1zA7yd5jju1O77/K9T1+HY8PNeai/x9WaL11DSC5sjM1PUUrcNkGN2PQjbkg4HYgrwvgXqmDRng3qa91ETpuhX7Y4gcdSRzIw0Z8hk8n0yvVeH+ndTX/xYqtb6qtbrNFDB0aWlc8OcSW7MeuAC4kkDJIx7ef0N4Y32u8GdR6eudK+23KeubU0wnxhxa1mM47A4Iz5d1fu9/wBT7fX8SfFjWNmoKHUV4qtL11qqnsL7ZR1ANVCxwyDgHggepdjzx5foeiqY6yjgqoDmGeNsjD6tcMj/AFX5ttGnby6ht1pHhFam3SIsiqLjXPzA5oGC87TnJ7nDj54BzhfpGhp2UlFT00bGRshjbG1kYw1oAwAB6LXDflOefDOpUKVqswRSOUws1pAVx2VcKwXOtwwpRSsq6DeyuFRqyBcXVcKzVUK7QgspCgKwQSERCgFVPZWUO7KjGVjd3WQqjlqM1jcsTllcsbl0jnWJyxOWVyxldY5ViKo5XcqOXWOdVUHlSoWmVcIpTyUtagilMLFagERThS1uQUomFi1qA7qUAUrFagEUosNOi1XaqBXC5Oi4V2rGCrtQXCsFRWBQWRAiDBWvLKchmd7ztaB3yf8A+ZKx0jyYnsc1zXRuLcO747j+y2iAcZHbsqnGScclUcgVNT8N1TK3iATY2f2+il9VKKkfO3HW6XRx82P82f7/AEXTLG4xtGMYxjyWNzG79+1u7tuxytRmvOz18zHMqBKyV3wskvSaMBhy3g8845/BUPraxz4YWzMBknEfULWkgGNzsYDiPIY+q7wjY1xc1jQ49yB3WMRRsADI2NAORhoGD6rpHOuKyrqzWgmVhh+KdTmPZzjaSDn1yFmu1U+O2PnpJYQ47dj3uG05cBwc4zg8c4zhb742HI2jk54Hn6rBFSww0jKZjB0GMEYYeRtAxhdY5WvLNuUpr6SeSueII4KrqsMYAc6Nze4BIJAPkfI47laNVdaqWPpvq2sLJ6V4mc1g29RzsghriMfKDgnPPK9qYImtY0RMDWfxAaMN+nosQpKdrCxtPEGHu0MGP+uStxi15aS7VUTpph/2p9JFVBjmDDZ9oiIOB6Elp9wV0tP1lVVPqWVMjZGsDHMkBjB+YHIIY5wxwCCe+fbK7LIY4w0RxsaGjDQ1oGB7KsUEcILYYmRtJyQxoAJ9VWdjxr9QXKKTpO6b8n4APazk1m8jJHk3bh2FldeK6epq2EtZEH1EBjOwFoY12HA7t5ccA/xxh3tk+uEUec9Nmd2/+I7+v1VTTwmUy9KPqkbS/aMkemVmtyvOaUr6iU0dPLVR1jJKFs5exoHRPygNJyc5ye/Pyn7enVIaeKHd0Yo49xy7Y0DP1WRS1UYUqcJgrNreIVlAClZtaEUoFmtJREWWo6AVwsYVwuTa4VwqAqwQXClVClBYKVUKSUGGqmMYYGAF73bRnsOCefwtFtXM0FuA6Qvf5OcAGkccD3XQkY2RhbIA5p8isJpKcsDOk3aCSAPfuqjWFc9zmx9PbK4sIa7yaRkn6jDh+Fqm4TSw7hGWNe1r2uDXDb8w4ORycHy911OjE17HiNocxu1pA7D0WEUlO0uLYWAnvx75/wBVqJWka+TqtcY2dB8r4W8ncHN3cn2+UrSfdqmOmZJJFCXSxNmjDXHABc0EH/zDn6rrPpIOq6URNErs/NjnkYytantlLTwCNsLD8rWuOO+OR/flbjnXObc6plTtnjhMbZjA8szknYXgj2xgYWj+vVTqEVLaZu2Rsb4ste0Dc9rdpJGCcOzkeh+/o3U0O8u6bd2/qZx/VjGfrjhagtlEzdspohuxkY9DuGPTnldI53HKfW3F1ZTQ5pmFtaYJsAkPb0TIMenB/I9Fe7XGrp6yWGkjpnCKm+JcZnluQCQWjH07+S6klJTvcXPiaXdQS5894AAd9cDH0WnW2ekra01FXEyYGIRbHtyMAkn7HPI9luOfTisvNSZ68QvaXOqIxCyVj3lrTAx5AawEnk+wGfsudV3q7Vlvr6mllhghdQU08bC0l0Zkc8Ow71+U/wBu3K9dUWuhqHOdLTRuc5+9xxgl23bn/wAox9FUWigbG1gpIgwRCANAwNgOQ36AqkxyW3WqpXbXRxOpaeoiopTvcZHPfsG4ZzwC8dySRk59Yjut0mioTHFRtfWSObGHFxDQ1rjk+pO3t7rtSW6jfWtq3U8ZqRgiTHOQMA/XBxlXZSU7OjtiYOiSY8D+OQQcfYlRqOVY7rVVk0LayGCNtTStqohE4ktHGQ4nufmHb3+p7mFhhpIITEYomMMUfSYQP4s4+Ue3AWdYtakSmFKLOtihSUUWIUhApWaoiIordBVwqBWCw6LhXCoFYKCwKsCqKUF8plVB9VOUBQUJUFVK5jK+QTSCWP8AaE/REgI4JxgY+p7qlTcg2GEta5plgMwII+XG3j/1LZ+AgE5k+fJf1C0uO3d64WD9KpQ7OJD8pYAZCQ1pIJA9OwWojXjrql1whgEbHRPdNudnBAY8Af6qLld46CUxzRknczGD3a7dl32DHH7e63TRwtkZI0OD2Oc4EH/McuH0JWKqoaepm6s0e5/TdFnP9Lu//XufVbjnXGud6niidJSRNeQDta93yuAlDN3bPOeFZl1nZWzR1FP+wKllP1GvHyOcxhAx3PzOIz7hb77TRmnZAY3dNkIgb8xyGgg9/XIByjrdTlpDg5xdMyckuOS9oaAf/SFuVixzbtcpqC7QA4NCIi+fjlg3AB+fQZ59snyXNbqjoUreuxks5fO4jqNj/bZK5gxnu7AGB7ckL08lJDJUGaRgc8xmI55BaTkgj7LmRadoIKaKCnE8LY2vY0xzOa7a925wJzk8/ceS1rOMQvZkrvg4KVzqgxmdgc/a10W3h+ccZcduO/c9lhgu9TJo2K6VMbYah8LZCIiCOccjI9+3K6bLTSsrBVNa/wCI3bt+8kn5du0/8OAOO2RnvytOtsDHWoUNJLJFGGCJofI5zWs3AnAzyQBgZ7JqYtSXf4m8VFD0BH0sjLpWh5xjnZ32nPDhn7LrALl1NkikklnjmnZUlsnSe6RzhC54wXNGf7fjC6sbQxjWgk4GMk5P5UtakAFOFKLNrUiFKkJhZ1rEKcIijWIwpwiKBhMIiNRtqwVFYLDS4KsCqKQoMmVKxq2UFlWXf0z0i0P8i4ZCZUqjn0NdI9sLqsxjruc2PYDjLc5ySfQFV/Wad3LGTOYGCVzw3hrDnDj7cFVraBxtrKOnJyX56pIBYN2SfxkfdUmtr5a6pxI6Klkgjh2sx8wBdkduOCB9yqjZ/UYDV9AB/wDLZvx8u7vtz6/2WlSXVz+rJVOp2Rxxl8jASJIfZzT3+o9PNZ2WqGOsdUMwNzzIW7G/yI/zYz79+6wyWhs3/eqqebEToW52ggOxk5A5PA5ViVaa7MjiY6Slq2vfJ02xmP5nEgkY5x5evHnhYXXdjHBhhnlle6TayKPJwxwBzz/xD/rAWwbe57oXz1U0zopRK3IaAMNLcYA9yVpz2uX9QgkpqiSFrWzlz27Scvcw4wR24P4C1GbFrfd2V9xmp4IZDA2COdlRxteH5wMdx2/se3Gc9yrhQwiQwTzZONsTQSB5kkkAflaYtD6JmbXM6OTpRU/7mCNrHE7u38sOf7HKzXe1R3J8D5JCx0O7b8jXD5gBnDgRkY4PktaxY1Xaho+q1sbKiSMtieZWM+RrZP4k85x9uFjkvrH3GlpqaGV0c1Q6nM7mfIS1jy4NPqCzHPvjOFkgsFPBTSQtllLXwwwknGcRdj27nPKRWJkVXBIyqnEEFRJUsg+XaHvDt3OM4+dxAz5/RWVMY73WXGkraGKkNJ0qqbojqscS07HOzwRx8v8AdRV32Om+Ihmjlimjhke2R8f7cjmNy7aM5OO/OMjsV0q2hjq5qOR7nNNLN1mhvmdjm4PthxXHl0pSy1E0rp5B1DOSGsYCeqHA5djJxuOMnjhPIkZjfYoKuthrAWCGR7WOaMhwZCyQ598OOPZqNv0E8lMynDwZOi52+PsJN2G9+HfKc98cd1lrLBTVfW6kkoMtU2qJGOCGBhb27FoIP/MUp7DTwbtskpLqz4zJI4d5N7fxClq4xQ6koZGbnsqYmmn+JjL4j+6zgHaBkkguaMeeRjI5Wa0XR1wra2F1PJT/AA/T+WVuH5cCeeSPIdvVYptO08sNJGZp2/DUhpI3NIyASwh3b+QMbSPJblttnwdTVVElTLUTVO3e6QNAG0YGAAMLOtY30CnCn7qNIwqq/wB1H3QQFJHopAUqaKBThWRTVZh/ZSq5UgqNLAqwKplSCguCpyqZUoLZU5CplMoLZQlVyoRE5UFFUkogSqFSe6grSWOZW3CZlYaWhpPip2RiWQGQRhjSSG8kHJO08e3JC05L7I6qjp6WglfMYG1D4pXiJ4a4kbWg/wAnDacjgDjnlbdXQ1IuD6ygqIopJIhFI2WMvaQ0ktIwRgjc76rTudlqq6mEE1ZBM3pBm+emDnsfzmRhBGCePoQFqVMZTdZXXWWkp6TqshexkrhM0PbuAO4MPdo3DJz64BwusuLX2aesq4nvngLIpY5I5HQZnjDSCWh4I4dg5OOxI5XcTUxQorEKMJq4hMKwCKaYjBU4RSpq4qpU4TCmriMKMK2EwggIpwoUURThMILqVAUoGVKIDlFSmfdEQTlMqEQTlQiwSVlNFVxUslRCyplBMcLngPeB3Ib3KIz5VT3WNtTA6pfTMmidUMaHPiDxvaD2JHcBZFRChSSoBBGRgj2QCoWvR11JXGcUdTBUGnldBN0pA7pyN7sdjs4ZHB5WympiqFYGVtLJVyUkdTA6qjG58LZAXtHqW9wFBrqQVwojVQfGlnUFP1B1NmcbtvfHummNhMLHVVENJA+eqmjhhYMukkcGtaPcngKKSpp6ynZPSTxTwP5bJE8Oa76EcJpjLhMLCK2lNaaMVMBqwzeYBIN4b67e+PdY6e50FVVS01NW0s1TF/vIo5Wuez6tByFDG0ilAioRWUIIRArIKorKAghFZMIJRFKAmURAypyoRFTlMqERA8r4f4xWiuvXixpqKzVBprvBbKmqopc4Amje1zQfZ2C0+zivuC5lRYbbU6gpL3NT7rpSRPghm3uGxj/5Dbnac+4RXweg8Q3/AKxrnVtHROFfTWGlbJRyNOYKhsj2Oa4d8NdyfYL1WkLlrWruTqCvrbm2irba+VtwraKmidSVHGHRhjiHxnJ4cD2HK+hU+jtP091u9yitkIq7vH0q4kuLZ24xhzCdvPOcDnzyuZQeGmlLfT1sNNbHhtXTmkkc6qme8QHvExxeXMb7NICDwXh3qzUOv4L0ypu7Le2228UxFNExxqpju/7WNzeGENwAOMk+gXpf9n2Cam8J7E6qr5Kls8ZfEyRrWiFuSOm3ABIyCcnJ5K9fSaVstHcqK4UlCyGroqP4CF8b3NDYPKMtBw4DHGQcLm0Xh9pq3UlNT0Ft2RUlb+o00bqiVzIqgAgOaC44HJ+UfL7IPnbr7qn9K1J/h4NM0erJ6aY0lPAKgUrQC7pMcA2STty7c4+/l73wsvkl809UvqLnPcKimrJKaR1TRfCTxEYPTlYON4DhkgAHPbuudp3w4gfYbjTaybS19ZX3SW7PNIZIm08r8Y6T8h4xjvkHnC9fp3T9s03bjRWalFPTmR0rhvc9z3u7uc5xLnOOBySTwiPkVBDadAeLOqKqnglFJTacFbPhxklnkMxLnEnkucfP/QLzPh5qG01Pi7ZLzW3ZlRe7zTVAqw1j9sEj3RiGnblo4a0Yz6557L9CR2K2x6imvrKfF1lpxSPn3u5iDtwbtzt7+eMq01lt898prxLBuuVNC+CKbe4bWPILhtzg5IHJGUHA8SabTM1jhr9YPY+1W6cVPSedzJZAC0NLMHf3OG+ZXM8ILJNaLZd7jPQx2alu9YayntbQGijj2ta0OAADXOxktHAyB6rqXDw20pcbJS2irtZfbqaofVQxNqZWbJXklztzXg93HzwM8JR+G+laOzXC1Q26Q0FeYzUxSVk8m8sO5vLnkjB9CM+aD59W2y2ac8cLtNF1qeGbStVXVk7HF8pcagl7wT5gAYHYYAXltC0TrLcvDO4XO0UFqoJnvgpbjRNb8TXPlYREKgAnaHA5PL+f8q/Qc+nLTUagN7no2yXI0ZoDK5ziDAXbiwsztIz54z7rk2bw60pZrrHcLdaI46mEuMO6WR7IC7v02OcWsz/wgIr1alMIUQUYUogAYRMJhAyiYTCAowFKnCAiIgnKlVRFWRVRBZFVEFkVUQWUZUIglQiIiFwdX32o07Qw17LbNX0LJMVhpzmSCLB/cazHzgHGQDkDJ5xhd57GvY5jxlrhgj1C+W6u8I7HVUMNNpqw2ajmmlDJ6yVhc6ni/qdGzkOf5DPAznlB9Kt9ZTXGhgrKGeOopZ2CSKWM5a9pGQQVsLlaV0/b9LWGks9nh6NFTN2tBOS4k5LifMkkkrrIIRSiAiIgIiICIiAiIgIiICIiCVKhEVKhEQEREEooRAUqEQSiha1yq/gqN85DTtLR87treSBknBwOUG0i5EN9pXNjEzmte9xaDG7qM/kG5Dsdsn+x9Csv6zSGR0YMnVDg0R7DucTuxgf+Bx+gyg6ShaEV0jlirJGQzbaYAnczaXZYHcA89iO6q26s/T/iJIy2QMyY85wdm/GceiDpKPJaMF0p5GOMj2ROBcAxzhucAMkgdysIvdO6SEMjncyWKSYOEZPDNueO5zvHZB1UXPju1NL0+l1Hl+eGsLtuHbSTjtz/AO57Aq1ruMVxg6kIPHDh3DT6Z9f/AJQbqlQiApUIgIiIJRQiCVCIglFCIJRSiCEUogjCKUQRhFKIIRHgljgCQSO47heWtsVyo7JE3FQHspqcbABkPJ/cJ+Ukkefc/flB6lY54WTx7JMlu5ruPUEEf3C4NH+rTyM68lREz5AcMaMjMm4nI74DPyoYb1LM2MyPiLoRhxjBAPS5J4wD1PLPpxhB0zZ6TrCRrXNIcXYB45duI/OT9z5FYobFRwuDouox7S0scDyzG7GPs9w5znPK0IJL28SSVIfHH8sgYxgLgx72/L2/kxrXf+Yd1klZc5qV5aZS8dIxNkAaHfvE5dgZ/gGZ+/CDrQ0MMUcrGmQtlaGu3OLicN25yec4A/Cwy2mmkdlxl27dpYHnaflLM49dpI/HosU0le2zRSRtldWtwHsIGXHlp9sAnPHkAufNFdJWESGbMbXtEgaA9wa6Lngf1YecDug7sVHFFFJGzcGvJJIcQefQ+S0/0Kj2EEPJcHhxz/IO25yMY/ob5eXuc69jjrYakNmMxp5HVTyHgfKetlmPPlpJWKE3B9XEZTVOjiqck7Q3c0tkGNuOwJZzkjscjlB0YrVTxFhjfMxzc5LX43Au3YOPLPpjzHZZbfbqe3hwpWljXY3NzwSPP6+/sFwYX3N4Y+p+Na1smcsjBcAY+3LRkbvPH9kP61HvLQ9j5Hlzi1u4b9kWMcH5c7/Qe4QepTC4l8bcJKyF1DCHMpWiYuc8t3ku/i0AHcdrXDnH8wudJX3Ewzyxy1Gxs8kbj024aBOGNDOOTjcPP38kHrEWpanTOpSZzKfndsMrQ15bnjIH/wB4xnlbiCEwpRBCKUQQilEEIpRBGUypRBGUypRBGUypRBGUypRBGUypRBGUypRBGUypRByKS+QTn5gBlu4CN3UI+bbggDIJPl9fRbQulIQcSnI2ZGx2QXOLWjGO+QRjyxysQs9O1rek+WJ7WhoewjPDtwPbGQSfs4qv6JTiaOQSTgt2FwDhiQtcXAu475ce2O/0QTT3yimigeZHRmWNsga9hGNwJAJxgHAP/RC3qedlRAyaLcY3jLS5paSPoeVzYrBSRvjc0yHYwR87eQAQ3nGeAccegzldSGNsMLI2Z2saGjPoEFsplSiCMqMDGMDHfCsiCMplSiCMplSiCMplSiCMplSiCMplSmEBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBETKCUUoghFKIIRSiCEUoghFKIMFbVQ0VHPV1T+nTwRulkfgna1oyTx7BeIh8X9CTTthi1BE6VxDQ3oS5ye39C995L5rptjXeOetmkcG3UI448noPpKL4Lp+6Xur1HafDyaurzXWe7z1VfWGpeZJqGPEkO92ckSdVjcZ/p5Xmqq+VEdk13dDrG6w6iteop4rZQC4Ha9nVaGsEJP7jTlwxggBvGOch+n15vVGuNN6VqIqe/3anpKiVu9sRDnv25xuLWgkD3PHB9F8b1neL5W6s1g2ou5tM1sbF+nl97dQx07TGHCXohhE4c7IOSf8vHC7t4mEGrorrTastNm1a6zwfqFLcoc0VVHgnMb3FrgA7d/Ek4xkIPsVsr6S6W+Cut1RFU0c7A+KaJ25r2nzBWyvz9b9QR3qzeHtJKTpLTVyfWfFC2ymkikljJ2NbIMFjHu3uxkE5xz3W7eJ/i6fRdrsGsr1WUFRfZ6OWvZUOZK6MMdmPqYHUA7B/PqDxwH2alu1JVXeutkL5DWUTY3ztMT2taHgluHEbXdj2Jx5rclkZDE+SVzWRsBc5zjgADuSV+edcXG52WPxVo6G8XRkVrorOyke6rkc+HO0OcDnO539R7u81vNfHUanrbLY9TXS+2mv09NV1xNxdKYJWkbHte0/IXdiwYGD2wg+50FZTXCjhq6GeKopZmh8csTg5j2nsQRwQs6+feAUVJF4U2E0VU+o3wNdLuqDL05NoywZJ2AcfKMAenK+hIIRSiCEUoghFKIIRSiCFKKUBERAREQEREBERAREQFrRUNJFXTVsVLAysna1ks7YwJJGt/iHO7kDJxnstlEHkdI6NNlv13v1zuBul7uWyN9SYGwtjhZ/GNrQTjyyc84HosOkPD62WGpuVVVR0txrKm61F0gqJaVokpTKWksY45PG3uMZ9F5/wAWq29xansNPb5tS09pNPUSVT7HS9aR8mWCNpy1zR/UeccZXrPDe50d00hS1FBda66Rh0jHz14a2oDw87mSNAADm9sY7Ad+6g6txsNoudZT1dytVBV1VPzDNPTskfHzn5XEZHPPCm8WK03tsTbza6G4NhJdGKqnZLsJ7kbgcdgvg1l8Qbpep6fUMmortbaee4dCOH9MElsgi6uxsM0gAPUcMHeDxuHby+7ampbpW2aensVwjt1fIWhtU+ES9MZG4hp4JxnGeMoM9wtFtuNu+AuFvpKqhwB8PNC18fHb5SMceShlmtjI6KNluomx0J3UrRA3FOcYzGMfLxxxhfOPCTUFVctU3+3096rrxY6SKJ0Ut0Y2KrZM7O9uzaxxZjBBc0egzyvd33Ven7BURwXy9W+3zSN3sZU1DYy5ucZAJ7ZQaGudF0WqNPXi3xCCgq7o2Js9bHTtdI8Rva5u7sXYAwMnjK7dss9utb6h9voKSllqXb53wQtYZnf5nEDk9+SvEeHfiRSX+8VWn7hX2ue8w7pIJrfOJIKyHPD24JLXgfyYTkYyMjt9GQaVrtdvtMMkVroaWiikkMr2U8TY2uee7iGgZJwOfZbqIqCIiAiIgIiICIiAiIgJhSigjCKUQRhFKIIwilEEJhSiCEwpRB5nVVivVwr6Ou0/qKS0zwMfHJDJT/EU8zXY5dHub8wxw4H2WhpbRdXpqioae33txJrpq+6ySUrS64Pkznz/AG+dvbPDQF7VEHyp3hPUCilsUOonRaOkrPjDbBRtMjR1Op0hMXfw3c/xz5ZXqrzp693Klu0LdUVFGZ545qCSmp2sdRhuMsdz+61xHIOODherRB4bS2ia2h1jU6o1DexdbvJRCgj6NIKaKOEP3/xDnFzieck/+y9jUUVLUuDqinhlcBgGRgcQPuthEHktGaMgsFXW3OtnbX3ytd+7VdIRtjjz8sUTB/Bg44zknkk+XrMKUQQmFKIIRSiCMIpRBGEUogjCKUQQpwiICKUREIpRBCKUQQilEELivrqynuVSKiSB9JF0zhkJDwHuIBLtxHy4yTj17LtrXq6OKphnjcNvXZ05HNAy5vPHI9z+UVzBf4zKP2H9D9omUOHaV5aw47nOAfbKvQX2GqhdKYKljPkLS2F79wcCR/Ee3PkOOeVtttlKKw1JiaZNrGgEDDQzOMD7rPT01PAXfDxMYTgHaPTsP79vdBzH3sNbPmnc18cnTDHuDXO5dzjvztJGAcj740/8RvbI6R1OXQSGNsDW/wAnF0fUJd6ccfVdhtDQOj6jYYSx37gcAMefIP8A4nfk+qv8BSGNrBTxbG4LQGgYw3aMfbj6IMdTXBlqkq4hh/w7p2MkGDw3PI7+YytV18iZVyQyRODWv29QHcP5hhJxxwXDz4HfGF0ZaKmmZGyWCJ7IxhoLRho9B7cBYzRUW+RroIS6Zrg4ED5mk/Nx6Enn6oNAXxrpoI44cmZm9hLxj+LnDd/lBDfP180pLlVbDFNG2as3AdJrDFtBaTzuJB7EAgkH7FdJ9DSvk6j6eJz8YyWj0Lf9CR9CqfptGGFnw0W0nPbzxj/Q4+iDnXC9upGPldTuEUb3tIJBL9sTnkDnjsBk58/qtiluUs9eKY02wt3CXLwdpAYRjHfIePRZq230dVTPo5WMaJWPwG4DgC3aSPs7H3WanpaWPBgijG1xOWjz7Hn7Y+yDmy3+COudSGKQztcGFvuXhrR9wS76ArFTXirmbDspmSb3MaMO2l26IvPB/jyPUrrmnpTUbjFEZ8iTOBuzgtDvwSMqY6Snjc10cMbS3AbhuMYBA/sSPugmkmbU0sM7AQ2VjXgHuARlZVEbGRxtZG0NY0BrWjgADyVkRCKUQQilEEIpRBCKUQSiIqp9kUoghFKhA+yKUQQilEGheKeWpoTHDvDt7T8rtpwCD59x7cZ9QtCitUzJGGZrRmaOaTZI7BxBsxycnDhnnuu6pQeafaKyWllgqB1XyQCNshncBGOjtII8/nyfvnuAty5W+apomRwbonMppGsAkLdshDdh4Plgrsog87La60OkbGSY9z+hiYt6RJaQ73xh3H27ErDWWivlme+A9J/zh0nWJ6jTMx2APL5GkeXp25XqFCDhvt1abNT0/VeZGSlzwX5c5mXYbngcZbx7Yye61xarixnUZJuqBgN6k7sEdAtwSP8Ajwcjnz7r0qIPICy3Lc9zwX4EzYf3ywx7mxbTx5BzHnue+eStyW1V5rYntmcImybgGPxtPVLiT9WkD7Y88r0aIOHZLfVUtY6Wq+ZxgbG+TqF3UeHEl2PIHI/08gu2pRBCIiAilEEfZFKhARSiCPsn2UogIpRQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQQilEEIpRBCKUQETCYQETCYQETCYQETCYQFSeMTQyRlzmh7S3cw4Iz5g+RV8JhB8Jp9I7/GSr027Umqv0yOyMr2j9Xl39UzbD82e2PJd2XxWnotYUNprqWy/D1dx/TWR012bPWRkktbJJE1uA0kDI3ZGV7aLSsUfiDPqsVMhnltzbcafaNoaJN+7PfPlheOt3g+yhNup2ahrHWu3XRt0pqM08Qw8Pc/D5AN7/AORAJPAPY8YDzcOoZ6irohb3VtG1viBLb5wa2SXrtaxxP8j8rCcftj5Rhds+MJipLT8TamtrX3SagusLZcigZFMyN0pOOR+7CfL+Xsu3R+GNLTSte24zuLdSSajwYx/vHtI6X/Lz37rHWeE9sqr3q64yVk4OoKYwGING2mc4M3SM9XF0bHc+YQcer1x+s3uyF9BI2mbqiW1Us0NY+MSiONwMrg3h7Sdw2nI4VKbxXvT6SC7TabphYf1Y2madtaeq13W6YkDNmC3t55zn6r0tD4bUdFZ9H2+Gtm2adqvi2PLBmoeQ7du9Ml5Ko3wzpW6Pdp/9Qn6Trp+qdbpjdu6wl2Y9MjGUHmdea9vlfYddDTVpBtVmjnoZri2s6U7Z2x5c6NgHZhcCTuBxyOeF39aaortMeBr7/SOMlxittOWSSDeepJsZvOe+C/dz3wsd/wDCplxqNQsodQXG22u/F0tdQwNYWPmLcF4cRubkgFwB+bGDxwvYVOm6Gt0h/hy4sNTb3UjaSQH5S9oaG547HjPHYoPH2TQF4tFTZrlbtYXiqq2vY65R3GoM8FVGR84azsx2f4kdvdeH1ZNc6LUt/qNX3fVtjxWbrTdKAOktsFNkdPqMZxnOQ4PHOV7y1eGU0VbaDe9U3W8260StmoaKobG0Me0YY6R7RukLR2yq3rwxnuD7vTU+qrtSWK7zOmrLc1kbwS/G9rJHAuY047DgZQed8RX3V2qIqm9VGp36Sdb2Glq9MuIayfu+SVrCXkYwW9249eV9G8Oaptbom0zsvjb810XFxEfTM4BIBc3Jw4djnnIOcFci5eH87bi2r0tqO4afzSxUckEMbJonRxjDCGvB2uA4yPQL0GitM0WkNM0VktZkdTUzT88rtz3uc4uc5x9SSSg7aJhMICJhMICJhMICJhMICJhMIJREQEREBERAREQEREBERB84m8TJ6W503x+lbtR2Kpr226K51Do2Zlc8saTCTvDCRw7HbyX0ckAEkgAd18F1xJe9SagtctBpTUFFq2318fR+IJmtLY2vJMpef2zlpPLQHg8DsF7/AFsL5q3Teo7FpyKW01rJW0nxde10cc8Tmhz3RObkkEEtzj180EaF8SKPWOp7xa7fRTMpaFgkhrXO+SrYXuZvYMfx3McAc84WS9eIMVs13bNNC0V8grJxTGvIDIGSGPqbWk8vIbgnHAyOc8LxOhrTq7T/AImXGWvs9ritNNZYYMW2OcsfHHvLI4C8AOkz3BPYjzXf1eK7UF68MbrTWm4xRiuNTURSwO30rXQn/egZ2cnHPmg6euvEmi0rqWx2MUctdWXGaNkvSeAKWN7wxr38Hu44A88H7+8X521do3xBo5H1hhsl0qbhqClrHzwiokmY1j/2muAbhsLB3x2BJyvvdVS1FdZn0s9S+kqpodj56N2DG8jlzC4Hse2Qg1rlqK22y9221V0zoKq4h4pi9hEb3Nx8m/tuIOQ3OTgrrr4h4ieH13uQotP0N+1RcpK53UfU1ssRpaNjC3MjiIwS/kbWtIOecgBfZbXSvobbS0slTPVvhjbGZ5yDJIQMbnEADJQbSIiAiIgIiICIiAiIgIiIJwmERAwmERAwmERAwmERAwmERAwmFjqZmU1PLNMdscbS9xxnAAyVr/qNLvgb1cGeMzMJBA2gA5J8uD5oNzCYWnHcqKSWOKOrge+RnUYGyA7m5xkevPCyuq6dj9jqiFr8E4LwDxnP4wfwUGfCYWA1dOKX4o1EIpsburvGzHrnssX6nQ5lHxlOOiGmTMgG0O/iT9fJBuYTC1Zq+jgcRNV08ZAyQ+QDA455PuPyFY1dO14Y+ZjHF/TaHOA3OwDgZ790GxhMLUq7hBSvDZi/ONx2RucGN9XYBwODyfQ+iqbnTNM2XPDYQS95jds474djBI9Ag3cJhYaaoZUxb42yNGcYkjcwj7EArMgYTCIgYTCKEE4TCIgYTCIgYTCJlBCK33T7oKorfdPugqitj3Ty7oKorfdPugqit90IODg8+WUGtcKf4ugqabds60bo92M4yCM4XLFh2yTPZVOy8vDN7d2xrs4b37Aud9iB5LHS11dBTvNTNBJtjdM55YRyXODWgA8AYH/XKpTXutqaRskcdO1zZBE/dk5JlMYIwT7HufTPmgzxWR8QBbUt6gycujLueoJB3dnGR68+ys6yb46kSzgyTNA3NjxtIlfJ65x8wGM+Xda0d9rJZ4IWQQ7z8jySAC7e9mRkg4BZnHJ5x9UeoZZGF7I4g1zOqwkkbWZDMu9MO3E/8IQdBtscy2/DRzBji8yFwDiCS7J7uz989/wtemsj6eKBsdRGTDsI3xEglrHM5+bkYd68EdysM12rHU1V0uiDBTzSGRgJ3luQCzJx39c9iPdZK+vq5rAailkZT1D52REFu4xbpGsLTz/IZ+np6oMsNkjjjYwyNftcDkxjkCHp47/f+3ukFofBNHIyaJxDQx3Uh3ZADRxzwfl9/L0WrLe6puWRNp3PdI2NvJPT/ebHh/ud2R27Ee66FwrZ6aVwaYdkcBle54PJ5AAx2Gf+vMBkqqWofUPmpalsLpIxG7dHvIwSQW8jB+Y98jt99eS1yOqppmzxM3tc07Yf557bxnDsfQH39dCPUFSQzc2nBDS5zM/NJiQswwAkZOAe59PcXjutS74dnWhbmSNrg8/uPDpCPlHbGBjt69sch1bXRuooZGGRrg55cGtaWtYMAYaCTgcZ+63FyLjcHwfFRNna2o6sQhZxuLXFo4Hnzu/ut61yvnpN8rtzupI3PsHuA/sAg2UVvun3QVRWx7p90FUVvun3QVRW+6fdBVFb7qUBERUFHoiIJREQFHqiIJREQQeylEQE9ERACjyREE+aeqIg0GgfrknA/wC7t/8A2ct/zREEeinzREBPREQR6qSiICjyREE+aIiAiIiv/9k="}, {"timing": 3000, "timestamp": ************, "data": "data:image/jpeg;base64,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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 1073, "timestamp": ************, "data": "data:image/jpeg;base64,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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 0.23, "scoreDisplayMode": "numeric", "numericValue": 1113, "numericUnit": "millisecond", "displayValue": "1,110 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 0, "scoreDisplayMode": "numeric", "numericValue": 1163, "numericUnit": "millisecond", "displayValue": "1,160 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "unitless", "displayValue": "0", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0, "newEngineResult": {"cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0}, "newEngineResultDiffered": false}]}}, "errors-in-console": {"id": "errors-in-console", "title": "Browser errors were logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "sourceLocation", "valueType": "source-location", "label": "Source"}, {"key": "description", "valueType": "code", "label": "Description"}], "items": [{"source": "network", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "sourceLocation": {"type": "source-location", "url": "http://localhost:3000/favicon.ico", "urlProvider": "network", "line": 0, "column": 0}}]}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 33.7, "numericUnit": "millisecond", "displayValue": "Root document took 30 ms", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:3000/en", "responseTime": 33.7}], "overallSavingsMs": 0}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.24, "scoreDisplayMode": "numeric", "numericValue": 10335.137149999999, "numericUnit": "millisecond", "displayValue": "10.3 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "1 chain found", "details": {"type": "criticalrequestchain", "chains": {"07E76E2771BEE4A99FA99D30D92DCDE2": {"request": {"url": "http://localhost:3000/", "startTime": 349586.747173, "endTime": 349586.962513, "responseReceivedTime": 349586.96183600003, "transferSize": 653}, "children": {"07E76E2771BEE4A99FA99D30D92DCDE2:redirect": {"request": {"url": "http://localhost:3000/en", "startTime": 349586.96284, "endTime": 349586.999689, "responseReceivedTime": 349586.996964, "transferSize": 8589}, "children": {"22586.3": {"request": {"url": "http://localhost:3000/_next/static/css/app/%5Blocale%5D/layout.css?v=1753001567307", "startTime": 349587.003945, "endTime": 349587.016695, "responseReceivedTime": 349587.01475599996, "transferSize": 9732}}}}}}}, "longestChain": {"duration": 269.5219999551773, "length": 3, "transferSize": 9732}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 607.37905, "numericUnit": "millisecond", "displayValue": "Est savings of 610 ms", "metricSavings": {"LCP": 600, "FCP": 600}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:3000/", "wastedMs": 607.37905}, {"url": "http://localhost:3000/en", "wastedMs": 0}], "overallSavingsMs": 607.37905}, "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 1504.6240000000128, "numericUnit": "millisecond", "displayValue": "1.5 s", "metricSavings": {"TBT": 1100}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 822.9640000000123}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 537.008}, {"group": "other", "groupLabel": "Other", "duration": 84.96400000000014}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 51.556}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 4.4879999999999995}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 3.6439999999999997}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "Reduce JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1340.6440000000048, "numericUnit": "millisecond", "displayValue": "1.3 s", "metricSavings": {"TBT": 1100}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "total": 1161.1960000000067, "scripting": 628.4960000000067, "scriptParseCompile": 526.664}, {"url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js", "total": 177.5999999999981, "scripting": 174.5159999999981, "scriptParseCompile": 0}, {"url": "http://localhost:3000/en", "total": 69.884, "scripting": 4.0920000000000005, "scriptParseCompile": 2.644}, {"url": "Unattributable", "total": 68.70399999999991, "scripting": 4.232, "scriptParseCompile": 0}], "summary": {"wastedMs": 1340.6440000000048}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 11, "numScripts": 6, "numStylesheets": 1, "numFonts": 1, "numTasks": 388, "numTasksOver10ms": 3, "numTasksOver25ms": 2, "numTasksOver50ms": 1, "numTasksOver100ms": 1, "numTasksOver500ms": 0, "rtt": 0.07695, "throughput": 61213141.03665552, "maxRtt": 0.07695, "maxServerLatency": 7.379049999999999, "totalByteWeight": 1625867, "totalTaskTime": 376.15599999999966, "mainDocumentTransferSize": 8589}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "http://localhost:3000/", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 0, "networkRequestTime": 1.9010000824928284, "networkEndTime": 217.24100005626678, "finished": true, "transferSize": 653, "resourceSize": 0, "statusCode": 307, "mimeType": "text/html", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/en", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 217.24100005626678, "networkRequestTime": 217.56800001859665, "networkEndTime": 254.4170000553131, "finished": true, "transferSize": 8589, "resourceSize": 24468, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/media/e4af272ccee01ff0-s.p.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 255.466000020504, "networkRequestTime": 255.9860000014305, "networkEndTime": 258.1360000371933, "finished": true, "transferSize": 48928, "resourceSize": 48432, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/css/app/%5Blocale%5D/layout.css?v=1753001567307", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.01700007915497, "networkRequestTime": 258.67300003767014, "networkEndTime": 271.42300003767014, "finished": true, "transferSize": 9732, "resourceSize": 45591, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/webpack.js?v=1753001567307", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.0840000510216, "networkRequestTime": 260.5610000491142, "networkEndTime": 269.92000007629395, "finished": true, "transferSize": 11104, "resourceSize": 56325, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.12500005960464, "networkRequestTime": 260.81900000572205, "networkEndTime": 476.0730000734329, "finished": true, "transferSize": 1529836, "resourceSize": 6819975, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/main-app.js?v=1753001567307", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.22400003671646, "networkRequestTime": 261.2900000810623, "networkEndTime": 268.6620000600815, "finished": true, "transferSize": 1210, "resourceSize": 1300, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/app-pages-internals.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.25200003385544, "networkRequestTime": 261.4930000305176, "networkEndTime": 269.6389999985695, "finished": true, "transferSize": 2844, "resourceSize": 14094, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/app/not-found.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.28400003910065, "networkRequestTime": 261.7290000319481, "networkEndTime": 270.2570000886917, "finished": true, "transferSize": 4968, "resourceSize": 12038, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/layout.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 258.3100000023842, "networkRequestTime": 269.5530000925064, "networkEndTime": 275.59100008010864, "finished": true, "transferSize": 3361, "resourceSize": 11180, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:3000/favicon.ico", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 826.0310000181198, "networkRequestTime": 826.3519999980927, "networkEndTime": 848.7000000476837, "finished": true, "transferSize": 4642, "resourceSize": 13877, "statusCode": 404, "mimeType": "text/html", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 349586745271.99994}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 0.07695, "numericUnit": "millisecond", "displayValue": "0 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:3000", "rtt": 0.07695}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 7.379049999999999, "numericUnit": "millisecond", "displayValue": "10 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:3000", "serverResponseTime": 7.379049999999999}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 5.554, "startTime": 253.573}, {"duration": 10.35, "startTime": 369.053}, {"duration": 290.636, "startTime": 490.46}, {"duration": 34.297, "startTime": 781.683}, {"duration": 6.482, "startTime": 816.316}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 10335, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 922, "largestContentfulPaint": 10185, "interactive": 10335, "speedIndex": 1327, "totalBlockingTime": 1113, "maxPotentialFID": 1163, "cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0, "timeToFirstByte": 457, "observedTimeOrigin": 0, "observedTimeOriginTs": 349586744726, "observedNavigationStart": 0, "observedNavigationStartTs": 349586744726, "observedFirstPaint": 689, "observedFirstPaintTs": 349587433592, "observedFirstContentfulPaint": 689, "observedFirstContentfulPaintTs": 349587433592, "observedFirstContentfulPaintAllFrames": 689, "observedFirstContentfulPaintAllFramesTs": 349587433592, "observedLargestContentfulPaint": 689, "observedLargestContentfulPaintTs": 349587433592, "observedLargestContentfulPaintAllFrames": 689, "observedLargestContentfulPaintAllFramesTs": 349587433592, "observedTraceEnd": 3184, "observedTraceEndTs": 349589928762, "observedLoad": 816, "observedLoadTs": 349587560751, "observedDomContentLoaded": 274, "observedDomContentLoadedTs": 349587019096, "observedCumulativeLayoutShift": 0, "observedCumulativeLayoutShiftMainFrame": 0, "observedFirstVisualChange": 678, "observedFirstVisualChangeTs": 349587422726, "observedLastVisualChange": 988, "observedLastVisualChangeTs": 349587732726, "observedSpeedIndex": 684, "observedSpeedIndexTs": 349587428947}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 10, "transferSize": 1621225}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 6, "transferSize": 1553323}, {"resourceType": "font", "label": "Font", "requestCount": 1, "transferSize": 48928}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 1, "transferSize": 9732}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 8589}, {"resourceType": "other", "label": "Other", "requestCount": 1, "transferSize": 653}, {"resourceType": "image", "label": "Image", "requestCount": 0, "transferSize": 0}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 0, "transferSize": 0}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "10,190 ms", "metricSavings": {"LCP": 7700}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-P", "path": "1,H<PERSON>L,1,BODY,0,<PERSON><PERSON>,1,DIV,0,DIV,1,P", "selector": "main.flex > div.relative > div.text-center > p.text-lg", "boundingRect": {"top": 232, "bottom": 372, "left": 96, "right": 316, "width": 220, "height": 140}, "snippet": "<p class=\"text-lg text-gray-600 dark:text-gray-300\">", "nodeLabel": "A modern, stable web development platform built with Next.js, TypeScript, and T…"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 457.37905, "percent": "4%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 9727.7581, "percent": "96%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "2 long tasks found", "metricSavings": {"TBT": 1100}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Duration"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "duration": 1163, "startTime": 9172.137149999999}, {"url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js", "duration": 137, "startTime": 766.7581}], "sortedBy": ["duration"], "skipSumming": ["startTime"], "debugData": {"type": "debugdata", "urls": ["http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js"], "tasks": [{"urlIndex": 0, "startTime": 9172.1, "duration": 1163, "other": 1163, "scriptEvaluation": 0}, {"urlIndex": 1, "startTime": 766.8, "duration": 137, "other": 137}]}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Missing source maps for large first-party JavaScript", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "scriptUrl", "valueType": "url", "subItemsHeading": {"key": "error"}, "label": "URL"}, {"key": "sourceMapUrl", "valueType": "url", "label": "Map URL"}], "items": [{"scriptUrl": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}]}}]}}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No CSP found in enforcement mode"}]}}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No HSTS header found"}]}}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"description": "No COOP header found", "severity": "High"}]}}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "http://localhost:3000/_next/static/chunks/main-app.js?v=1753001567307", "resourceBytes": 1300, "encodedBytes": 636, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/app-pages-internals.js", "resourceBytes": 14094, "encodedBytes": 2269, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/app/not-found.js", "resourceBytes": 12038, "encodedBytes": 4393, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/webpack.js?v=1753001567307", "resourceBytes": 56325, "encodedBytes": 10529, "unusedBytes": 34650}, {"name": "http://localhost:3000/en", "resourceBytes": 18548, "encodedBytes": 6023, "children": [{"name": "(inline) (self.__next_f=…", "resourceBytes": 72, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 264, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2185, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 4494, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2353, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 249, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 8931, "unusedBytes": 0}]}, {"name": "http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/layout.js", "resourceBytes": 11180, "encodedBytes": 2786, "unusedBytes": 0}, {"name": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "resourceBytes": 6819911, "encodedBytes": 1529259, "unusedBytes": 8118}]}}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "button-name": {"id": "button-name", "title": "Buttons have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "notApplicable"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors have a sufficient contrast ratio", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "notApplicable"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "document-title": {"id": "document-title", "title": "Document has a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "notApplicable"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "notApplicable"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "notApplicable"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "notApplicable"}, "heading-order": {"id": "heading-order", "title": "Heading elements appear in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element has a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": null, "scoreDisplayMode": "notApplicable"}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "notApplicable"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": null, "scoreDisplayMode": "notApplicable"}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "notApplicable"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "target-size": {"id": "target-size", "title": "Touch targets have sufficient size and spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "notApplicable"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "notApplicable"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "manual"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "manual"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "manual"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "manual"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "manual"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "manual"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "manual"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "manual"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "manual"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "manual"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Uses efficient cache policy on static assets", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "byte", "displayValue": "0 resources found", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 1625867, "numericUnit": "byte", "displayValue": "Total size was 1,588 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "totalBytes": 1529836}, {"url": "http://localhost:3000/_next/static/media/e4af272ccee01ff0-s.p.woff2", "totalBytes": 48928}, {"url": "http://localhost:3000/_next/static/chunks/webpack.js?v=1753001567307", "totalBytes": 11104}, {"url": "http://localhost:3000/_next/static/css/app/%5Blocale%5D/layout.css?v=1753001567307", "totalBytes": 9732}, {"url": "http://localhost:3000/en", "totalBytes": 8589}, {"url": "http://localhost:3000/_next/static/chunks/app/not-found.js", "totalBytes": 4968}, {"url": "http://localhost:3000/favicon.ico", "totalBytes": 4642}, {"url": "http://localhost:3000/_next/static/chunks/app/%5Blocale%5D/layout.js", "totalBytes": 3361}, {"url": "http://localhost:3000/_next/static/chunks/app-pages-internals.js", "totalBytes": 2844}, {"url": "http://localhost:3000/_next/static/chunks/main-app.js?v=1753001567307", "totalBytes": 1210}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 155, "numericUnit": "millisecond", "displayValue": "Est savings of 160 ms", "metricSavings": {"FCP": 150, "LCP": 150}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/css/app/%5Blocale%5D/layout.css?v=1753001567307", "totalBytes": 9732, "wastedMs": 157}], "overallSavingsMs": 155}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 5 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/webpack.js?v=1753001567307", "totalBytes": 10529, "wastedBytes": 5135, "wastedPercent": 48.7705281846427}], "overallSavingsMs": 0, "overallSavingsBytes": 5135, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 10 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "wastedBytes": 10061, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 246, "column": 470}}, {"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 1029}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 415}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 528}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 906}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 1164}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 151}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 64}}]}, "totalBytes": 0}], "overallSavingsMs": 0, "overallSavingsBytes": 10061, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": 1, "scoreDisplayMode": "binary"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": 1, "scoreDisplayMode": "binary"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 35, "numericUnit": "element", "displayValue": "35 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 35}}, {"node": {"type": "node", "lhId": "1-1-<PERSON><PERSON>", "path": "1,<PERSON><PERSON><PERSON>,1,BODY,0,<PERSON><PERSON>,2,DIV,0,DIV,0,H2,2,<PERSON>AN", "selector": "div.mb-32 > div.group > h2.mb-3 > span.inline-block", "boundingRect": {"top": 421, "bottom": 453, "left": 195, "right": 217, "width": 23, "height": 32}, "snippet": "<span class=\"inline-block transition-transform group-hover:translate-x-1 motion-reduce:…\">", "nodeLabel": "->"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 6}}, {"node": {"type": "node", "lhId": "1-2-<PERSON>ODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body.__className_e8ce0c", "boundingRect": {"top": 0, "bottom": 1256, "left": 0, "right": 412, "width": 412, "height": 1256}, "snippet": "<body class=\"__className_e8ce0c\">", "nodeLabel": "body.__className_e8ce0c"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 10}}]}, "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "notApplicable"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document has a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": 1, "scoreDisplayMode": "binary"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": 1, "scoreDisplayMode": "binary"}, "font-size": {"id": "font-size", "title": "Document uses legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": 1, "scoreDisplayMode": "binary", "displayValue": "100% legible text", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "selector", "valueType": "code", "label": "Selector"}, {"key": "coverage", "valueType": "text", "label": "% of Page Text"}, {"key": "fontSize", "valueType": "text", "label": "Font Size"}], "items": [{"source": {"type": "code", "value": "Legible text"}, "selector": "", "coverage": "100.00%", "fontSize": "≥ 12px"}]}}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": 1, "scoreDisplayMode": "binary", "warnings": [], "details": {"type": "table", "headings": [], "items": []}}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": null, "scoreDisplayMode": "notApplicable"}, "hreflang": {"id": "hreflang", "title": "Document doesn't have a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "source", "valueType": "code", "subItemsHeading": {"key": "reason", "valueType": "text"}, "label": ""}], "items": [{"source": {"type": "node", "lhId": "1-6-LINK", "path": "1,<PERSON><PERSON><PERSON>,0,<PERSON><PERSON><PERSON>,18,<PERSON><PERSON><PERSON>", "selector": "head > link", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<link rel=\"alternate\" hreflang=\"en\" href=\"/\" />", "nodeLabel": "head > link"}, "subItems": {"type": "subitems", "items": [{"reason": "Relative href value"}]}}, {"source": {"type": "node", "lhId": "1-7-LINK", "path": "1,<PERSON><PERSON><PERSON>,0,<PERSON><PERSON><PERSON>,19,<PERSON><PERSON><PERSON>", "selector": "head > link", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<link rel=\"alternate\" hreflang=\"zh\" href=\"/zh\" />", "nodeLabel": "head > link"}, "subItems": {"type": "subitems", "items": [{"reason": "Relative href value"}]}}]}}, "canonical": {"id": "canonical", "title": "Document does not have a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": 0, "scoreDisplayMode": "binary", "explanation": "Is not an absolute URL (/)"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "manual"}, "bf-cache": {"id": "bf-cache", "title": "Page prevented back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 0, "scoreDisplayMode": "binary", "displayValue": "4 failure reasons", "details": {"type": "table", "headings": [{"key": "reason", "valueType": "text", "subItemsHeading": {"key": "frameUrl", "valueType": "url"}, "label": "Failure reason"}, {"key": "failureType", "valueType": "text", "label": "Failure type"}], "items": [{"reason": "Pages with WebSocket cannot enter back/forward cache.", "failureType": "Pending browser support", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/en"}]}, "protocolReason": "WebSocket"}, {"reason": "Pages whose main resource has cache-control:no-store cannot enter back/forward cache.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/en"}]}, "protocolReason": "MainResourceHasCacheControlNoStore"}, {"reason": "Back/forward cache is disabled because some JavaScript network request received resource with Cache-Control: no-store header.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/en"}]}, "protocolReason": "JsNetworkRequestReceivedCacheControlNoStoreResource"}, {"reason": "Back/forward cache is disabled because WebSocket has been used.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:3000/en"}]}, "protocolReason": "WebSocketSticky"}]}, "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": []}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 0, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 200, "LCP": 200}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Had redirects (1 redirects, +217 ms)", "value": false}, "serverResponseIsFast": {"label": "Server responds quickly (observed 34 ms) ", "value": true}, "usesCompression": {"label": "Applies text compression", "value": true}}, "debugData": {"type": "debugdata", "redirectDuration": 217, "serverResponseTime": 34, "uncompressedResponseBytes": 0, "wastedBytes": 0}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 37}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-2-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body.__className_e8ce0c", "boundingRect": {"top": 0, "bottom": 1256, "left": 0, "right": 412, "width": 412, "height": 1256}, "snippet": "<body class=\"__className_e8ce0c\">", "nodeLabel": "body.__className_e8ce0c"}, "value": {"type": "numeric", "granularity": 1, "value": 10}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-3-SPAN", "path": "1,<PERSON><PERSON><PERSON>,1,BODY,0,<PERSON><PERSON>,2,DIV,0,DIV,0,H2,2,<PERSON>AN", "selector": "div.mb-32 > div.group > h2.mb-3 > span.inline-block", "boundingRect": {"top": 421, "bottom": 453, "left": 195, "right": 217, "width": 23, "height": 32}, "snippet": "<span class=\"inline-block transition-transform group-hover:translate-x-1 motion-reduce:…\">", "nodeLabel": "->"}, "value": {"type": "numeric", "granularity": 1, "value": 6}}], "debugData": {"type": "debugdata", "totalElements": 37, "maxChildren": 10, "maxDepth": 6}}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.", "score": 1, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [], "items": []}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "INP by phase", "description": "Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "LCP by phase", "description": "Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Phase"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"phase": "timeToFirstByte", "label": "Time to first byte", "duration": 252.186}, {"phase": "elementRenderDelay", "label": "Element render delay", "duration": 436.67999999999995}]}, {"type": "node", "lhId": "page-0-P", "path": "1,H<PERSON>L,1,BODY,0,<PERSON><PERSON>,1,DIV,0,DIV,1,P", "selector": "main.flex > div.relative > div.text-center > p.text-lg", "boundingRect": {"top": 232, "bottom": 372, "left": 96, "right": 316, "width": 220, "height": 140}, "snippet": "<p class=\"text-lg text-gray-600 dark:text-gray-300\">", "nodeLabel": "A modern, stable web development platform built with Next.js, TypeScript, and T…"}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable older browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support older browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0, "scoreDisplayMode": "numeric", "displayValue": "Est savings of 10 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Wasted bytes"}], "items": [{"url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "wastedBytes": 10065, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 246, "column": 470}}, {"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 1029}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 415}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 528}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 906}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 1164}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 151}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:3000/_next/static/chunks/vendors.js?v=1753001567307", "urlProvider": "network", "line": 214, "column": 64}}]}}], "debugData": {"type": "debugdata", "wastedBytes": 10065}}, "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "network-tree", "chains": {}, "longestChain": {"duration": 0}}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) can move these network requests out of the critical path.", "score": 0, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "http://localhost:3000/_next/static/css/app/%5Blocale%5D/layout.css?v=1753001567307", "totalBytes": 9732}]}, "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) to prioritize your page's content.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-1-META", "path": "1,H<PERSON><PERSON>,0,HEAD,1,<PERSON><PERSON>", "selector": "head > meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">", "nodeLabel": "head > meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": null, "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.52}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 0, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 0, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 0, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 7, "group": "a11y-aria"}, {"id": "aria-text", "weight": 0, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 0, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 0, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 0, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 0, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 0, "group": "a11y-language"}, {"id": "image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "label", "weight": 0, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 0, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 0, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 0, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 0, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 0, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 0, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 0, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 0, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 0, "group": "a11y-language"}, {"id": "video-caption", "weight": 0, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": 1}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": 0.96}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 0, "group": "seo-crawl"}, {"id": "image-alt", "weight": 0, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 1, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": 0.82}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "localhost", "origins": ["http://localhost:3000"], "isFirstParty": true, "isUnrecognized": true}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 412, "height": 1256}, "nodes": {"page-0-P": {"id": "", "top": 232, "bottom": 372, "left": 96, "right": 316, "width": 220, "height": 140}, "page-1-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-2-BODY": {"id": "", "top": 0, "bottom": 1256, "left": 0, "right": 412, "width": 412, "height": 1256}, "page-3-SPAN": {"id": "", "top": 421, "bottom": 453, "left": 195, "right": 217, "width": 23, "height": 32}, "1-0-P": {"id": "", "top": 0, "bottom": 77, "left": 0, "right": 412, "width": 412, "height": 77}, "1-1-SPAN": {"id": "", "top": 421, "bottom": 453, "left": 195, "right": 217, "width": 23, "height": 32}, "1-2-BODY": {"id": "", "top": 0, "bottom": 1256, "left": 0, "right": 412, "width": 412, "height": 1256}, "1-3-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-4-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-5-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-6-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-7-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-8-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-9-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-10-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-11-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-12-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-13-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-14-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-15-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-16-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-17-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-18-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-19-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-20-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-21-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-22-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-23-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-24-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-25-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-26-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-27-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 1517.69, "name": "lh:config", "duration": 408.88, "entryType": "measure"}, {"startTime": 1522.02, "name": "lh:config:resolveArtifactsToDefns", "duration": 106.72, "entryType": "measure"}, {"startTime": 1926.67, "name": "lh:runner:gather", "duration": 7030.1, "entryType": "measure"}, {"startTime": 2028.1, "name": "lh:driver:connect", "duration": 6.81, "entryType": "measure"}, {"startTime": 2035.03, "name": "lh:driver:navigate", "duration": 40.95, "entryType": "measure"}, {"startTime": 2076.16, "name": "lh:gather:getBenchmarkIndex", "duration": 1004.18, "entryType": "measure"}, {"startTime": 3080.44, "name": "lh:gather:getVersion", "duration": 0.85, "entryType": "measure"}, {"startTime": 3082.07, "name": "lh:prepare:navigationMode", "duration": 57.89, "entryType": "measure"}, {"startTime": 3097.83, "name": "lh:storage:clearDataForOrigin", "duration": 27.17, "entryType": "measure"}, {"startTime": 3125.14, "name": "lh:storage:clearBrowserCaches", "duration": 13.36, "entryType": "measure"}, {"startTime": 3139.15, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 0.74, "entryType": "measure"}, {"startTime": 3231.64, "name": "lh:driver:navigate", "duration": 3222.41, "entryType": "measure"}, {"startTime": 6963.18, "name": "lh:computed:NetworkRecords", "duration": 0.55, "entryType": "measure"}, {"startTime": 6963.98, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.06, "entryType": "measure"}, {"startTime": 6964.04, "name": "lh:gather:getArtifact:Trace", "duration": 0.11, "entryType": "measure"}, {"startTime": 6964.16, "name": "lh:gather:getArtifact:Accessibility", "duration": 91.64, "entryType": "measure"}, {"startTime": 7055.83, "name": "lh:gather:getArtifact:AnchorElements", "duration": 4.08, "entryType": "measure"}, {"startTime": 7059.93, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.09, "entryType": "measure"}, {"startTime": 7060.04, "name": "lh:gather:getArtifact:CSSUsage", "duration": 10.63, "entryType": "measure"}, {"startTime": 7070.7, "name": "lh:gather:getArtifact:Doctype", "duration": 0.84, "entryType": "measure"}, {"startTime": 7071.55, "name": "lh:gather:getArtifact:DOMStats", "duration": 2.55, "entryType": "measure"}, {"startTime": 7074.11, "name": "lh:gather:getArtifact:FontSize", "duration": 4.95, "entryType": "measure"}, {"startTime": 7079.08, "name": "lh:gather:getArtifact:Inputs", "duration": 1.45, "entryType": "measure"}, {"startTime": 7080.54, "name": "lh:gather:getArtifact:ImageElements", "duration": 4.27, "entryType": "measure"}, {"startTime": 7084.98, "name": "lh:gather:getArtifact:InspectorIssues", "duration": 0.21, "entryType": "measure"}, {"startTime": 7085.2, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.16, "entryType": "measure"}, {"startTime": 7085.37, "name": "lh:gather:getArtifact:LinkElements", "duration": 2.66, "entryType": "measure"}, {"startTime": 7087.6, "name": "lh:computed:MainResource", "duration": 0.09, "entryType": "measure"}, {"startTime": 7088.05, "name": "lh:gather:getArtifact:MainDocumentContent", "duration": 1.17, "entryType": "measure"}, {"startTime": 7089.24, "name": "lh:gather:getArtifact:MetaElements", "duration": 2.32, "entryType": "measure"}, {"startTime": 7091.59, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.16, "entryType": "measure"}, {"startTime": 7091.78, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.2, "entryType": "measure"}, {"startTime": 7092.01, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 0.31, "entryType": "measure"}, {"startTime": 7092.33, "name": "lh:gather:getArtifact:RobotsTxt", "duration": 23.48, "entryType": "measure"}, {"startTime": 7115.85, "name": "lh:gather:getArtifact:Scripts", "duration": 0.12, "entryType": "measure"}, {"startTime": 7115.99, "name": "lh:gather:getArtifact:SourceMaps", "duration": 0.04, "entryType": "measure"}, {"startTime": 7116.03, "name": "lh:gather:getArtifact:Stacks", "duration": 8.49, "entryType": "measure"}, {"startTime": 7116.09, "name": "lh:gather:collectStacks", "duration": 8.42, "entryType": "measure"}, {"startTime": 7124.54, "name": "lh:gather:getArtifact:Stylesheets", "duration": 3.94, "entryType": "measure"}, {"startTime": 7128.5, "name": "lh:gather:getArtifact:TraceElements", "duration": 399.59, "entryType": "measure"}, {"startTime": 7128.72, "name": "lh:computed:TraceEngineResult", "duration": 384.74, "entryType": "measure"}, {"startTime": 7128.78, "name": "lh:computed:ProcessedTrace", "duration": 15.49, "entryType": "measure"}, {"startTime": 7144.95, "name": "lh:computed:TraceEngineResult:total", "duration": 365.81, "entryType": "measure"}, {"startTime": 7145.02, "name": "lh:computed:TraceEngineResult:parse", "duration": 131.63, "entryType": "measure"}, {"startTime": 7145.85, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 64.7, "entryType": "measure"}, {"startTime": 7210.59, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 1.44, "entryType": "measure"}, {"startTime": 7212.34, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 1.6, "entryType": "measure"}, {"startTime": 7213.97, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 1.3, "entryType": "measure"}, {"startTime": 7215.29, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 1.29, "entryType": "measure"}, {"startTime": 7216.59, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 1.29, "entryType": "measure"}, {"startTime": 7217.99, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 3.2, "entryType": "measure"}, {"startTime": 7221.22, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 7.61, "entryType": "measure"}, {"startTime": 7228.86, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 2.48, "entryType": "measure"}, {"startTime": 7231.37, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 1.72, "entryType": "measure"}, {"startTime": 7233.11, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 0.07, "entryType": "measure"}, {"startTime": 7233.2, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 1.25, "entryType": "measure"}, {"startTime": 7234.48, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 1.4, "entryType": "measure"}, {"startTime": 7235.89, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 1.42, "entryType": "measure"}, {"startTime": 7237.32, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 15.34, "entryType": "measure"}, {"startTime": 7252.72, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 0.92, "entryType": "measure"}, {"startTime": 7253.68, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 1.34, "entryType": "measure"}, {"startTime": 7255.05, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 0.28, "entryType": "measure"}, {"startTime": 7255.34, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 1.26, "entryType": "measure"}, {"startTime": 7256.61, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 1.84, "entryType": "measure"}, {"startTime": 7258.47, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 1.32, "entryType": "measure"}, {"startTime": 7259.8, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 1.26, "entryType": "measure"}, {"startTime": 7261.07, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 2.87, "entryType": "measure"}, {"startTime": 7263.96, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 2.13, "entryType": "measure"}, {"startTime": 7266.11, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 1.27, "entryType": "measure"}, {"startTime": 7267.41, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 1.26, "entryType": "measure"}, {"startTime": 7268.68, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 1.68, "entryType": "measure"}, {"startTime": 7270.38, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 1.2, "entryType": "measure"}, {"startTime": 7271.59, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 1.35, "entryType": "measure"}, {"startTime": 7272.95, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 1.23, "entryType": "measure"}, {"startTime": 7274.19, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 1.34, "entryType": "measure"}, {"startTime": 7275.55, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 1.07, "entryType": "measure"}, {"startTime": 7276.65, "name": "lh:computed:TraceEngineResult:insights", "duration": 234.1, "entryType": "measure"}, {"startTime": 7276.81, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 7.61, "entryType": "measure"}, {"startTime": 7284.56, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.44, "entryType": "measure"}, {"startTime": 7285.03, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 0.41, "entryType": "measure"}, {"startTime": 7285.45, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.2, "entryType": "measure"}, {"startTime": 7285.65, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.27, "entryType": "measure"}, {"startTime": 7285.92, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 1.1, "entryType": "measure"}, {"startTime": 7287.04, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.21, "entryType": "measure"}, {"startTime": 7287.27, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.16, "entryType": "measure"}, {"startTime": 7287.43, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.17, "entryType": "measure"}, {"startTime": 7287.61, "name": "lh:computed:TraceEngineResult:insights:InteractionToNextPaint", "duration": 0.07, "entryType": "measure"}, {"startTime": 7287.68, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.11, "entryType": "measure"}, {"startTime": 7287.8, "name": "lh:computed:TraceEngineResult:insights:LCPPhases", "duration": 0.15, "entryType": "measure"}, {"startTime": 7287.96, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 217.58, "entryType": "measure"}, {"startTime": 7505.58, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 0.52, "entryType": "measure"}, {"startTime": 7506.15, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 0.37, "entryType": "measure"}, {"startTime": 7506.54, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.16, "entryType": "measure"}, {"startTime": 7506.71, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.12, "entryType": "measure"}, {"startTime": 7506.84, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 3.44, "entryType": "measure"}, {"startTime": 7510.29, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.11, "entryType": "measure"}, {"startTime": 7514.46, "name": "lh:computed:ProcessedNavigation", "duration": 0.34, "entryType": "measure"}, {"startTime": 7514.84, "name": "lh:computed:CumulativeLayoutShift", "duration": 7.68, "entryType": "measure"}, {"startTime": 7522.89, "name": "lh:computed:Responsiveness", "duration": 0.09, "entryType": "measure"}, {"startTime": 7528.11, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.93, "entryType": "measure"}, {"startTime": 7529.05, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1086.18, "entryType": "measure"}, {"startTime": 8615.27, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 329.19, "entryType": "measure"}, {"startTime": 8956.99, "name": "lh:runner:audit", "duration": 706.17, "entryType": "measure"}, {"startTime": 8957.05, "name": "lh:runner:auditing", "duration": 705.78, "entryType": "measure"}, {"startTime": 8957.68, "name": "lh:audit:is-on-https", "duration": 0.75, "entryType": "measure"}, {"startTime": 8958.57, "name": "lh:audit:redirects-http", "duration": 0.42, "entryType": "measure"}, {"startTime": 8959.11, "name": "lh:audit:viewport", "duration": 0.76, "entryType": "measure"}, {"startTime": 8959.26, "name": "lh:computed:ViewportMeta", "duration": 0.31, "entryType": "measure"}, {"startTime": 8959.98, "name": "lh:audit:first-contentful-paint", "duration": 7.93, "entryType": "measure"}, {"startTime": 8960.16, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 4.56, "entryType": "measure"}, {"startTime": 8960.27, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 4.43, "entryType": "measure"}, {"startTime": 8960.33, "name": "lh:computed:PageDependencyGraph", "duration": 1.3, "entryType": "measure"}, {"startTime": 8961.63, "name": "lh:computed:LoadSimulator", "duration": 0.83, "entryType": "measure"}, {"startTime": 8961.65, "name": "lh:computed:NetworkAnalysis", "duration": 0.67, "entryType": "measure"}, {"startTime": 8968.03, "name": "lh:audit:largest-contentful-paint", "duration": 2.47, "entryType": "measure"}, {"startTime": 8968.26, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 1.74, "entryType": "measure"}, {"startTime": 8968.3, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 1.69, "entryType": "measure"}, {"startTime": 8970.63, "name": "lh:audit:first-meaningful-paint", "duration": 0.36, "entryType": "measure"}, {"startTime": 8971.08, "name": "lh:audit:speed-index", "duration": 121.49, "entryType": "measure"}, {"startTime": 8971.23, "name": "lh:computed:SpeedIndex", "duration": 120.85, "entryType": "measure"}, {"startTime": 8971.27, "name": "lh:computed:LanternSpeedIndex", "duration": 120.81, "entryType": "measure"}, {"startTime": 8971.29, "name": "lh:computed:Speedline", "duration": 119.23, "entryType": "measure"}, {"startTime": 9092.58, "name": "lh:audit:screenshot-thumbnails", "duration": 0.25, "entryType": "measure"}, {"startTime": 9092.84, "name": "lh:audit:final-screenshot", "duration": 0.67, "entryType": "measure"}, {"startTime": 9092.9, "name": "lh:computed:Screenshots", "duration": 0.6, "entryType": "measure"}, {"startTime": 9093.63, "name": "lh:audit:total-blocking-time", "duration": 2.07, "entryType": "measure"}, {"startTime": 9093.79, "name": "lh:computed:TotalBlockingTime", "duration": 1.57, "entryType": "measure"}, {"startTime": 9093.83, "name": "lh:computed:LanternTotalBlockingTime", "duration": 1.54, "entryType": "measure"}, {"startTime": 9093.86, "name": "lh:computed:LanternInteractive", "duration": 0.82, "entryType": "measure"}, {"startTime": 9095.79, "name": "lh:audit:max-potential-fid", "duration": 1.33, "entryType": "measure"}, {"startTime": 9095.93, "name": "lh:computed:MaxPotentialFID", "duration": 0.68, "entryType": "measure"}, {"startTime": 9095.95, "name": "lh:computed:LanternMaxPotentialFID", "duration": 0.66, "entryType": "measure"}, {"startTime": 9097.21, "name": "lh:audit:cumulative-layout-shift", "duration": 0.51, "entryType": "measure"}, {"startTime": 9097.82, "name": "lh:audit:errors-in-console", "duration": 0.68, "entryType": "measure"}, {"startTime": 9098.06, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.03, "entryType": "measure"}, {"startTime": 9098.6, "name": "lh:audit:server-response-time", "duration": 0.54, "entryType": "measure"}, {"startTime": 9099.23, "name": "lh:audit:interactive", "duration": 0.43, "entryType": "measure"}, {"startTime": 9099.36, "name": "lh:computed:Interactive", "duration": 0.03, "entryType": "measure"}, {"startTime": 9099.74, "name": "lh:audit:user-timings", "duration": 0.84, "entryType": "measure"}, {"startTime": 9099.87, "name": "lh:computed:UserTimings", "duration": 0.46, "entryType": "measure"}, {"startTime": 9100.68, "name": "lh:audit:critical-request-chains", "duration": 1.44, "entryType": "measure"}, {"startTime": 9100.82, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 0.25, "entryType": "measure"}, {"startTime": 9102.29, "name": "lh:audit:redirects", "duration": 1.21, "entryType": "measure"}, {"startTime": 9103.81, "name": "lh:audit:image-aspect-ratio", "duration": 0.57, "entryType": "measure"}, {"startTime": 9104.51, "name": "lh:audit:image-size-responsive", "duration": 0.72, "entryType": "measure"}, {"startTime": 9104.71, "name": "lh:computed:ImageRecords", "duration": 0.13, "entryType": "measure"}, {"startTime": 9105.36, "name": "lh:audit:deprecations", "duration": 0.49, "entryType": "measure"}, {"startTime": 9105.96, "name": "lh:audit:third-party-cookies", "duration": 0.49, "entryType": "measure"}, {"startTime": 9106.64, "name": "lh:audit:mainthread-work-breakdown", "duration": 4.08, "entryType": "measure"}, {"startTime": 9106.94, "name": "lh:computed:MainThreadTasks", "duration": 3.02, "entryType": "measure"}, {"startTime": 9110.84, "name": "lh:audit:bootup-time", "duration": 7.42, "entryType": "measure"}, {"startTime": 9111.65, "name": "lh:computed:TBTImpactTasks", "duration": 4.92, "entryType": "measure"}, {"startTime": 9118.45, "name": "lh:audit:uses-rel-preconnect", "duration": 1.06, "entryType": "measure"}, {"startTime": 9119.65, "name": "lh:audit:font-display", "duration": 1.13, "entryType": "measure"}, {"startTime": 9120.8, "name": "lh:audit:diagnostics", "duration": 0.33, "entryType": "measure"}, {"startTime": 9121.14, "name": "lh:audit:network-requests", "duration": 1.02, "entryType": "measure"}, {"startTime": 9121.23, "name": "lh:computed:EntityClassification", "duration": 0.74, "entryType": "measure"}, {"startTime": 9122.26, "name": "lh:audit:network-rtt", "duration": 0.48, "entryType": "measure"}, {"startTime": 9122.84, "name": "lh:audit:network-server-latency", "duration": 0.42, "entryType": "measure"}, {"startTime": 9123.26, "name": "lh:audit:main-thread-tasks", "duration": 0.1, "entryType": "measure"}, {"startTime": 9123.36, "name": "lh:audit:metrics", "duration": 1.72, "entryType": "measure"}, {"startTime": 9123.42, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 1.47, "entryType": "measure"}, {"startTime": 9123.56, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.18, "entryType": "measure"}, {"startTime": 9123.75, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.06, "entryType": "measure"}, {"startTime": 9123.84, "name": "lh:computed:LCPBreakdown", "duration": 0.65, "entryType": "measure"}, {"startTime": 9123.88, "name": "lh:computed:TimeToFirstByte", "duration": 0.07, "entryType": "measure"}, {"startTime": 9123.95, "name": "lh:computed:LCPImageRecord", "duration": 0.53, "entryType": "measure"}, {"startTime": 9125.09, "name": "lh:audit:resource-summary", "duration": 0.52, "entryType": "measure"}, {"startTime": 9125.18, "name": "lh:computed:ResourceSummary", "duration": 0.17, "entryType": "measure"}, {"startTime": 9125.76, "name": "lh:audit:third-party-summary", "duration": 2.04, "entryType": "measure"}, {"startTime": 9127.92, "name": "lh:audit:third-party-facades", "duration": 1.79, "entryType": "measure"}, {"startTime": 9129.81, "name": "lh:audit:largest-contentful-paint-element", "duration": 0.77, "entryType": "measure"}, {"startTime": 9130.69, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.43, "entryType": "measure"}, {"startTime": 9131.24, "name": "lh:audit:layout-shifts", "duration": 0.5, "entryType": "measure"}, {"startTime": 9131.83, "name": "lh:audit:long-tasks", "duration": 4.38, "entryType": "measure"}, {"startTime": 9136.31, "name": "lh:audit:non-composited-animations", "duration": 0.47, "entryType": "measure"}, {"startTime": 9136.9, "name": "lh:audit:unsized-images", "duration": 0.46, "entryType": "measure"}, {"startTime": 9137.49, "name": "lh:audit:valid-source-maps", "duration": 0.65, "entryType": "measure"}, {"startTime": 9138.23, "name": "lh:audit:prioritize-lcp-image", "duration": 0.35, "entryType": "measure"}, {"startTime": 9138.67, "name": "lh:audit:csp-xss", "duration": 0.49, "entryType": "measure"}, {"startTime": 9139.26, "name": "lh:audit:has-hsts", "duration": 0.47, "entryType": "measure"}, {"startTime": 9139.82, "name": "lh:audit:origin-isolation", "duration": 0.46, "entryType": "measure"}, {"startTime": 9140.37, "name": "lh:audit:clickjacking-mitigation", "duration": 0.42, "entryType": "measure"}, {"startTime": 9140.8, "name": "lh:audit:script-treemap-data", "duration": 76.09, "entryType": "measure"}, {"startTime": 9140.96, "name": "lh:computed:ModuleDuplication", "duration": 0.08, "entryType": "measure"}, {"startTime": 9141.05, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.21, "entryType": "measure"}, {"startTime": 9141.3, "name": "lh:computed:UnusedJavascriptSummary", "duration": 3.19, "entryType": "measure"}, {"startTime": 9144.53, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.23, "entryType": "measure"}, {"startTime": 9144.79, "name": "lh:computed:UnusedJavascriptSummary", "duration": 1.34, "entryType": "measure"}, {"startTime": 9146.15, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 9146.2, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.03, "entryType": "measure"}, {"startTime": 9146.26, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 9146.35, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 9146.48, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.07, "entryType": "measure"}, {"startTime": 9146.58, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.04, "entryType": "measure"}, {"startTime": 9146.66, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.18, "entryType": "measure"}, {"startTime": 9146.9, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.22, "entryType": "measure"}, {"startTime": 9147.17, "name": "lh:computed:UnusedJavascriptSummary", "duration": 69.62, "entryType": "measure"}, {"startTime": 9217.08, "name": "lh:audit:accesskeys", "duration": 0.6, "entryType": "measure"}, {"startTime": 9217.8, "name": "lh:audit:aria-allowed-attr", "duration": 1.76, "entryType": "measure"}, {"startTime": 9219.66, "name": "lh:audit:aria-allowed-role", "duration": 1.92, "entryType": "measure"}, {"startTime": 9221.71, "name": "lh:audit:aria-command-name", "duration": 0.43, "entryType": "measure"}, {"startTime": 9222.27, "name": "lh:audit:aria-conditional-attr", "duration": 1.62, "entryType": "measure"}, {"startTime": 9223.99, "name": "lh:audit:aria-deprecated-role", "duration": 1.5, "entryType": "measure"}, {"startTime": 9225.59, "name": "lh:audit:aria-dialog-name", "duration": 0.4, "entryType": "measure"}, {"startTime": 9226.17, "name": "lh:audit:aria-hidden-body", "duration": 1.71, "entryType": "measure"}, {"startTime": 9227.99, "name": "lh:audit:aria-hidden-focus", "duration": 0.48, "entryType": "measure"}, {"startTime": 9228.57, "name": "lh:audit:aria-input-field-name", "duration": 0.5, "entryType": "measure"}, {"startTime": 9229.17, "name": "lh:audit:aria-meter-name", "duration": 0.62, "entryType": "measure"}, {"startTime": 9229.91, "name": "lh:audit:aria-progressbar-name", "duration": 0.57, "entryType": "measure"}, {"startTime": 9230.58, "name": "lh:audit:aria-prohibited-attr", "duration": 1.67, "entryType": "measure"}, {"startTime": 9232.35, "name": "lh:audit:aria-required-attr", "duration": 3.46, "entryType": "measure"}, {"startTime": 9235.93, "name": "lh:audit:aria-required-children", "duration": 0.61, "entryType": "measure"}, {"startTime": 9236.64, "name": "lh:audit:aria-required-parent", "duration": 0.59, "entryType": "measure"}, {"startTime": 9237.32, "name": "lh:audit:aria-roles", "duration": 1.55, "entryType": "measure"}, {"startTime": 9238.98, "name": "lh:audit:aria-text", "duration": 0.6, "entryType": "measure"}, {"startTime": 9239.67, "name": "lh:audit:aria-toggle-field-name", "duration": 0.59, "entryType": "measure"}, {"startTime": 9240.35, "name": "lh:audit:aria-tooltip-name", "duration": 0.65, "entryType": "measure"}, {"startTime": 9241.1, "name": "lh:audit:aria-treeitem-name", "duration": 0.65, "entryType": "measure"}, {"startTime": 9241.83, "name": "lh:audit:aria-valid-attr-value", "duration": 1.53, "entryType": "measure"}, {"startTime": 9243.46, "name": "lh:audit:aria-valid-attr", "duration": 4.66, "entryType": "measure"}, {"startTime": 9248.24, "name": "lh:audit:button-name", "duration": 0.71, "entryType": "measure"}, {"startTime": 9249.04, "name": "lh:audit:bypass", "duration": 0.7, "entryType": "measure"}, {"startTime": 9249.84, "name": "lh:audit:color-contrast", "duration": 1.54, "entryType": "measure"}, {"startTime": 9251.49, "name": "lh:audit:definition-list", "duration": 0.8, "entryType": "measure"}, {"startTime": 9252.39, "name": "lh:audit:dlitem", "duration": 0.81, "entryType": "measure"}, {"startTime": 9253.29, "name": "lh:audit:document-title", "duration": 1.64, "entryType": "measure"}, {"startTime": 9255.03, "name": "lh:audit:duplicate-id-aria", "duration": 0.78, "entryType": "measure"}, {"startTime": 9255.9, "name": "lh:audit:empty-heading", "duration": 1.75, "entryType": "measure"}, {"startTime": 9257.78, "name": "lh:audit:form-field-multiple-labels", "duration": 0.87, "entryType": "measure"}, {"startTime": 9258.75, "name": "lh:audit:frame-title", "duration": 0.89, "entryType": "measure"}, {"startTime": 9259.73, "name": "lh:audit:heading-order", "duration": 4.16, "entryType": "measure"}, {"startTime": 9264.03, "name": "lh:audit:html-has-lang", "duration": 1.79, "entryType": "measure"}, {"startTime": 9265.91, "name": "lh:audit:html-lang-valid", "duration": 1.62, "entryType": "measure"}, {"startTime": 9267.64, "name": "lh:audit:html-xml-lang-mismatch", "duration": 0.92, "entryType": "measure"}, {"startTime": 9268.65, "name": "lh:audit:identical-links-same-purpose", "duration": 0.97, "entryType": "measure"}, {"startTime": 9269.72, "name": "lh:audit:image-alt", "duration": 1.01, "entryType": "measure"}, {"startTime": 9270.84, "name": "lh:audit:image-redundant-alt", "duration": 0.98, "entryType": "measure"}, {"startTime": 9271.91, "name": "lh:audit:input-button-name", "duration": 0.97, "entryType": "measure"}, {"startTime": 9272.98, "name": "lh:audit:input-image-alt", "duration": 3.66, "entryType": "measure"}, {"startTime": 9276.75, "name": "lh:audit:label-content-name-mismatch", "duration": 1.04, "entryType": "measure"}, {"startTime": 9277.88, "name": "lh:audit:label", "duration": 0.97, "entryType": "measure"}, {"startTime": 9278.94, "name": "lh:audit:landmark-one-main", "duration": 1.47, "entryType": "measure"}, {"startTime": 9280.5, "name": "lh:audit:link-name", "duration": 1.09, "entryType": "measure"}, {"startTime": 9281.69, "name": "lh:audit:link-in-text-block", "duration": 1.08, "entryType": "measure"}, {"startTime": 9282.87, "name": "lh:audit:list", "duration": 1.29, "entryType": "measure"}, {"startTime": 9284.28, "name": "lh:audit:listitem", "duration": 1.21, "entryType": "measure"}, {"startTime": 9285.6, "name": "lh:audit:meta-refresh", "duration": 1.19, "entryType": "measure"}, {"startTime": 9289.54, "name": "lh:audit:meta-viewport", "duration": 1.9, "entryType": "measure"}, {"startTime": 9291.55, "name": "lh:audit:object-alt", "duration": 1.18, "entryType": "measure"}, {"startTime": 9292.82, "name": "lh:audit:select-name", "duration": 1.17, "entryType": "measure"}, {"startTime": 9294.08, "name": "lh:audit:skip-link", "duration": 1.16, "entryType": "measure"}, {"startTime": 9295.33, "name": "lh:audit:tabindex", "duration": 1.25, "entryType": "measure"}, {"startTime": 9296.67, "name": "lh:audit:table-duplicate-name", "duration": 1.43, "entryType": "measure"}, {"startTime": 9298.23, "name": "lh:audit:table-fake-caption", "duration": 1.5, "entryType": "measure"}, {"startTime": 9299.84, "name": "lh:audit:target-size", "duration": 4.09, "entryType": "measure"}, {"startTime": 9304.05, "name": "lh:audit:td-has-header", "duration": 1.57, "entryType": "measure"}, {"startTime": 9305.75, "name": "lh:audit:td-headers-attr", "duration": 1.59, "entryType": "measure"}, {"startTime": 9307.44, "name": "lh:audit:th-has-data-cells", "duration": 1.57, "entryType": "measure"}, {"startTime": 9309.11, "name": "lh:audit:valid-lang", "duration": 1.51, "entryType": "measure"}, {"startTime": 9310.72, "name": "lh:audit:video-caption", "duration": 1.52, "entryType": "measure"}, {"startTime": 9312.25, "name": "lh:audit:custom-controls-labels", "duration": 0.06, "entryType": "measure"}, {"startTime": 9312.32, "name": "lh:audit:custom-controls-roles", "duration": 0.04, "entryType": "measure"}, {"startTime": 9312.36, "name": "lh:audit:focus-traps", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.38, "name": "lh:audit:focusable-controls", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.4, "name": "lh:audit:interactive-element-affordance", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.41, "name": "lh:audit:logical-tab-order", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.43, "name": "lh:audit:managed-focus", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.44, "name": "lh:audit:offscreen-content-hidden", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.46, "name": "lh:audit:use-landmarks", "duration": 0.01, "entryType": "measure"}, {"startTime": 9312.48, "name": "lh:audit:visual-order-follows-dom", "duration": 0.02, "entryType": "measure"}, {"startTime": 9312.59, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.2, "entryType": "measure"}, {"startTime": 9313.89, "name": "lh:audit:total-byte-weight", "duration": 0.68, "entryType": "measure"}, {"startTime": 9314.65, "name": "lh:audit:offscreen-images", "duration": 4.42, "entryType": "measure"}, {"startTime": 9319.19, "name": "lh:audit:render-blocking-resources", "duration": 1.36, "entryType": "measure"}, {"startTime": 9319.53, "name": "lh:computed:UnusedCSS", "duration": 0.23, "entryType": "measure"}, {"startTime": 9319.77, "name": "lh:computed:NavigationInsights", "duration": 0.04, "entryType": "measure"}, {"startTime": 9319.86, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.05, "entryType": "measure"}, {"startTime": 9320.63, "name": "lh:audit:unminified-css", "duration": 5.18, "entryType": "measure"}, {"startTime": 9325.89, "name": "lh:audit:unminified-javascript", "duration": 96.57, "entryType": "measure"}, {"startTime": 9422.55, "name": "lh:audit:unused-css-rules", "duration": 0.98, "entryType": "measure"}, {"startTime": 9423.61, "name": "lh:audit:unused-javascript", "duration": 1.44, "entryType": "measure"}, {"startTime": 9425.13, "name": "lh:audit:modern-image-formats", "duration": 0.85, "entryType": "measure"}, {"startTime": 9426.05, "name": "lh:audit:uses-optimized-images", "duration": 1.16, "entryType": "measure"}, {"startTime": 9427.3, "name": "lh:audit:uses-text-compression", "duration": 0.94, "entryType": "measure"}, {"startTime": 9428.31, "name": "lh:audit:uses-responsive-images", "duration": 0.82, "entryType": "measure"}, {"startTime": 9429.2, "name": "lh:audit:efficient-animated-content", "duration": 0.91, "entryType": "measure"}, {"startTime": 9430.19, "name": "lh:audit:duplicated-javascript", "duration": 1.03, "entryType": "measure"}, {"startTime": 9431.31, "name": "lh:audit:legacy-javascript", "duration": 204.52, "entryType": "measure"}, {"startTime": 9635.96, "name": "lh:audit:doctype", "duration": 0.46, "entryType": "measure"}, {"startTime": 9636.53, "name": "lh:audit:charset", "duration": 0.49, "entryType": "measure"}, {"startTime": 9637.14, "name": "lh:audit:dom-size", "duration": 1.23, "entryType": "measure"}, {"startTime": 9638.47, "name": "lh:audit:geolocation-on-start", "duration": 0.49, "entryType": "measure"}, {"startTime": 9639.07, "name": "lh:audit:inspector-issues", "duration": 0.37, "entryType": "measure"}, {"startTime": 9639.53, "name": "lh:audit:no-document-write", "duration": 0.35, "entryType": "measure"}, {"startTime": 9639.95, "name": "lh:audit:js-libraries", "duration": 0.28, "entryType": "measure"}, {"startTime": 9640.4, "name": "lh:audit:notification-on-start", "duration": 0.35, "entryType": "measure"}, {"startTime": 9640.84, "name": "lh:audit:paste-preventing-inputs", "duration": 0.34, "entryType": "measure"}, {"startTime": 9641.25, "name": "lh:audit:uses-http2", "duration": 1.3, "entryType": "measure"}, {"startTime": 9642.67, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.4, "entryType": "measure"}, {"startTime": 9643.18, "name": "lh:audit:meta-description", "duration": 0.33, "entryType": "measure"}, {"startTime": 9643.6, "name": "lh:audit:http-status-code", "duration": 0.32, "entryType": "measure"}, {"startTime": 9644.02, "name": "lh:audit:font-size", "duration": 1.63, "entryType": "measure"}, {"startTime": 9645.76, "name": "lh:audit:link-text", "duration": 0.36, "entryType": "measure"}, {"startTime": 9646.21, "name": "lh:audit:crawlable-anchors", "duration": 0.37, "entryType": "measure"}, {"startTime": 9646.68, "name": "lh:audit:is-crawlable", "duration": 0.65, "entryType": "measure"}, {"startTime": 9647.42, "name": "lh:audit:robots-txt", "duration": 0.4, "entryType": "measure"}, {"startTime": 9647.95, "name": "lh:audit:hreflang", "duration": 0.49, "entryType": "measure"}, {"startTime": 9648.54, "name": "lh:audit:canonical", "duration": 0.51, "entryType": "measure"}, {"startTime": 9649.12, "name": "lh:audit:structured-data", "duration": 0.23, "entryType": "measure"}, {"startTime": 9649.45, "name": "lh:audit:bf-cache", "duration": 0.56, "entryType": "measure"}, {"startTime": 9650.1, "name": "lh:audit:cache-insight", "duration": 0.47, "entryType": "measure"}, {"startTime": 9650.67, "name": "lh:audit:cls-culprits-insight", "duration": 0.41, "entryType": "measure"}, {"startTime": 9651.17, "name": "lh:audit:document-latency-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 9651.61, "name": "lh:audit:dom-size-insight", "duration": 0.45, "entryType": "measure"}, {"startTime": 9652.15, "name": "lh:audit:duplicated-javascript-insight", "duration": 0.38, "entryType": "measure"}, {"startTime": 9654.1, "name": "lh:audit:font-display-insight", "duration": 0.5, "entryType": "measure"}, {"startTime": 9654.7, "name": "lh:audit:forced-reflow-insight", "duration": 0.43, "entryType": "measure"}, {"startTime": 9655.23, "name": "lh:audit:image-delivery-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 9655.69, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 0.36, "entryType": "measure"}, {"startTime": 9656.14, "name": "lh:audit:lcp-discovery-insight", "duration": 0.34, "entryType": "measure"}, {"startTime": 9656.58, "name": "lh:audit:lcp-phases-insight", "duration": 0.48, "entryType": "measure"}, {"startTime": 9657.19, "name": "lh:audit:legacy-javascript-insight", "duration": 0.64, "entryType": "measure"}, {"startTime": 9657.92, "name": "lh:audit:modern-http-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 9658.38, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.37, "entryType": "measure"}, {"startTime": 9658.84, "name": "lh:audit:render-blocking-insight", "duration": 0.42, "entryType": "measure"}, {"startTime": 9659.36, "name": "lh:audit:third-parties-insight", "duration": 2.97, "entryType": "measure"}, {"startTime": 9662.44, "name": "lh:audit:viewport-insight", "duration": 0.39, "entryType": "measure"}, {"startTime": 9662.83, "name": "lh:runner:generate", "duration": 0.32, "entryType": "measure"}], "total": 7736.27}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/gather/driver/navigation.js | warningRedirected": [{"values": {"requested": "http://localhost:3000/", "final": "http://localhost:3000/en"}, "path": "runWarnings[0]"}], "core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 922.13715}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 10185.137149999999}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 1326.7649040245865}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 10335.137149999999}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 1504.6240000000128}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 1340.6440000000048}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 1113}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 1163}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 0.07695}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 7.379049999999999}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 10185.137149999999}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | failureTitle": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/lib/i18n/i18n.js | columnSource": ["audits[errors-in-console].details.headings[0].label", "audits[font-size].details.headings[0].label"], "core/lib/i18n/i18n.js | columnDescription": ["audits[errors-in-console].details.headings[1].label", "audits[csp-xss].details.headings[0].label", "audits[has-hsts].details.headings[0].label", "audits[origin-isolation].details.headings[0].label"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 33.7}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits.redirects.details.headings[0].label", "audits[bootup-time].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[long-tasks].details.headings[0].label", "audits[valid-source-maps].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[render-blocking-resources].details.headings[0].label", "audits[unminified-javascript].details.headings[0].label", "audits[legacy-javascript].details.headings[0].label", "audits[legacy-javascript-insight].details.headings[0].label", "audits[render-blocking-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits.redirects.details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/critical-request-chains.js | displayValue": [{"values": {"itemCount": 1}, "path": "audits[critical-request-chains].displayValue"}], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/lib/i18n/i18n.js | displayValueMsSavings": [{"values": {"wastedMs": 607.37905}, "path": "audits.redirects.displayValue"}, {"values": {"wastedMs": 155}, "path": "audits[render-blocking-resources].displayValue"}], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | failureTitle": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[render-blocking-resources].details.headings[1].label", "audits[unminified-javascript].details.headings[1].label", "audits[render-blocking-insight].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[dom-size].details.headings[1].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/long-tasks.js | displayValue": [{"values": {"itemCount": 2}, "path": "audits[long-tasks].displayValue"}], "core/lib/i18n/i18n.js | columnStartTime": ["audits[long-tasks].details.headings[1].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[long-tasks].details.headings[2].label", "audits[lcp-phases-insight].details.items[0].headings[1].label"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | failureTitle": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/valid-source-maps.js | columnMapURL": ["audits[valid-source-maps].details.headings[1].label"], "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": ["audits[valid-source-maps].details.items[0].subItems.items[0].error"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/csp-xss.js | columnDirective": ["audits[csp-xss].details.headings[1].label"], "core/audits/csp-xss.js | columnSeverity": ["audits[csp-xss].details.headings[2].label"], "core/lib/i18n/i18n.js | itemSeverityHigh": ["audits[csp-xss].details.items[0].severity", "audits[has-hsts].details.items[0].severity", "audits[origin-isolation].details.items[0].severity"], "core/audits/csp-xss.js | noCsp": ["audits[csp-xss].details.items[0].description"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/has-hsts.js | columnDirective": ["audits[has-hsts].details.headings[1].label"], "core/audits/has-hsts.js | columnSeverity": ["audits[has-hsts].details.headings[2].label"], "core/audits/has-hsts.js | noHsts": ["audits[has-hsts].details.items[0].description"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/origin-isolation.js | columnDirective": ["audits[origin-isolation].details.headings[1].label"], "core/audits/origin-isolation.js | columnSeverity": ["audits[origin-isolation].details.headings[2].label"], "core/audits/origin-isolation.js | noCoop": ["audits[origin-isolation].details.items[0].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | title": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | title": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | title": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 0}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 1625867}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[render-blocking-resources].details.headings[2].label", "audits[unminified-javascript].details.headings[2].label", "audits[legacy-javascript].details.headings[2].label", "audits[render-blocking-insight].details.headings[2].label"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 5135}, "path": "audits[unminified-javascript].displayValue"}, {"values": {"wastedBytes": 10061}, "path": "audits[legacy-javascript].displayValue"}, {"values": {"wastedBytes": 10065}, "path": "audits[legacy-javascript-insight].displayValue"}], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 35}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/font-size.js | displayValue": [{"values": {"decimalProportion": 1}, "path": "audits[font-size].displayValue"}], "core/audits/seo/font-size.js | columnSelector": ["audits[font-size].details.headings[1].label"], "core/audits/seo/font-size.js | columnPercentPageText": ["audits[font-size].details.headings[2].label"], "core/audits/seo/font-size.js | columnFontSize": ["audits[font-size].details.headings[3].label"], "core/audits/seo/font-size.js | legibleText": ["audits[font-size].details.items[0].source.value"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | title": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/hreflang.js | failureTitle": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/hreflang.js | notFullyQualified": ["audits.hreflang.details.items[0].subItems.items[0].reason", "audits.hreflang.details.items[1].subItems.items[0].reason"], "core/audits/seo/canonical.js | failureTitle": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/canonical.js | explanationRelative": [{"values": {"url": "/"}, "path": "audits.canonical.explanation"}], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | failureTitle": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "core/audits/bf-cache.js | displayValue": [{"values": {"itemCount": 4}, "path": "audits[bf-cache].displayValue"}], "core/audits/bf-cache.js | failureReasonColumn": ["audits[bf-cache].details.headings[0].label"], "core/audits/bf-cache.js | failureTypeColumn": ["audits[bf-cache].details.headings[1].label"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocket": ["audits[bf-cache].details.items[0].reason"], "core/audits/bf-cache.js | supportPendingFailureType": ["audits[bf-cache].details.items[0].failureType"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | mainResourceHasCacheControlNoStore": ["audits[bf-cache].details.items[1].reason"], "core/audits/bf-cache.js | notActionableFailureType": ["audits[bf-cache].details.items[1].failureType", "audits[bf-cache].details.items[2].failureType", "audits[bf-cache].details.items[3].failureType"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | jsNetworkRequestReceivedCacheControlNoStoreResource": ["audits[bf-cache].details.items[2].reason"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocketSticky": ["audits[bf-cache].details.items[3].reason"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | failedRedirects": [{"values": {"PH1": 1, "PH2": "217 ms"}, "path": "audits[document-latency-insight].details.items.noRedirects.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": [{"values": {"PH1": "34 ms"}, "path": "audits[document-latency-insight].details.items.serverResponseIsFast.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase": ["audits[lcp-phases-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte": ["audits[lcp-phases-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay": ["audits[lcp-phases-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnWastedBytes": ["audits[legacy-javascript-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}