# Local Development Environment Variables
# Copy this file to .env.local for local development

# Next.js Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Tucsenberg Web Stable"

# Development Environment
NODE_ENV=development

# Development Tools
NEXT_PUBLIC_SHOW_DEBUG_INFO=true
NEXT_PUBLIC_ENABLE_DEVTOOLS=true

# Local Development Database (if needed)
# DATABASE_URL="postgresql://username:password@localhost:5432/tucsenberg_dev"

# Local API Keys (for development only)
# OPENAI_API_KEY="your-dev-api-key"
# STRIPE_SECRET_KEY="sk_test_..."
# STRIPE_PUBLISHABLE_KEY="pk_test_..."

# Email Service (for local testing)
# SMTP_HOST=localhost
# SMTP_PORT=1025
# SMTP_USER=""
# SMTP_PASSWORD=""
# SMTP_FROM="<EMAIL>"

# Analytics (disabled in development)
# NEXT_PUBLIC_VERCEL_ANALYTICS_ID=""
# NEXT_PUBLIC_GA_MEASUREMENT_ID=""
