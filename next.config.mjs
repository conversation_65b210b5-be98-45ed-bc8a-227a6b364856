import { default as bundleAnalyzer } from '@next/bundle-analyzer';
import createNextIntlPlugin from 'next-intl/plugin';

/**
 * Next.js Configuration for Tucsenberg Web Stable
 *
 * Optimized configuration following stable tech stack requirements
 * with performance, security, and developer experience enhancements.
 * Includes internationalization support with next-intl.
 */

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');
const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

const nextConfig = {
  // TypeScript configuration - 严格模式，确保类型安全
  typescript: {
    // 启用严格的TypeScript检查，发现错误及时处理
    ignoreBuildErrors: false,
  },

  // ESLint configuration - 严格代码质量检查
  eslint: {
    // 启用构建时ESLint检查，确保代码质量
    ignoreDuringBuilds: false,
    dirs: ['src'],
  },

  // Experimental features for stable tech stack
  experimental: {
    // Optimize package imports for better performance and smaller bundles
    optimizePackageImports: [
      '@radix-ui/react-icons',
      '@radix-ui/react-slot',
      'lucide-react',
      'framer-motion',
    ],

    // Enable modern bundling optimizations
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },

    // Optimize server components
    serverComponentsExternalPackages: ['@prisma/client'],

    // Font optimization is handled by next/font/google automatically

    // Enable optimized CSS loading (disabled due to critters dependency issue)
    // optimizeCss: isProduction,

    // Enable WebAssembly support
    webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB'],

    // 性能优化：启用更多实验性功能
    optimizeServerReact: true,
    serverMinification: isProduction,

    // 启用并发特性
    serverActions: {
      allowedOrigins: ['localhost:3000', 'localhost:3001'],
    },
  },

  // Image optimization configuration
  images: {
    // Modern image formats for better performance
    formats: ['image/webp', 'image/avif'],

    // Responsive image sizes for different devices
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],

    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],

    // Image quality settings (removed as it's not a valid Next.js config option)

    // Allowed image domains (add your CDN domains here)
    domains: [
      'localhost',
      // Add your production domains here
      // 'your-domain.com',
      // 'cdn.your-domain.com',
    ],

    // Remote patterns for more flexible image sources
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.vercel.app',
      },
      {
        protocol: 'https',
        hostname: '**.netlify.app',
      },
    ],

    // Enable image optimization
    unoptimized: false,

    // Minimize layout shift
    minimumCacheTTL: 60,
  },

  // Compiler optimizations
  compiler: {
    // Remove console statements in production
    removeConsole: isProduction
      ? {
          exclude: ['error', 'warn'],
        }
      : false,

    // Remove React DevTools in production
    reactRemoveProperties: isProduction,

    // Enable SWC minification
    styledComponents: true,
  },

  // Output configuration
  output: 'standalone',

  // Generate source maps in development
  productionBrowserSourceMaps: false,

  // Optimize builds
  swcMinify: true,

  // Compression
  compress: true,

  // Power optimizations
  poweredByHeader: false,

  // Minimal webpack configuration for edge cases
  webpack: (config, { isServer }) => {
    // Only add fallback for client-side builds if needed
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    return config;
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // Security headers
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          // Performance headers
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // Redirects for SEO and user experience
  async redirects() {
    return [
      // Add your redirects here
      // {
      //   source: '/old-path',
      //   destination: '/new-path',
      //   permanent: true,
      // },
    ];
  },

  // Rewrites for API proxying or path mapping
  async rewrites() {
    return [
      // Add your rewrites here
      // {
      //   source: '/api/proxy/:path*',
      //   destination: 'https://external-api.com/:path*',
      // },
    ];
  },

  // Webpack configuration
  webpack: (config, { buildId, webpack, isServer }) => {
    // 性能优化：更精细的代码分割策略
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          // 框架代码单独打包
          framework: {
            test: /[\\/]node_modules[\\/](react|react-dom|next)[\\/]/,
            name: 'framework',
            chunks: 'all',
            priority: 40,
            enforce: true,
          },
          // UI 库单独打包
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react|framer-motion)[\\/]/,
            name: 'ui',
            chunks: 'all',
            priority: 30,
          },
          // 国际化库单独打包
          i18n: {
            test: /[\\/]node_modules[\\/](next-intl)[\\/]/,
            name: 'i18n',
            chunks: 'all',
            priority: 25,
          },
          // 其他第三方库
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 20,
          },
          // 公共代码
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 10,
            reuseExistingChunk: true,
          },
        },
      },
    };

    // Add custom webpack plugins
    config.plugins.push(
      new webpack.DefinePlugin({
        __DEV__: JSON.stringify(isDevelopment),
        __PROD__: JSON.stringify(isProduction),
        __BUILD_ID__: JSON.stringify(buildId),
      })
    );

    // SVG handling with optimization
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            svgo: true,
            svgoConfig: {
              plugins: [
                {
                  name: 'preset-default',
                  params: {
                    overrides: {
                      removeViewBox: false,
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    });

    // 性能优化：生产环境优化
    if (isProduction) {
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
      config.optimization.moduleIds = 'deterministic';
      config.optimization.chunkIds = 'deterministic';

      // 启用更激进的 Tree Shaking
      config.optimization.innerGraph = true;
    }

    // 性能优化：开发环境优化
    if (isDevelopment) {
      config.optimization.removeAvailableModules = false;
      config.optimization.removeEmptyChunks = false;
      config.optimization.splitChunks = false;
    }

    return config;
  },

  // Environment variables
  env: {
    BUILD_TIME: new Date().toISOString(),
    BUILD_ID: process.env.VERCEL_GIT_COMMIT_SHA || 'local',
  },

  // Logging
  logging: {
    fetches: {
      fullUrl: isDevelopment,
    },
  },

  // Development configuration
  ...(isDevelopment && {
    // Fast refresh
    reactStrictMode: true,

    // Development optimizations
    onDemandEntries: {
      maxInactiveAge: 25 * 1000,
      pagesBufferLength: 2,
    },
  }),

  // Production configuration
  ...(isProduction && {
    // Strict mode for production
    reactStrictMode: true,

    // Generate build ID
    generateBuildId: async () => {
      return process.env.VERCEL_GIT_COMMIT_SHA || `build-${Date.now()}`;
    },
  }),
};

export default withBundleAnalyzer(withNextIntl(nextConfig));
