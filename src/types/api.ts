/**
 * API Type Definitions
 *
 * Centralized type definitions for API requests, responses,
 * and data structures used throughout the application.
 */

import { z } from 'zod';

/**
 * Standard API Response Format
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  meta?: {
    timestamp: string;
    requestId?: string;
    version: string;
  };
}

/**
 * API Error Codes
 */
export const API_ERROR_CODES = {
  // Client Errors (4xx)
  BAD_REQUEST: 'BAD_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  METHOD_NOT_ALLOWED: 'METHOD_NOT_ALLOWED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',

  // Server Errors (5xx)
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',

  // Custom Application Errors
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
  FEATURE_DISABLED: 'FEATURE_DISABLED',
} as const;

export type ApiErrorCode =
  (typeof API_ERROR_CODES)[keyof typeof API_ERROR_CODES];

/**
 * Pagination Parameters
 */
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).default('asc'),
});

export type PaginationParams = z.infer<typeof paginationSchema>;

/**
 * Paginated Response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Search Parameters
 */
export const searchSchema = z.object({
  q: z.string().min(1).max(100),
  fields: z.array(z.string()).optional(),
  filters: z.record(z.string()).optional(),
});

export type SearchParams = z.infer<typeof searchSchema>;

/**
 * Common Request Headers
 */
export interface ApiRequestHeaders {
  'Content-Type'?: string;
  Accept?: string;
  Authorization?: string;
  'X-Request-ID'?: string;
  'X-Client-Version'?: string;
  'Accept-Language'?: string;
}

/**
 * Health Check Types
 */
export interface HealthCheckStatus {
  status: 'healthy' | 'warning' | 'error';
  message: string;
  timestamp: string;
  details?: Record<string, unknown>;
}

export interface SystemHealth {
  overall: HealthCheckStatus;
  components: {
    database: HealthCheckStatus;
    cache: HealthCheckStatus;
    external_services: HealthCheckStatus;
    configuration: HealthCheckStatus;
  };
  metrics: {
    uptime: number;
    memory_usage: number;
    cpu_usage?: number;
    response_time: number;
  };
}

/**
 * File Upload Types
 */
export const fileUploadSchema = z.object({
  file: z.instanceof(File),
  type: z.enum(['image', 'document', 'video', 'audio']),
  maxSize: z.number().default(10 * 1024 * 1024), // 10MB default
  allowedTypes: z.array(z.string()).optional(),
});

export type FileUploadParams = z.infer<typeof fileUploadSchema>;

export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: string;
}

/**
 * Validation Error Details
 */
export interface ValidationErrorDetail {
  field: string;
  code: string;
  message: string;
  value?: unknown;
}

export interface ValidationError {
  code: 'VALIDATION_ERROR';
  message: string;
  details: ValidationErrorDetail[];
}

/**
 * Rate Limiting Types
 */
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

/**
 * API Configuration Types
 */
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  defaultHeaders: ApiRequestHeaders;
}

/**
 * Webhook Types
 */
export interface WebhookPayload<T = unknown> {
  id: string;
  event: string;
  timestamp: string;
  data: T;
  signature?: string;
}

/**
 * Batch Operation Types
 */
export interface BatchRequest<T> {
  operations: Array<{
    id: string;
    method: 'CREATE' | 'UPDATE' | 'DELETE';
    data: T;
  }>;
}

export interface BatchResponse<T> {
  results: Array<{
    id: string;
    success: boolean;
    data?: T;
    error?: {
      code: string;
      message: string;
    };
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

/**
 * API Route Handler Types
 */
export type ApiHandler<T = unknown> = (
  request: Request,
  context?: { params?: Record<string, string> }
) => Promise<Response | ApiResponse<T>>;

/**
 * Middleware Types
 */
export interface ApiMiddleware {
  name: string;
  handler: (
    request: Request,
    next: () => Promise<Response>
  ) => Promise<Response>;
}

/**
 * Cache Types
 */
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[];
  revalidate?: boolean;
}

export interface CachedResponse<T> {
  data: T;
  cached: boolean;
  timestamp: string;
  ttl: number;
}
