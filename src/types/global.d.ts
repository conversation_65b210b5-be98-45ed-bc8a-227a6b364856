/**
 * Global Type Augmentations for Tucsenberg Web Stable
 *
 * This file provides type safety and autocompletion for next-intl
 * by augmenting the AppConfig interface with our specific types.
 */

import { formats } from '@/i18n/request';
import { routing } from '@/i18n/routing';
import type messages from '../../messages/en.json';

// Type-only imports to avoid runtime dependency
type Messages = typeof messages;
type Formats = typeof formats;

declare module 'next-intl' {
  interface AppConfig {
    Locale: (typeof routing.locales)[number];
    Messages: Messages;
    Formats: Formats;
  }
}

export {};
