/**
 * Common Type Definitions
 *
 * Shared type definitions used across the application,
 * including utility types, enums, and common data structures.
 */

import { z } from 'zod';

/**
 * Environment Types
 */
export type Environment = 'development' | 'test' | 'production';

/**
 * Locale Types
 */
export type Locale = 'en' | 'zh';

export const localeSchema = z.enum(['en', 'zh']);

/**
 * Status Types
 */
export type Status = 'active' | 'inactive' | 'pending' | 'archived';

export const statusSchema = z.enum([
  'active',
  'inactive',
  'pending',
  'archived',
]);

/**
 * Priority Types
 */
export type Priority = 'low' | 'medium' | 'high' | 'critical';

export const prioritySchema = z.enum(['low', 'medium', 'high', 'critical']);

/**
 * Common Entity Fields
 */
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  version?: number;
}

export const baseEntitySchema = z.object({
  id: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  version: z.number().int().min(1).optional(),
});

/**
 * Audit Fields
 */
export interface AuditFields {
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: string;
  deletedBy?: string;
}

export const auditFieldsSchema = z.object({
  createdBy: z.string().uuid().optional(),
  updatedBy: z.string().uuid().optional(),
  deletedAt: z.string().datetime().optional(),
  deletedBy: z.string().uuid().optional(),
});

/**
 * Metadata Types
 */
export interface Metadata {
  [key: string]: string | number | boolean | null;
}

export const metadataSchema = z.record(
  z.union([z.string(), z.number(), z.boolean(), z.null()])
);

/**
 * Contact Information
 */
export interface ContactInfo {
  email?: string;
  phone?: string;
  website?: string;
  address?: Address;
}

export const contactInfoSchema = z.object({
  email: z.string().email().optional(),
  phone: z.string().min(1).optional(),
  website: z.string().url().optional(),
  address: z
    .object({
      street: z.string(),
      city: z.string(),
      state: z.string(),
      country: z.string(),
      postalCode: z.string(),
    })
    .optional(),
});

/**
 * Address Types
 */
export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export const addressSchema = z.object({
  street: z.string().min(1),
  city: z.string().min(1),
  state: z.string().min(1),
  country: z.string().min(1),
  postalCode: z.string().min(1),
  coordinates: z
    .object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180),
    })
    .optional(),
});

/**
 * File Types
 */
export interface FileInfo {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  metadata?: Metadata;
}

export const fileInfoSchema = z.object({
  id: z.string().uuid(),
  filename: z.string().min(1),
  originalName: z.string().min(1),
  mimeType: z.string().min(1),
  size: z.number().int().min(0),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  metadata: metadataSchema.optional(),
});

/**
 * User Types
 */
export interface User extends BaseEntity, AuditFields {
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  status: Status;
  preferences: UserPreferences;
  lastLoginAt?: string;
}

export type UserRole = 'admin' | 'editor' | 'viewer' | 'guest';

export const userRoleSchema = z.enum(['admin', 'editor', 'viewer', 'guest']);

export interface UserPreferences {
  locale: Locale;
  theme: 'light' | 'dark' | 'system';
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export const userPreferencesSchema = z.object({
  locale: localeSchema,
  theme: z.enum(['light', 'dark', 'system']),
  timezone: z.string(),
  notifications: z.object({
    email: z.boolean(),
    push: z.boolean(),
    sms: z.boolean(),
  }),
});

export const userSchema = baseEntitySchema.merge(auditFieldsSchema).extend({
  email: z.string().email(),
  name: z.string().min(1),
  avatar: z.string().url().optional(),
  role: userRoleSchema,
  status: statusSchema,
  preferences: userPreferencesSchema,
  lastLoginAt: z.string().datetime().optional(),
});

/**
 * Content Types
 */
export interface Content extends BaseEntity, AuditFields {
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  status: ContentStatus;
  publishedAt?: string;
  tags: string[];
  metadata?: Metadata;
}

export type ContentStatus = 'draft' | 'published' | 'archived';

export const contentStatusSchema = z.enum(['draft', 'published', 'archived']);

export const contentSchema = baseEntitySchema.merge(auditFieldsSchema).extend({
  title: z.string().min(1),
  slug: z.string().min(1),
  excerpt: z.string().optional(),
  content: z.string(),
  status: contentStatusSchema,
  publishedAt: z.string().datetime().optional(),
  tags: z.array(z.string()),
  metadata: metadataSchema.optional(),
});

/**
 * Utility Types
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type NonEmptyArray<T> = [T, ...T[]];

/**
 * Form Types
 */
export interface FormField {
  name: string;
  label: string;
  type:
    | 'text'
    | 'email'
    | 'password'
    | 'number'
    | 'textarea'
    | 'select'
    | 'checkbox'
    | 'radio'
    | 'file';
  required: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  validation?: z.ZodSchema;
}

export interface FormData {
  [key: string]: unknown;
}

export interface FormErrors {
  [key: string]: string[];
}

/**
 * Theme Types
 */
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    foreground: string;
    muted: string;
    accent: string;
    destructive: string;
  };
  fonts: {
    sans: string[];
    serif: string[];
    mono: string[];
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
}

/**
 * Configuration Types
 */
export interface AppConfig {
  name: string;
  version: string;
  environment: Environment;
  features: Record<string, boolean>;
  limits: {
    fileUpload: {
      maxSize: number;
      allowedTypes: string[];
    };
    api: {
      rateLimit: number;
      timeout: number;
    };
  };
}

/**
 * Error Types
 */
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
  requestId?: string;
}

/**
 * Log Types
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, unknown>;
  error?: Error;
}
