/**
 * Page Layout Component
 *
 * Provides consistent page structure with <PERSON><PERSON> and <PERSON><PERSON>.
 * Supports conditional rendering and flexible content areas.
 *
 * Features:
 * - Header/Footer conditional display
 * - Full-width content mode
 * - Proper semantic HTML structure
 * - Accessibility support
 * - Performance optimizations
 */

'use client';

import { memo } from 'react';
import Header from './header';
import Footer from './footer';
import { cn } from '@/lib/utils';

interface PageLayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  fullWidth?: boolean;
  headerProps?: {
    showBadge?: boolean;
    badgeCount?: number;
  };
}

const PageLayout = memo(function PageLayout({
  children,
  className = '',
  showHeader = true,
  showFooter = true,
  fullWidth = false,
  headerProps = {},
}: PageLayoutProps) {
  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {/* Header */}
      {showHeader && <Header {...headerProps} />}

      {/* Main Content */}
      <main
        className={cn('flex-1', !fullWidth && 'container mx-auto px-4')}
        role='main'
      >
        {children}
      </main>

      {/* Footer */}
      {showFooter && <Footer />}
    </div>
  );
});

PageLayout.displayName = 'PageLayout';

export default PageLayout;
