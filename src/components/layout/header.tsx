/**
 * Enhanced Header Layout Component
 *
 * Enterprise-grade navigation header with full feature set:
 * - shadcn/ui components integration
 * - Theme switching (light/dark/system)
 * - Language switching with next-intl
 * - Badge notifications
 * - Mobile-responsive navigation
 * - Full accessibility support
 * - Performance optimizations
 *
 * Built for Next.js 14.2.18 App Router with strict TypeScript.
 */

'use client';

import { Link, usePathname } from '@/i18n/navigation';
import { BookOpen, Home, Mail, Menu, Package, Users } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { memo, useCallback, useState } from 'react';

import { ThemeToggle } from '@/components/theme';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LanguageToggle } from '@/components/ui/language-toggle';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';

interface HeaderProps {
  className?: string;
  showBadge?: boolean;
  badgeCount?: number;
}

const Header = memo(function Header({
  className = '',
  showBadge = false,
  badgeCount = 0,
}: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const t = useTranslations('Header');
  const pathname = usePathname();

  // Enhanced navigation items with icons
  const navigationItems = [
    { href: '/', label: t('nav.home'), icon: Home },
    { href: '/products', label: t('nav.products'), icon: Package },
    { href: '/blog', label: t('nav.blog'), icon: BookOpen },
    { href: '/about', label: t('nav.about'), icon: Users },
    { href: '/contact', label: t('nav.contact'), icon: Mail },
    { href: '/components', label: t('nav.components'), icon: Package },
  ];

  // Optimized event handlers with useCallback
  const closeMenu = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  const isActivePath = useCallback(
    (href: string) => {
      if (href === '/') {
        return pathname === '/';
      }
      return pathname.startsWith(href);
    },
    [pathname]
  );

  return (
    <header
      className={cn(
        'sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
        className
      )}
    >
      <div className='container mx-auto px-4'>
        <div className='flex h-16 items-center justify-between'>
          {/* Enhanced Logo with Badge */}
          <div className='flex items-center space-x-2'>
            <Link
              href='/'
              className='flex items-center space-x-3 transition-opacity hover:opacity-80'
              aria-label={t('brand')}
            >
              <div className='relative'>
                <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-primary'>
                  <span className='text-lg font-bold text-primary-foreground'>
                    T
                  </span>
                </div>
                {showBadge && badgeCount > 0 && (
                  <Badge
                    variant='destructive'
                    className='absolute -right-2 -top-2 h-5 w-5 rounded-full p-0 text-xs'
                  >
                    {badgeCount > 99 ? '99+' : badgeCount}
                  </Badge>
                )}
              </div>
              <span className='text-xl font-bold'>{t('brand')}</span>
            </Link>
          </div>

          {/* Enhanced Desktop Navigation */}
          <nav
            className='hidden md:flex items-center space-x-1'
            role='navigation'
          >
            {navigationItems.map(item => {
              const Icon = item.icon;
              const isActive = isActivePath(item.href);

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-2 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                    'hover:bg-accent hover:text-accent-foreground',
                    'focus:bg-accent focus:text-accent-foreground focus:outline-none',
                    isActive && 'bg-accent text-accent-foreground'
                  )}
                  aria-current={isActive ? 'page' : undefined}
                >
                  <Icon className='h-4 w-4' aria-hidden='true' />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>

          {/* Enhanced Desktop Actions */}
          <div className='hidden md:flex items-center space-x-2'>
            <LanguageToggle />
            <ThemeToggle />
          </div>

          {/* Enhanced Mobile Menu */}
          <div className='md:hidden'>
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button
                  variant='ghost'
                  size='sm'
                  className='h-9 w-9 px-0'
                  aria-label={t('actions.toggleMenu')}
                >
                  <Menu className='h-5 w-5' />
                </Button>
              </SheetTrigger>
              <SheetContent side='right' className='w-80'>
                <div className='flex flex-col space-y-4 mt-6'>
                  {/* Mobile Navigation */}
                  <nav className='flex flex-col space-y-2' role='navigation'>
                    {navigationItems.map(item => {
                      const Icon = item.icon;
                      const isActive = isActivePath(item.href);

                      return (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={closeMenu}
                          className={cn(
                            'flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                            'hover:bg-accent hover:text-accent-foreground',
                            'focus:bg-accent focus:text-accent-foreground focus:outline-none',
                            isActive && 'bg-accent text-accent-foreground'
                          )}
                          aria-current={isActive ? 'page' : undefined}
                        >
                          <Icon className='h-4 w-4' aria-hidden='true' />
                          <span>{item.label}</span>
                        </Link>
                      );
                    })}
                  </nav>

                  {/* Mobile Actions */}
                  <div className='flex flex-col space-y-4 border-t pt-4'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>
                        {t('actions.language')}
                      </span>
                      <LanguageToggle />
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>
                        {t('actions.theme')}
                      </span>
                      <ThemeToggle />
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
});

Header.displayName = 'Header';

export default Header;
