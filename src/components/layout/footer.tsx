/**
 * Footer Layout Component
 *
 * Site footer with links and information, supporting internationalization.
 * Built for Next.js 14.2.18 App Router with next-intl integration.
 */

'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/navigation';

interface FooterProps {
  className?: string;
}

export default function Footer({ className = '' }: FooterProps) {
  const t = useTranslations('Footer');

  const currentYear = new Date().getFullYear();

  // Footer navigation sections
  const footerSections = [
    {
      title: t('sections.company.title'),
      links: [
        { href: '/about', label: t('sections.company.about') },
        { href: '/blog', label: t('sections.company.blog') },
        { href: '/contact', label: t('sections.company.contact') },
      ],
    },
    {
      title: t('sections.products.title'),
      links: [
        { href: '/products', label: t('sections.products.all') },
        { href: '/products', label: t('sections.products.featured') },
        { href: '/products', label: t('sections.products.new') },
      ],
    },
    {
      title: t('sections.support.title'),
      links: [
        { href: '/contact', label: t('sections.support.help') },
        { href: '/contact', label: t('sections.support.documentation') },
        { href: '/contact', label: t('sections.support.faq') },
      ],
    },
    {
      title: t('sections.legal.title'),
      links: [
        { href: '/privacy', label: t('sections.legal.privacy') },
        { href: '/terms', label: t('sections.legal.terms') },
        { href: '/cookies', label: t('sections.legal.cookies') },
      ],
    },
  ];

  // Social media links
  const socialLinks = [
    {
      name: 'Twitter',
      href: '#',
      icon: (
        <svg className='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
          <path d='M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84' />
        </svg>
      ),
    },
    {
      name: 'GitHub',
      href: '#',
      icon: (
        <svg className='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
          <path
            fillRule='evenodd'
            d='M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z'
            clipRule='evenodd'
          />
        </svg>
      ),
    },
    {
      name: 'LinkedIn',
      href: '#',
      icon: (
        <svg className='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
          <path
            fillRule='evenodd'
            d='M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z'
            clipRule='evenodd'
          />
        </svg>
      ),
    },
  ];

  return (
    <footer
      className={`bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 ${className}`}
    >
      <div className='container mx-auto px-4 py-12'>
        {/* Main Footer Content */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8'>
          {/* Brand Section */}
          <div className='lg:col-span-1'>
            <div className='flex items-center mb-4'>
              <div className='w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3'>
                <span className='text-white font-bold text-lg'>T</span>
              </div>
              <span className='text-xl font-bold text-gray-900 dark:text-white'>
                {t('brand')}
              </span>
            </div>
            <p className='text-gray-600 dark:text-gray-400 text-sm mb-4'>
              {t('description')}
            </p>

            {/* Social Links */}
            <div className='flex space-x-4'>
              {socialLinks.map(social => (
                <a
                  key={social.name}
                  href={social.href}
                  className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors'
                  aria-label={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Navigation Sections */}
          {footerSections.map((section, index) => (
            <div key={index} className='lg:col-span-1'>
              <h3 className='text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4'>
                {section.title}
              </h3>
              <ul className='space-y-3'>
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className='text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white text-sm transition-colors'
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className='mt-12 pt-8 border-t border-gray-200 dark:border-gray-700'>
          <div className='max-w-md'>
            <h3 className='text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4'>
              {t('newsletter.title')}
            </h3>
            <p className='text-gray-600 dark:text-gray-400 text-sm mb-4'>
              {t('newsletter.description')}
            </p>
            <div className='flex flex-col sm:flex-row gap-3'>
              <input
                type='email'
                placeholder={t('newsletter.placeholder')}
                className='flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white'
                disabled
              />
              <button
                className='px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
                disabled
              >
                {t('newsletter.subscribe')}
              </button>
            </div>
            <p className='text-xs text-gray-500 dark:text-gray-400 mt-2'>
              {t('newsletter.comingSoon')}
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className='mt-12 pt-8 border-t border-gray-200 dark:border-gray-700'>
          <div className='flex flex-col md:flex-row justify-between items-center'>
            <div className='text-gray-600 dark:text-gray-400 text-sm'>
              {t('copyright', { year: currentYear })}
            </div>

            {/* Additional Links */}
            <div className='flex space-x-6 mt-4 md:mt-0'>
              <Link
                href='/privacy'
                className='text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white text-sm transition-colors'
              >
                {t('links.privacy')}
              </Link>
              <Link
                href='/terms'
                className='text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white text-sm transition-colors'
              >
                {t('links.terms')}
              </Link>
              <Link
                href='/sitemap'
                className='text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white text-sm transition-colors'
              >
                {t('links.sitemap')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
