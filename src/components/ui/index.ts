/**
 * UI Components Index
 *
 * Central export point for all shadcn/ui components
 * Enables tree-shaking and clean imports
 */

// Core UI Components
export { Button, buttonVariants } from './button';
export type { ButtonProps } from './button';

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from './card';

export { Badge, badgeVariants } from './badge';
export type { BadgeProps } from './badge';

export { Input } from './input';

export { Label } from './label';

// Additional UI Components
export { DropdownMenu } from './dropdown-menu';
export { NavigationMenu } from './navigation-menu';
export { Sheet } from './sheet';
export { OptimizedImage } from './optimized-image';
export { LanguageToggle } from './language-toggle';
