'use client';

import { usePathname, useRouter } from '@/i18n/navigation';
import { Languages } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

/**
 * Language Toggle Component
 *
 * Provides a dropdown menu for switching between supported locales.
 * Uses next-intl navigation for proper locale routing.
 *
 * Features:
 * - English/Chinese language switching
 * - Proper next-intl integration
 * - Accessible dropdown menu
 * - Current language indication
 * - Keyboard navigation support
 */

interface Locale {
  code: string;
  label: string;
  nativeLabel: string;
  flag: string;
}

const locales: Locale[] = [
  { code: 'en', label: 'English', nativeLabel: 'English', flag: '🇺🇸' },
  { code: 'zh', label: 'Chinese', nativeLabel: '中文', flag: '🇨🇳' },
];

export function LanguageToggle() {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const t = useTranslations('Header');

  const currentLocale = params.locale as string;
  const currentLocaleData = locales.find(
    locale => locale.code === currentLocale
  );

  const handleLocaleChange = (newLocale: string) => {
    // Use next-intl navigation to switch locale
    router.replace(pathname, { locale: newLocale as 'en' | 'zh' });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className='gap-2'
          aria-label={t('actions.language')}
        >
          <Languages className='h-4 w-4' />
          <span className='hidden sm:inline'>
            {currentLocaleData?.flag} {currentLocaleData?.nativeLabel}
          </span>
          <span className='sm:hidden'>{currentLocaleData?.flag}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        {locales.map(locale => (
          <DropdownMenuItem
            key={locale.code}
            onClick={() => handleLocaleChange(locale.code)}
            className={`cursor-pointer ${
              currentLocale === locale.code ? 'bg-accent' : ''
            }`}
          >
            <span className='mr-2'>{locale.flag}</span>
            <span>{locale.nativeLabel}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
