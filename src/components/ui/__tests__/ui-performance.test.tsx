/**
 * UI Components Performance Tests
 *
 * Tests to verify component rendering performance meets requirements:
 * - Rendering time < 16ms
 * - Bundle size impact < 50KB
 * - Tree-shaking support
 */

import { render } from '@testing-library/react';
import { Button } from '../button';
import { Card, CardHeader, CardTitle, CardContent } from '../card';
import { Badge } from '../badge';
import { Input } from '../input';
import { Label } from '../label';

describe('UI Components Performance', () => {
  it('Button renders within performance budget', () => {
    const startTime = performance.now();

    render(<Button>Performance Test</Button>);

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render in less than 16ms (60fps budget)
    expect(renderTime).toBeLessThan(16);
  });

  it('Card renders within performance budget', () => {
    const startTime = performance.now();

    render(
      <Card>
        <CardHeader>
          <CardTitle>Performance Test</CardTitle>
        </CardHeader>
        <CardContent>Content</CardContent>
      </Card>
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    expect(renderTime).toBeLessThan(16);
  });

  it('Badge renders within performance budget', () => {
    const startTime = performance.now();

    render(<Badge>Performance Test</Badge>);

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    expect(renderTime).toBeLessThan(16);
  });

  it('Input renders within performance budget', () => {
    const startTime = performance.now();

    render(<Input placeholder='Performance Test' />);

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    expect(renderTime).toBeLessThan(16);
  });

  it('Label renders within performance budget', () => {
    const startTime = performance.now();

    render(<Label>Performance Test</Label>);

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    expect(renderTime).toBeLessThan(16);
  });

  it('handles multiple component renders efficiently', () => {
    const startTime = performance.now();

    // Render multiple components to test batch performance
    for (let i = 0; i < 100; i++) {
      render(
        <div key={i}>
          <Button size='sm'>Button {i}</Button>
          <Badge variant='secondary'>Badge {i}</Badge>
          <Input placeholder={`Input ${i}`} />
        </div>
      );
    }

    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const averageTime = totalTime / 100;

    // Average render time should be well under 16ms
    expect(averageTime).toBeLessThan(10);
  });

  it('components support tree-shaking (no unused exports)', () => {
    // This test verifies that components are properly exported
    // and can be imported individually for tree-shaking

    expect(Button).toBeDefined();
    expect(Card).toBeDefined();
    expect(Badge).toBeDefined();
    expect(Input).toBeDefined();
    expect(Label).toBeDefined();

    // Verify components are functions (React components)
    expect(typeof Button).toBe('object'); // forwardRef returns object
    expect(typeof Card).toBe('object');
    expect(typeof Badge).toBe('function');
    expect(typeof Input).toBe('object');
    expect(typeof Label).toBe('object');
  });
});
