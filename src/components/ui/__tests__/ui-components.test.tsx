/**
 * UI Components Test Suite
 *
 * Comprehensive tests for core UI components to verify:
 * - Functionality and variants
 * - Accessibility compliance
 * - Type safety
 * - Theme support
 */

import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Badge } from '../badge';
import { Button } from '../button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../card';
import { Input } from '../input';
import { Label } from '../label';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

describe('Button Component', () => {
  it('renders all variants correctly', () => {
    const variants = [
      'default',
      'destructive',
      'outline',
      'secondary',
      'ghost',
      'link',
    ] as const;

    variants.forEach(variant => {
      render(
        <Button variant={variant} data-testid={`button-${variant}`}>
          Test Button
        </Button>
      );
      expect(screen.getByTestId(`button-${variant}`)).toBeInTheDocument();
    });
  });

  it('renders all sizes correctly', () => {
    const sizes = ['sm', 'default', 'lg', 'icon'] as const;

    sizes.forEach(size => {
      render(
        <Button size={size} data-testid={`button-${size}`}>
          Test
        </Button>
      );
      expect(screen.getByTestId(`button-${size}`)).toBeInTheDocument();
    });
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();

    render(<Button onClick={handleClick}>Clickable Button</Button>);

    const button = screen.getByRole('button');
    await user.tab();
    expect(button).toHaveFocus();

    await user.keyboard('{Enter}');
    expect(handleClick).toHaveBeenCalledTimes(1);

    await user.keyboard(' ');
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Button>Accessible Button</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});

describe('Card Component', () => {
  it('renders complete card structure', () => {
    render(
      <Card data-testid='card'>
        <CardHeader>
          <CardTitle>Card Title</CardTitle>
          <CardDescription>Card Description</CardDescription>
        </CardHeader>
        <CardContent>Card Content</CardContent>
        <CardFooter>Card Footer</CardFooter>
      </Card>
    );

    expect(screen.getByTestId('card')).toBeInTheDocument();
    expect(screen.getByText('Card Title')).toBeInTheDocument();
    expect(screen.getByText('Card Description')).toBeInTheDocument();
    expect(screen.getByText('Card Content')).toBeInTheDocument();
    expect(screen.getByText('Card Footer')).toBeInTheDocument();
  });

  it('has no accessibility violations', async () => {
    const { container } = render(
      <Card>
        <CardHeader>
          <CardTitle>Accessible Card</CardTitle>
        </CardHeader>
        <CardContent>Content</CardContent>
      </Card>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});

describe('Badge Component', () => {
  it('renders all variants correctly', () => {
    const variants = [
      'default',
      'secondary',
      'destructive',
      'outline',
    ] as const;

    variants.forEach(variant => {
      render(
        <Badge variant={variant} data-testid={`badge-${variant}`}>
          Badge
        </Badge>
      );
      expect(screen.getByTestId(`badge-${variant}`)).toBeInTheDocument();
    });
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Badge>Accessible Badge</Badge>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});

describe('Input Component', () => {
  it('renders with different types', () => {
    const types = ['text', 'email', 'password', 'number'] as const;

    types.forEach(type => {
      render(<Input type={type} data-testid={`input-${type}`} />);
      expect(screen.getByTestId(`input-${type}`)).toBeInTheDocument();
    });
  });

  it('supports keyboard interaction', async () => {
    const user = userEvent.setup();
    const handleChange = jest.fn();

    render(<Input onChange={handleChange} placeholder='Test input' />);

    const input = screen.getByPlaceholderText('Test input');
    await user.type(input, 'Hello World');

    expect(input).toHaveValue('Hello World');
    expect(handleChange).toHaveBeenCalled();
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Input aria-label='Accessible input' />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});

describe('Label Component', () => {
  it('associates with input correctly', () => {
    render(
      <div>
        <Label htmlFor='test-input'>Test Label</Label>
        <Input id='test-input' />
      </div>
    );

    const label = screen.getByText('Test Label');
    const input = screen.getByRole('textbox');

    expect(label).toBeInTheDocument();
    expect(input).toHaveAccessibleName('Test Label');
  });

  it('has no accessibility violations', async () => {
    const { container } = render(
      <div>
        <Label htmlFor='accessible-input'>Accessible Label</Label>
        <Input id='accessible-input' />
      </div>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});

describe('Theme Support', () => {
  it('uses CSS custom properties for theming', () => {
    render(<Button>Themed Button</Button>);

    const button = screen.getByRole('button');

    // Check if component uses theme variables
    expect(button.className).toContain('bg-primary');
    expect(button.className).toContain('text-primary-foreground');
  });
});
