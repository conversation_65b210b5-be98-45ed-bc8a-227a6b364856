'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useState } from 'react';
import {
  FadeIn,
  Floating,
  HoverScale,
  Pulse,
  RotateIn,
  SlideIn,
  StaggeredFadeIn,
  useAnimationConfig,
  useReducedMotion,
} from './index';

// Import additional components for advanced demo

/**
 * Animation Demo Component
 *
 * Demonstrates various animation components and their capabilities.
 * Useful for testing and showcasing animation functionality.
 */
export function AnimationDemo() {
  const [showAnimations, setShowAnimations] = useState(true);
  const prefersReducedMotion = useReducedMotion();
  const animationConfig = useAnimationConfig();

  const demoItems = ['First item', 'Second item', 'Third item', 'Fourth item'];

  return (
    <div className='space-y-8 p-6'>
      <div className='space-y-4'>
        <h2 className='text-2xl font-bold'>Animation Demo</h2>

        {/* Animation Status */}
        <div className='rounded-lg border p-4 space-y-2'>
          <h3 className='font-semibold'>Animation Status</h3>
          <p className='text-sm text-muted-foreground'>
            Prefers Reduced Motion: {prefersReducedMotion ? 'Yes' : 'No'}
          </p>
          <p className='text-sm text-muted-foreground'>
            Should Animate: {animationConfig.shouldAnimate ? 'Yes' : 'No'}
          </p>
          <p className='text-sm text-muted-foreground'>
            Duration: {animationConfig.duration}s
          </p>
        </div>

        {/* Controls */}
        <div className='flex gap-2'>
          <Button
            onClick={() => setShowAnimations(!showAnimations)}
            variant='outline'
          >
            {showAnimations ? 'Hide' : 'Show'} Animations
          </Button>
        </div>
      </div>

      {showAnimations && (
        <div className='space-y-8'>
          {/* Fade In Demo */}
          <section className='space-y-4'>
            <h3 className='text-lg font-semibold'>Fade In Animations</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FadeIn direction='up' delay={0.1}>
                <div className='rounded-lg border p-4 bg-card'>
                  <h4 className='font-medium'>Fade In Up</h4>
                  <p className='text-sm text-muted-foreground'>
                    Slides up while fading in
                  </p>
                </div>
              </FadeIn>

              <FadeIn direction='left' delay={0.2}>
                <div className='rounded-lg border p-4 bg-card'>
                  <h4 className='font-medium'>Fade In Left</h4>
                  <p className='text-sm text-muted-foreground'>
                    Slides from left while fading in
                  </p>
                </div>
              </FadeIn>
            </div>
          </section>

          {/* Staggered Animation Demo */}
          <section className='space-y-4'>
            <h3 className='text-lg font-semibold'>Staggered Animations</h3>
            <StaggeredFadeIn staggerDelay={0.1} direction='up'>
              {demoItems.map((item, index) => (
                <div key={index} className='rounded-lg border p-4 bg-card mb-2'>
                  <p className='font-medium'>{item}</p>
                  <p className='text-sm text-muted-foreground'>
                    Staggered animation item {index + 1}
                  </p>
                </div>
              ))}
            </StaggeredFadeIn>
          </section>

          {/* Micro Interactions Demo */}
          <section className='space-y-4'>
            <h3 className='text-lg font-semibold'>Micro Interactions</h3>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <HoverScale>
                <div className='rounded-lg border p-4 bg-card text-center cursor-pointer'>
                  <h4 className='font-medium'>Hover Scale</h4>
                  <p className='text-xs text-muted-foreground'>Hover me!</p>
                </div>
              </HoverScale>

              <Floating intensity={5}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Floating</h4>
                  <p className='text-xs text-muted-foreground'>I float!</p>
                </div>
              </Floating>

              <Pulse scale={1.05} duration={2}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Pulse</h4>
                  <p className='text-xs text-muted-foreground'>I pulse!</p>
                </div>
              </Pulse>

              <RotateIn degrees={90} delay={0.5}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Rotate In</h4>
                  <p className='text-xs text-muted-foreground'>I rotated!</p>
                </div>
              </RotateIn>
            </div>
          </section>

          {/* Slide Animations Demo */}
          <section className='space-y-4'>
            <h3 className='text-lg font-semibold'>Slide Animations</h3>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <SlideIn direction='left' delay={0.1}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Slide Left</h4>
                </div>
              </SlideIn>

              <SlideIn direction='right' delay={0.2}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Slide Right</h4>
                </div>
              </SlideIn>

              <SlideIn direction='up' delay={0.3}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Slide Up</h4>
                </div>
              </SlideIn>

              <SlideIn direction='down' delay={0.4}>
                <div className='rounded-lg border p-4 bg-card text-center'>
                  <h4 className='font-medium'>Slide Down</h4>
                </div>
              </SlideIn>
            </div>
          </section>
        </div>
      )}
    </div>
  );
}
