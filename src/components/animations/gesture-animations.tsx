'use client';

import { motion, PanInfo, useDragControls } from 'framer-motion';
import { ReactNode, useState } from 'react';

interface SwipeableCardProps {
  children: ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  threshold?: number;
  className?: string;
}

/**
 * Swipeable Card Component
 *
 * Card that can be swiped left or right with gesture animations.
 * Perfect for card stacks, image galleries, or dismissible content.
 */
export function SwipeableCard({
  children,
  onSwipeLeft,
  onSwipeRight,
  threshold = 100,
  className = '',
}: SwipeableCardProps) {
  const [exitX, setExitX] = useState(0);

  const handleDragEnd = (_event: any, info: PanInfo) => {
    const offset = info.offset.x;
    const velocity = info.velocity.x;

    if (Math.abs(velocity) >= 500 || Math.abs(offset) >= threshold) {
      if (offset > 0) {
        setExitX(1000);
        onSwipeRight?.();
      } else {
        setExitX(-1000);
        onSwipeLeft?.();
      }
    }
  };

  return (
    <motion.div
      className={className}
      drag='x'
      dragConstraints={{ left: 0, right: 0 }}
      onDragEnd={handleDragEnd}
      animate={{ x: exitX }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      whileDrag={{
        scale: 1.05,
        rotate: exitX > 0 ? 5 : exitX < 0 ? -5 : 0,
      }}
      style={{
        cursor: 'grab',
      }}
    >
      {children}
    </motion.div>
  );
}

interface DraggableProps {
  children: ReactNode;
  constrainToParent?: boolean;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  className?: string;
}

/**
 * Draggable Component
 *
 * Makes any element draggable with smooth animations.
 * Supports constraints and callbacks.
 */
export function Draggable({
  children,
  constrainToParent = true,
  onDragStart,
  onDragEnd,
  className = '',
}: DraggableProps) {
  const dragControls = useDragControls();

  return (
    <motion.div
      drag
      dragControls={dragControls}
      dragConstraints={
        constrainToParent ? { left: 0, right: 0, top: 0, bottom: 0 } : false
      }
      dragElastic={0.1}
      onDragStart={onDragStart || (() => {})}
      onDragEnd={onDragEnd || (() => {})}
      whileDrag={{
        scale: 1.1,
        zIndex: 1000,
        cursor: 'grabbing',
      }}
      className={className}
      style={{
        cursor: 'grab',
      }}
    >
      {children}
    </motion.div>
  );
}

interface PinchZoomProps {
  children: ReactNode;
  minScale?: number;
  maxScale?: number;
  className?: string;
}

/**
 * Pinch Zoom Component
 *
 * Enables pinch-to-zoom functionality on touch devices.
 * Great for images, maps, or detailed content.
 */
export function PinchZoom({
  children,
  minScale = 0.5,
  maxScale = 3,
  className = '',
}: PinchZoomProps) {
  const [scale, setScale] = useState(1);

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newScale = Math.min(Math.max(scale + delta, minScale), maxScale);
    setScale(newScale);
  };

  return (
    <motion.div
      className={className}
      style={{
        scale,
        cursor: 'grab',
      }}
      onWheel={handleWheel}
      whileTap={{ cursor: 'grabbing' }}
    >
      {children}
    </motion.div>
  );
}

interface PullToRefreshProps {
  children: ReactNode;
  onRefresh: () => Promise<void>;
  threshold?: number;
  className?: string;
}

/**
 * Pull to Refresh Component
 *
 * Implements pull-to-refresh gesture for mobile interfaces.
 * Shows loading indicator and triggers refresh callback.
 */
export function PullToRefresh({
  children,
  onRefresh,
  threshold = 80,
  className = '',
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);

  const handleDrag = (_event: any, info: PanInfo) => {
    if (info.offset.y > 0) {
      setPullDistance(Math.min(info.offset.y, threshold * 1.5));
    }
  };

  const handleDragEnd = async (_event: any, info: PanInfo) => {
    if (info.offset.y >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      setPullDistance(0);
    }
  };

  const refreshProgress = Math.min(pullDistance / threshold, 1);

  return (
    <div className={className}>
      {/* Refresh Indicator */}
      <motion.div
        className='flex justify-center py-4'
        animate={{
          height: pullDistance > 0 ? 'auto' : 0,
          opacity: pullDistance > 0 ? 1 : 0,
        }}
      >
        <motion.div
          animate={{
            rotate: isRefreshing ? 360 : refreshProgress * 180,
          }}
          transition={{
            rotate: isRefreshing
              ? { duration: 1, repeat: Infinity, ease: 'linear' }
              : { duration: 0.2 },
          }}
          className='w-6 h-6 border-2 border-primary border-t-transparent rounded-full'
        />
      </motion.div>

      {/* Content */}
      <motion.div
        drag='y'
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={{ top: 0.3, bottom: 0 }}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        animate={{ y: 0 }}
      >
        {children}
      </motion.div>
    </div>
  );
}
