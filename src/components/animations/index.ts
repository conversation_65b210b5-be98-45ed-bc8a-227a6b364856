/**
 * Animation Components Export
 *
 * Centralized export for all animation-related components and utilities.
 * Provides a clean API for importing animation functionality.
 */

// Page transitions
export { PageTransition, ReducedMotionPageTransition } from './page-transition';

// Fade animations
export { FadeIn, StaggeredFadeIn } from './fade-in';

// Micro interactions
export {
  Floating,
  HoverScale,
  Pulse,
  RotateIn,
  SlideIn,
} from './micro-interactions';

// Advanced animations
export {
  CountUp,
  MorphingShape,
  Parallax,
  RevealOnScroll,
  Typewriter,
} from './advanced-animations';

// Gesture animations
export {
  Draggable,
  PinchZoom,
  PullToRefresh,
  SwipeableCard,
} from './gesture-animations';

// Performance monitoring
export {
  AnimationPerformanceDashboard,
  OptimizedMotion,
  useAnimationBudget,
  useAnimationPerformance,
} from './animation-performance';

// Debugging tools
export { AnimationDebugger, AnimationTimeline } from './animation-debugger';

// Scroll animations
export {
  ParallaxScroll,
  ScrollProgress,
  ScrollReveal,
  ScrollToTop,
  StaggeredScrollReveal,
} from './scroll-animations';

// Demo components
export { ScrollAnimationsDemo } from './scroll-demo';

// Configuration and utilities
export {
  ANIMATION_PRESETS,
  DURATIONS,
  EASINGS,
  FADE_VARIANTS,
  PERFORMANCE_CONFIG,
  SCALE_VARIANTS,
  SLIDE_VARIANTS,
  SPRINGS,
  STAGGER_CONFIGS,
  getAnimationConfig,
  type AnimationPreset,
  type Duration,
  type Easing,
  type Spring,
} from './animation-config';

export {
  createResponsiveVariants,
  createStaggerVariants,
  createVariants,
  patterns,
  performance,
  timing,
  useAnimationConfig,
  useReducedMotion,
} from './animation-utils';

// Re-export framer-motion for convenience
export { AnimatePresence, motion } from 'framer-motion';
export type { Transition, Variants } from 'framer-motion';
