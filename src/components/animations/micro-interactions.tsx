'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface HoverScaleProps {
  children: ReactNode;
  scale?: number;
  className?: string;
}

/**
 * Hover Scale Animation
 *
 * Provides subtle scale animation on hover.
 * Perfect for buttons, cards, and interactive elements.
 */
export function HoverScale({
  children,
  scale = 1.05,
  className = '',
}: HoverScaleProps) {
  return (
    <motion.div
      whileHover={{ scale }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      className={className}
      style={{
        willChange: 'transform',
      }}
    >
      {children}
    </motion.div>
  );
}

interface FloatingProps {
  children: ReactNode;
  intensity?: number;
  className?: string;
}

/**
 * Floating Animation
 *
 * Creates a subtle floating effect.
 * Great for hero elements, icons, or decorative content.
 */
export function Floating({
  children,
  intensity = 10,
  className = '',
}: FloatingProps) {
  return (
    <motion.div
      animate={{
        y: [-intensity, intensity, -intensity],
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
      className={className}
      style={{
        willChange: 'transform',
      }}
    >
      {children}
    </motion.div>
  );
}

interface PulseProps {
  children: ReactNode;
  scale?: number;
  duration?: number;
  className?: string;
}

/**
 * Pulse Animation
 *
 * Creates a pulsing scale effect.
 * Useful for drawing attention to important elements.
 */
export function Pulse({
  children,
  scale = 1.1,
  duration = 2,
  className = '',
}: PulseProps) {
  return (
    <motion.div
      animate={{
        scale: [1, scale, 1],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
      className={className}
      style={{
        willChange: 'transform',
      }}
    >
      {children}
    </motion.div>
  );
}

interface SlideInProps {
  children: ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
  delay?: number;
  className?: string;
}

/**
 * Slide In Animation
 *
 * Slides content in from specified direction.
 * Perfect for revealing content progressively.
 */
export function SlideIn({
  children,
  direction = 'left',
  delay = 0,
  className = '',
}: SlideInProps) {
  const directionVariants = {
    left: { x: -100, opacity: 0 },
    right: { x: 100, opacity: 0 },
    up: { y: -100, opacity: 0 },
    down: { y: 100, opacity: 0 },
  };

  return (
    <motion.div
      initial={directionVariants[direction]}
      animate={{ x: 0, y: 0, opacity: 1 }}
      transition={{
        duration: 0.6,
        delay,
        ease: 'easeOut',
      }}
      className={className}
      style={{
        willChange: 'transform, opacity',
      }}
    >
      {children}
    </motion.div>
  );
}

interface RotateInProps {
  children: ReactNode;
  degrees?: number;
  delay?: number;
  className?: string;
}

/**
 * Rotate In Animation
 *
 * Rotates and fades in content.
 * Great for icons, logos, or decorative elements.
 */
export function RotateIn({
  children,
  degrees = 180,
  delay = 0,
  className = '',
}: RotateInProps) {
  return (
    <motion.div
      initial={{ rotate: degrees, opacity: 0, scale: 0.8 }}
      animate={{ rotate: 0, opacity: 1, scale: 1 }}
      transition={{
        duration: 0.8,
        delay,
        ease: 'easeOut',
      }}
      className={className}
      style={{
        willChange: 'transform, opacity',
      }}
    >
      {children}
    </motion.div>
  );
}
