'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface PageTransitionProps {
  children: ReactNode;
  className?: string;
}

/**
 * Page Transition Animation Component
 *
 * Provides smooth page transitions with framer-motion.
 * Supports accessibility preferences for reduced motion.
 *
 * Features:
 * - Fade and slide animations
 * - Respects prefers-reduced-motion
 * - Optimized for performance
 * - Customizable transition timing
 */

const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98,
  },
  in: {
    opacity: 1,
    y: 0,
    scale: 1,
  },
  out: {
    opacity: 0,
    y: -20,
    scale: 1.02,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};

const reducedMotionTransition = {
  type: 'tween',
  ease: 'easeInOut',
  duration: 0.2,
};

export function PageTransition({
  children,
  className = '',
}: PageTransitionProps) {
  return (
    <motion.div
      initial='initial'
      animate='in'
      exit='out'
      variants={pageVariants}
      transition={pageTransition}
      className={className}
      // Respect user's motion preferences
      style={{
        willChange: 'transform, opacity',
      }}
    >
      {children}
    </motion.div>
  );
}

/**
 * Reduced Motion Page Transition
 *
 * Alternative transition for users who prefer reduced motion.
 * Uses only opacity changes without movement.
 */
export function ReducedMotionPageTransition({
  children,
  className = '',
}: PageTransitionProps) {
  const reducedVariants = {
    initial: { opacity: 0 },
    in: { opacity: 1 },
    out: { opacity: 0 },
  };

  return (
    <motion.div
      initial='initial'
      animate='in'
      exit='out'
      variants={reducedVariants}
      transition={reducedMotionTransition}
      className={className}
    >
      {children}
    </motion.div>
  );
}
