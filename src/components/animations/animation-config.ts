/**
 * Animation Configuration
 *
 * Centralized animation settings and presets for consistent motion design.
 * Supports accessibility preferences and performance optimization.
 */

// Animation durations (in seconds)
export const DURATIONS = {
  fast: 0.2,
  normal: 0.4,
  slow: 0.6,
  slower: 0.8,
} as const;

// Easing functions
export const EASINGS = {
  easeOut: [0.0, 0.0, 0.2, 1],
  easeIn: [0.4, 0.0, 1, 1],
  easeInOut: [0.4, 0.0, 0.2, 1],
  anticipate: [0.0, 0.0, 0.2, 1],
  backOut: [0.34, 1.56, 0.64, 1],
  circOut: [0.0, 0.55, 0.45, 1],
} as const;

// Spring configurations
export const SPRINGS = {
  gentle: { type: 'spring', stiffness: 120, damping: 14 },
  wobbly: { type: 'spring', stiffness: 180, damping: 12 },
  stiff: { type: 'spring', stiffness: 300, damping: 20 },
  slow: { type: 'spring', stiffness: 80, damping: 14 },
} as const;

// Common animation variants
export const FADE_VARIANTS = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
};

export const SLIDE_VARIANTS = {
  up: {
    hidden: { opacity: 0, y: 40 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -40 },
  },
  down: {
    hidden: { opacity: 0, y: -40 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 40 },
  },
  left: {
    hidden: { opacity: 0, x: 40 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -40 },
  },
  right: {
    hidden: { opacity: 0, x: -40 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 40 },
  },
};

export const SCALE_VARIANTS = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 1.1 },
};

// Stagger configurations
export const STAGGER_CONFIGS = {
  fast: { staggerChildren: 0.05, delayChildren: 0.1 },
  normal: { staggerChildren: 0.1, delayChildren: 0.2 },
  slow: { staggerChildren: 0.2, delayChildren: 0.3 },
} as const;

// Accessibility-aware animation settings
export const getAnimationConfig = (respectMotionPreference = true) => {
  if (respectMotionPreference && typeof window !== 'undefined') {
    const prefersReducedMotion = window.matchMedia(
      '(prefers-reduced-motion: reduce)'
    ).matches;

    if (prefersReducedMotion) {
      return {
        duration: DURATIONS.fast,
        ease: EASINGS.easeInOut,
        // Disable complex animations for reduced motion
        disableAnimations: true,
      };
    }
  }

  return {
    duration: DURATIONS.normal,
    ease: EASINGS.easeOut,
    disableAnimations: false,
  };
};

// Performance optimization settings
export const PERFORMANCE_CONFIG = {
  // Use transform and opacity for better performance
  optimizedProperties: ['transform', 'opacity'],

  // Viewport settings for intersection observer
  viewport: {
    once: true,
    margin: '-50px',
    amount: 0.3,
  },

  // Will-change optimization
  willChange: {
    transform: 'transform',
    opacity: 'opacity',
    both: 'transform, opacity',
  },
};

// Animation presets for common use cases
export const ANIMATION_PRESETS = {
  pageTransition: {
    initial: SLIDE_VARIANTS.up.hidden,
    animate: SLIDE_VARIANTS.up.visible,
    exit: SLIDE_VARIANTS.up.exit,
    transition: { duration: DURATIONS.normal, ease: EASINGS.anticipate },
  },

  cardHover: {
    whileHover: { scale: 1.02, y: -4 },
    whileTap: { scale: 0.98 },
    transition: SPRINGS.gentle,
  },

  buttonPress: {
    whileTap: { scale: 0.95 },
    transition: SPRINGS.stiff,
  },

  fadeInUp: {
    initial: SLIDE_VARIANTS.up.hidden,
    whileInView: SLIDE_VARIANTS.up.visible,
    viewport: PERFORMANCE_CONFIG.viewport,
    transition: { duration: DURATIONS.normal, ease: EASINGS.easeOut },
  },

  // New advanced presets
  heroEntrance: {
    initial: { opacity: 0, y: 60, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
    transition: { duration: 0.8, ease: EASINGS.backOut },
  },

  modalAppear: {
    initial: { opacity: 0, scale: 0.8, y: 20 },
    animate: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.9, y: 10 },
    transition: { duration: 0.3, ease: EASINGS.easeOut },
  },

  slideInFromSide: {
    initial: { x: -100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 100, opacity: 0 },
    transition: { duration: 0.5, ease: EASINGS.anticipate },
  },

  bounceIn: {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    transition: {
      type: 'spring',
      stiffness: 260,
      damping: 20,
      duration: 0.6,
    },
  },

  typewriter: {
    initial: { width: 0 },
    animate: { width: 'auto' },
    transition: { duration: 2, ease: 'linear' },
  },

  glowPulse: {
    animate: {
      boxShadow: [
        '0 0 0 0 rgba(59, 130, 246, 0.7)',
        '0 0 0 10px rgba(59, 130, 246, 0)',
        '0 0 0 0 rgba(59, 130, 246, 0)',
      ],
    },
    transition: { duration: 2, repeat: Infinity },
  },
} as const;

export type AnimationPreset = keyof typeof ANIMATION_PRESETS;
export type Duration = keyof typeof DURATIONS;
export type Easing = keyof typeof EASINGS;
export type Spring = keyof typeof SPRINGS;
