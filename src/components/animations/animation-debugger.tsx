'use client';

import { Button } from '@/components/ui/button';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { ANIMATION_PRESETS } from './animation-config';

/**
 * Animation Debugger Component
 *
 * Visual tool for testing and debugging animations.
 * Shows animation presets, timing controls, and performance metrics.
 */
export function AnimationDebugger() {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedPreset, setSelectedPreset] =
    useState<keyof typeof ANIMATION_PRESETS>('fadeInUp');
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);

  // Only show in development
  useEffect(() => {
    const isDev = process.env.NODE_ENV === 'development';
    setIsVisible(isDev);

    // Keyboard shortcut to toggle debugger
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'A') {
        setIsVisible(prev => !prev);
      }
    };

    if (isDev) {
      document.addEventListener('keydown', handleKeyPress);
      return () => document.removeEventListener('keydown', handleKeyPress);
    }

    return undefined;
  }, []);

  const playAnimation = () => {
    setIsPlaying(true);
    setTimeout(() => setIsPlaying(false), 2000 / playbackSpeed);
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, x: 300 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 300 }}
        className='fixed top-4 right-4 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-4 z-50 w-80'
      >
        <div className='flex justify-between items-center mb-4'>
          <h3 className='font-bold text-sm'>Animation Debugger</h3>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => setIsVisible(false)}
            className='h-6 w-6 p-0'
          >
            ×
          </Button>
        </div>

        {/* Preset Selector */}
        <div className='mb-4'>
          <label className='block text-xs font-medium mb-2'>
            Animation Preset
          </label>
          <select
            value={selectedPreset}
            onChange={e =>
              setSelectedPreset(
                e.target.value as keyof typeof ANIMATION_PRESETS
              )
            }
            className='w-full p-2 border rounded text-xs'
          >
            {Object.keys(ANIMATION_PRESETS).map(preset => (
              <option key={preset} value={preset}>
                {preset}
              </option>
            ))}
          </select>
        </div>

        {/* Playback Controls */}
        <div className='mb-4'>
          <label className='block text-xs font-medium mb-2'>
            Playback Speed
          </label>
          <input
            type='range'
            min='0.1'
            max='3'
            step='0.1'
            value={playbackSpeed}
            onChange={e => setPlaybackSpeed(parseFloat(e.target.value))}
            className='w-full'
          />
          <div className='text-xs text-gray-500 mt-1'>{playbackSpeed}x</div>
        </div>

        {/* Animation Preview */}
        <div className='mb-4'>
          <label className='block text-xs font-medium mb-2'>Preview</label>
          <div className='border rounded p-4 h-24 flex items-center justify-center bg-gray-50 dark:bg-gray-700'>
            <AnimatePresence mode='wait'>
              {isPlaying && (
                <motion.div
                  key={selectedPreset + isPlaying}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.4 / playbackSpeed,
                  }}
                  className='w-12 h-12 bg-blue-500 rounded'
                />
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Controls */}
        <div className='flex gap-2'>
          <Button
            onClick={playAnimation}
            disabled={isPlaying}
            size='sm'
            className='flex-1'
          >
            {isPlaying ? 'Playing...' : 'Play'}
          </Button>
        </div>

        {/* Animation Details */}
        <div className='mt-4 text-xs'>
          <details>
            <summary className='cursor-pointer font-medium'>
              Animation Details
            </summary>
            <pre className='mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-auto'>
              {JSON.stringify(ANIMATION_PRESETS[selectedPreset], null, 2)}
            </pre>
          </details>
        </div>

        {/* Keyboard Shortcuts */}
        <div className='mt-4 text-xs text-gray-500'>
          <div>Ctrl+Shift+A: Toggle Debugger</div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

/**
 * Animation Timeline Visualizer
 *
 * Shows animation timeline and keyframes.
 * Useful for complex animation sequences.
 */
export function AnimationTimeline({
  animations = [],
  duration = 2,
}: {
  animations?: Array<{
    name: string;
    start: number;
    end: number;
    color?: string;
  }>;
  duration?: number;
}) {
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentTime(prev => {
        if (prev >= duration) {
          setIsPlaying(false);
          return 0;
        }
        return prev + 0.1;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isPlaying, duration]);

  return (
    <div className='bg-white dark:bg-gray-800 border rounded-lg p-4'>
      <div className='flex justify-between items-center mb-4'>
        <h4 className='font-medium'>Animation Timeline</h4>
        <Button onClick={() => setIsPlaying(!isPlaying)} size='sm'>
          {isPlaying ? 'Pause' : 'Play'}
        </Button>
      </div>

      {/* Timeline */}
      <div className='relative h-20 bg-gray-100 dark:bg-gray-700 rounded mb-4'>
        {/* Time markers */}
        <div className='absolute top-0 left-0 right-0 h-4 flex justify-between text-xs text-gray-500'>
          {Array.from({ length: Math.ceil(duration) + 1 }, (_, i) => (
            <span key={i}>{i}s</span>
          ))}
        </div>

        {/* Animation bars */}
        <div className='absolute top-4 left-0 right-0 bottom-0'>
          {animations.map((anim, index) => (
            <div
              key={index}
              className='absolute h-6 rounded text-xs text-white flex items-center px-2'
              style={{
                left: `${(anim.start / duration) * 100}%`,
                width: `${((anim.end - anim.start) / duration) * 100}%`,
                top: `${index * 28}px`,
                backgroundColor: anim.color || '#3b82f6',
              }}
            >
              {anim.name}
            </div>
          ))}
        </div>

        {/* Current time indicator */}
        <div
          className='absolute top-0 bottom-0 w-0.5 bg-red-500'
          style={{ left: `${(currentTime / duration) * 100}%` }}
        />
      </div>

      {/* Time display */}
      <div className='text-sm text-gray-600'>
        Current Time: {currentTime.toFixed(1)}s / {duration}s
      </div>
    </div>
  );
}
