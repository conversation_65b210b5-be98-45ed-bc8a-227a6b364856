'use client';

import { motion, useScroll, useSpring, useTransform } from 'framer-motion';
import { ReactNode, useRef } from 'react';

interface ParallaxProps {
  children: ReactNode;
  offset?: number;
  className?: string;
}

/**
 * Parallax Scroll Animation
 *
 * Creates parallax effect based on scroll position.
 * Perfect for hero sections and background elements.
 */
export function Parallax({
  children,
  offset = 50,
  className = '',
}: ParallaxProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, offset]);
  const smoothY = useSpring(y, { stiffness: 100, damping: 30 });

  return (
    <motion.div ref={ref} style={{ y: smoothY }} className={className}>
      {children}
    </motion.div>
  );
}

interface RevealOnScrollProps {
  children: ReactNode;
  threshold?: number;
  className?: string;
}

/**
 * Reveal on Scroll Animation
 *
 * Reveals content as it enters the viewport with smooth animation.
 * Uses intersection observer for performance.
 */
export function RevealOnScroll({
  children,
  threshold = 0.1,
  className = '',
}: RevealOnScrollProps) {
  const variants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  };

  return (
    <motion.div
      initial='hidden'
      whileInView='visible'
      viewport={{ once: true, amount: threshold }}
      variants={variants}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface CountUpProps {
  from: number;
  to: number;
  duration?: number;
  className?: string;
}

/**
 * Count Up Animation
 *
 * Animates numbers from one value to another.
 * Perfect for statistics and metrics display.
 */
export function CountUp({
  from,
  to,
  duration = 2,
  className = '',
}: CountUpProps) {
  const nodeRef = useRef<HTMLSpanElement>(null);

  const variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.span
      ref={nodeRef}
      initial='hidden'
      whileInView='visible'
      viewport={{ once: true }}
      variants={variants}
      className={className}
    >
      <motion.span
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration }}
        onUpdate={latest => {
          if (nodeRef.current && typeof latest.opacity === 'number') {
            const progress = latest.opacity;
            const currentValue = Math.round(from + (to - from) * progress);
            nodeRef.current.textContent = currentValue.toString();
          }
        }}
      >
        {from}
      </motion.span>
    </motion.span>
  );
}

interface TypewriterProps {
  text: string;
  delay?: number;
  speed?: number;
  className?: string;
}

/**
 * Typewriter Animation
 *
 * Types out text character by character.
 * Great for hero headlines and dynamic content.
 */
export function Typewriter({
  text,
  delay = 0,
  speed = 0.05,
  className = '',
}: TypewriterProps) {
  const variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delay,
        staggerChildren: speed,
        delayChildren: delay,
      },
    },
  };

  const childVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  };

  return (
    <motion.span
      variants={variants}
      initial='hidden'
      whileInView='visible'
      viewport={{ once: true }}
      className={className}
    >
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          variants={childVariants}
          style={{ display: char === ' ' ? 'inline' : 'inline-block' }}
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </motion.span>
  );
}

interface MorphingShapeProps {
  shapes: string[];
  duration?: number;
  className?: string;
}

/**
 * Morphing Shape Animation
 *
 * Morphs between different SVG path shapes.
 * Perfect for icons and decorative elements.
 */
export function MorphingShape({
  shapes,
  duration = 2,
  className = '',
}: MorphingShapeProps) {
  return (
    <svg className={className} viewBox='0 0 100 100'>
      <motion.path
        d={shapes[0]}
        animate={{ d: shapes }}
        transition={{
          duration,
          repeat: Infinity,
          repeatType: 'loop',
          ease: 'easeInOut',
        }}
        fill='currentColor'
      />
    </svg>
  );
}
