/**
 * Animation Utilities
 *
 * Helper functions and hooks for working with framer-motion animations.
 * Provides accessibility-aware animation controls and performance optimizations.
 */

import { useEffect, useState } from 'react';
import { Variants } from 'framer-motion';
import { DURATIONS, EASINGS, getAnimationConfig } from './animation-config';

/**
 * Hook to detect user's motion preferences
 */
export function useReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

/**
 * Hook to get animation configuration based on user preferences
 */
export function useAnimationConfig() {
  const prefersReducedMotion = useReducedMotion();

  return {
    shouldAnimate: !prefersReducedMotion,
    duration: prefersReducedMotion ? DURATIONS.fast : DURATIONS.normal,
    ease: prefersReducedMotion ? EASINGS.easeInOut : EASINGS.easeOut,
  };
}

/**
 * Create variants with reduced motion support
 */
export function createVariants(
  normalVariants: Variants,
  reducedMotionVariants?: Variants
): Variants {
  const config = getAnimationConfig();

  if (config.disableAnimations && reducedMotionVariants) {
    return reducedMotionVariants;
  }

  return normalVariants;
}

/**
 * Generate staggered animation variants
 */
export function createStaggerVariants(
  itemVariants: Variants,
  staggerDelay = 0.1,
  delayChildren = 0.1
): { container: Variants; item: Variants } {
  return {
    container: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: staggerDelay,
          delayChildren,
        },
      },
    },
    item: itemVariants,
  };
}

/**
 * Create responsive animation variants based on screen size
 */
export function createResponsiveVariants(
  mobileVariants: Variants,
  desktopVariants: Variants
): Variants {
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  return isMobile ? mobileVariants : desktopVariants;
}

/**
 * Animation timing utilities
 */
export const timing = {
  /**
   * Calculate stagger delay for multiple items
   */
  stagger: (index: number, baseDelay = 0.1) => index * baseDelay,

  /**
   * Create progressive delay for cascading animations
   */
  cascade: (index: number, total: number, duration = 1) =>
    (index / total) * duration,

  /**
   * Generate random delay within range
   */
  random: (min = 0, max = 0.5) => Math.random() * (max - min) + min,
};

/**
 * Common animation patterns
 */
export const patterns = {
  /**
   * Fade in with upward movement
   */
  fadeInUp: (delay = 0): Variants => ({
    hidden: { opacity: 0, y: 40 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: DURATIONS.normal,
        delay,
        ease: EASINGS.easeOut,
      },
    },
  }),

  /**
   * Scale in with fade
   */
  scaleIn: (delay = 0): Variants => ({
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: DURATIONS.normal,
        delay,
        ease: EASINGS.backOut,
      },
    },
  }),

  /**
   * Slide in from direction
   */
  slideIn: (
    direction: 'left' | 'right' | 'up' | 'down',
    delay = 0
  ): Variants => {
    const directions = {
      left: { x: -100 },
      right: { x: 100 },
      up: { y: -100 },
      down: { y: 100 },
    };

    return {
      hidden: { opacity: 0, ...directions[direction] },
      visible: {
        opacity: 1,
        x: 0,
        y: 0,
        transition: {
          duration: DURATIONS.normal,
          delay,
          ease: EASINGS.easeOut,
        },
      },
    };
  },
};

/**
 * Performance optimization helpers
 */
export const performance = {
  /**
   * Get optimized style properties for animations
   */
  getOptimizedStyles: (properties: string[]) => ({
    willChange: properties.join(', '),
  }),

  /**
   * Viewport configuration for intersection observer
   */
  viewport: {
    once: true,
    margin: '-50px',
    amount: 0.3,
  },

  /**
   * Reduce motion for accessibility
   */
  reduceMotion: (variants: Variants): Variants => {
    const reduced: Variants = {};

    Object.keys(variants).forEach(key => {
      const variant = variants[key];
      if (typeof variant === 'object' && variant !== null) {
        reduced[key] = {
          ...variant,
          transition: {
            duration: DURATIONS.fast,
            ease: EASINGS.easeInOut,
          },
        };
      }
    });

    return reduced;
  },
};
