'use client';

import { motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';

/**
 * Animation Performance Monitor
 *
 * Monitors animation performance and provides optimization suggestions.
 * Tracks frame rate, animation duration, and resource usage.
 */
export function useAnimationPerformance() {
  const [metrics, setMetrics] = useState({
    fps: 0,
    frameDrops: 0,
    animationCount: 0,
    averageDuration: 0,
    isOptimal: true,
  });

  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const animationTimes = useRef<number[]>([]);

  useEffect(() => {
    let animationId: number;

    const measureFPS = () => {
      const now = performance.now();
      const delta = now - lastTime.current;

      if (delta >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / delta);
        const frameDrops = Math.max(0, 60 - fps);

        setMetrics(prev => ({
          ...prev,
          fps,
          frameDrops,
          isOptimal: fps >= 55 && frameDrops <= 5,
        }));

        frameCount.current = 0;
        lastTime.current = now;
      }

      frameCount.current++;
      animationId = requestAnimationFrame(measureFPS);
    };

    measureFPS();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  const trackAnimation = (duration: number) => {
    animationTimes.current.push(duration);

    if (animationTimes.current.length > 10) {
      animationTimes.current.shift();
    }

    const average =
      animationTimes.current.reduce((a, b) => a + b, 0) /
      animationTimes.current.length;

    setMetrics(prev => ({
      ...prev,
      animationCount: prev.animationCount + 1,
      averageDuration: average,
    }));
  };

  return { metrics, trackAnimation };
}

/**
 * Performance Optimized Motion Component
 *
 * Wrapper around motion.div with built-in performance monitoring.
 * Automatically applies performance optimizations.
 */
interface OptimizedMotionProps {
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}

export function OptimizedMotion({
  children,
  className = '',
  ...props
}: OptimizedMotionProps) {
  const { trackAnimation } = useAnimationPerformance();
  const startTime = useRef<number>(0);

  const handleAnimationStart = () => {
    startTime.current = performance.now();
  };

  const handleAnimationComplete = () => {
    const duration = performance.now() - startTime.current;
    trackAnimation(duration);
  };

  return (
    <motion.div
      className={className}
      style={{
        willChange: 'transform, opacity',
        backfaceVisibility: 'hidden',
        perspective: 1000,
      }}
      onAnimationStart={handleAnimationStart}
      onAnimationComplete={handleAnimationComplete}
      {...props}
    >
      {children}
    </motion.div>
  );
}

/**
 * Animation Performance Dashboard
 *
 * Visual dashboard showing animation performance metrics.
 * Useful for development and debugging.
 */
export function AnimationPerformanceDashboard() {
  const { metrics } = useAnimationPerformance();
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development');
  }, []);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className='fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50'
    >
      <h3 className='font-bold mb-2'>Animation Performance</h3>
      <div className='space-y-1'>
        <div
          className={`${metrics.fps >= 55 ? 'text-green-400' : 'text-red-400'}`}
        >
          FPS: {metrics.fps}
        </div>
        <div
          className={`${metrics.frameDrops <= 5 ? 'text-green-400' : 'text-yellow-400'}`}
        >
          Frame Drops: {metrics.frameDrops}
        </div>
        <div>Animations: {metrics.animationCount}</div>
        <div>Avg Duration: {metrics.averageDuration.toFixed(1)}ms</div>
        <div
          className={`${metrics.isOptimal ? 'text-green-400' : 'text-red-400'}`}
        >
          Status: {metrics.isOptimal ? 'Optimal' : 'Needs Optimization'}
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Animation Budget Manager
 *
 * Manages animation complexity based on device capabilities.
 * Automatically reduces animations on low-end devices.
 */
export function useAnimationBudget() {
  const [budget, setBudget] = useState({
    maxConcurrentAnimations: 10,
    complexAnimationsEnabled: true,
    particleEffectsEnabled: true,
    highFidelityEnabled: true,
  });

  useEffect(() => {
    // Detect device capabilities
    const detectCapabilities = () => {
      const canvas = document.createElement('canvas');
      const gl =
        canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      const hasWebGL = !!gl;

      // Rough performance estimation
      // const memoryInfo = (performance as any).memory;
      const deviceMemory = (navigator as any).deviceMemory || 4;
      const hardwareConcurrency = navigator.hardwareConcurrency || 4;

      const isLowEnd = deviceMemory < 4 || hardwareConcurrency < 4;
      const isMobile =
        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        );

      setBudget({
        maxConcurrentAnimations: isLowEnd ? 5 : isMobile ? 8 : 15,
        complexAnimationsEnabled: !isLowEnd,
        particleEffectsEnabled: hasWebGL && !isLowEnd,
        highFidelityEnabled: !isMobile && !isLowEnd,
      });
    };

    detectCapabilities();
  }, []);

  return budget;
}
