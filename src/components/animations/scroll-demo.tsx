'use client';

import {
  ParallaxScroll,
  ScrollProgress,
  ScrollReveal,
  ScrollToTop,
  StaggeredScrollReveal,
} from './scroll-animations';

/**
 * Scroll Animations Demo
 *
 * 演示各种滚动动画效果
 * 包括您描述的"页面下拉、上移过程中逐步浮现内容"效果
 */
export function ScrollAnimationsDemo() {
  const demoSections = [
    { title: '第一个内容区块', content: '这是第一个会在滚动时显示的内容' },
    { title: '第二个内容区块', content: '这是第二个会在滚动时显示的内容' },
    { title: '第三个内容区块', content: '这是第三个会在滚动时显示的内容' },
    { title: '第四个内容区块', content: '这是第四个会在滚动时显示的内容' },
  ];

  const cardItems = [
    '卡片 1 - 依次显示',
    '卡片 2 - 依次显示',
    '卡片 3 - 依次显示',
    '卡片 4 - 依次显示',
  ];

  return (
    <div className='min-h-screen'>
      {/* 滚动进度指示器 */}
      <ScrollProgress />

      {/* 英雄区域 */}
      <section className='h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-white'>
        <ScrollReveal direction='scale' duration={1}>
          <div className='text-center'>
            <h1 className='text-4xl font-bold mb-4'>滚动动画演示</h1>
            <p className='text-lg text-gray-600'>向下滚动查看各种动画效果</p>
          </div>
        </ScrollReveal>
      </section>

      {/* 基础滚动显示效果 */}
      <section className='py-20 px-8'>
        <ScrollReveal direction='up' delay={0.2}>
          <h2 className='text-3xl font-bold text-center mb-16'>
            基础滚动显示效果
          </h2>
        </ScrollReveal>

        <div className='max-w-4xl mx-auto space-y-16'>
          {demoSections.map((section, index) => (
            <ScrollReveal
              key={index}
              direction={index % 2 === 0 ? 'left' : 'right'}
              delay={0.1}
            >
              <div className='bg-white p-8 rounded-lg shadow-lg'>
                <h3 className='text-xl font-semibold mb-4'>{section.title}</h3>
                <p className='text-gray-600'>{section.content}</p>
              </div>
            </ScrollReveal>
          ))}
        </div>
      </section>

      {/* 交错动画效果 */}
      <section className='py-20 px-8 bg-gray-50'>
        <ScrollReveal direction='up'>
          <h2 className='text-3xl font-bold text-center mb-16'>交错动画效果</h2>
        </ScrollReveal>

        <div className='max-w-4xl mx-auto'>
          <StaggeredScrollReveal
            direction='up'
            staggerDelay={0.15}
            className='grid grid-cols-1 md:grid-cols-2 gap-6'
          >
            {cardItems.map((item, index) => (
              <div key={index} className='bg-white p-6 rounded-lg shadow-md'>
                <h4 className='font-semibold mb-2'>{item}</h4>
                <p className='text-gray-600'>
                  这些卡片会依次出现，创造流畅的视觉效果
                </p>
              </div>
            ))}
          </StaggeredScrollReveal>
        </div>
      </section>

      {/* 视差滚动效果 */}
      <section className='relative h-screen overflow-hidden'>
        <ParallaxScroll speed={-0.5} className='absolute inset-0'>
          <div className='h-full bg-gradient-to-b from-purple-400 to-pink-400 flex items-center justify-center'>
            <div className='text-white text-center'>
              <h2 className='text-4xl font-bold mb-4'>视差滚动背景</h2>
              <p className='text-lg'>背景移动速度与前景不同</p>
            </div>
          </div>
        </ParallaxScroll>

        <div className='relative z-10 h-full flex items-center justify-center'>
          <ScrollReveal direction='scale'>
            <div className='bg-white/90 backdrop-blur-sm p-8 rounded-lg text-center'>
              <h3 className='text-2xl font-bold mb-4'>前景内容</h3>
              <p className='text-gray-700'>
                这个内容在前景，背景会以不同速度移动
              </p>
            </div>
          </ScrollReveal>
        </div>
      </section>

      {/* 不同方向的动画 */}
      <section className='py-20 px-8'>
        <ScrollReveal direction='up'>
          <h2 className='text-3xl font-bold text-center mb-16'>
            不同方向的动画
          </h2>
        </ScrollReveal>

        <div className='max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8'>
          <ScrollReveal direction='left'>
            <div className='bg-blue-100 p-6 rounded-lg text-center'>
              <h4 className='font-semibold mb-2'>从左侧进入</h4>
              <p className='text-sm text-gray-600'>
                direction=&ldquo;left&rdquo;
              </p>
            </div>
          </ScrollReveal>

          <ScrollReveal direction='up'>
            <div className='bg-green-100 p-6 rounded-lg text-center'>
              <h4 className='font-semibold mb-2'>从下方进入</h4>
              <p className='text-sm text-gray-600'>
                direction=&ldquo;up&rdquo;
              </p>
            </div>
          </ScrollReveal>

          <ScrollReveal direction='right'>
            <div className='bg-purple-100 p-6 rounded-lg text-center'>
              <h4 className='font-semibold mb-2'>从右侧进入</h4>
              <p className='text-sm text-gray-600'>
                direction=&ldquo;right&rdquo;
              </p>
            </div>
          </ScrollReveal>
        </div>
      </section>

      {/* 缩放和淡入效果 */}
      <section className='py-20 px-8 bg-gray-50'>
        <div className='max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8'>
          <ScrollReveal direction='scale' delay={0.1}>
            <div className='bg-white p-8 rounded-lg shadow-lg text-center'>
              <h4 className='text-xl font-semibold mb-4'>缩放进入</h4>
              <p className='text-gray-600'>从小到大的缩放动画效果</p>
            </div>
          </ScrollReveal>

          <ScrollReveal direction='fade' delay={0.2}>
            <div className='bg-white p-8 rounded-lg shadow-lg text-center'>
              <h4 className='text-xl font-semibold mb-4'>淡入效果</h4>
              <p className='text-gray-600'>纯透明度变化的淡入动画</p>
            </div>
          </ScrollReveal>
        </div>
      </section>

      {/* 结尾区域 */}
      <section className='py-20 px-8'>
        <ScrollReveal direction='up'>
          <div className='text-center'>
            <h2 className='text-3xl font-bold mb-4'>演示结束</h2>
            <p className='text-lg text-gray-600'>
              这就是您描述的&ldquo;页面下拉、上移过程中逐步浮现内容&rdquo;的效果
            </p>
          </div>
        </ScrollReveal>
      </section>

      {/* 回到顶部按钮 */}
      <ScrollToTop threshold={500} />
    </div>
  );
}
