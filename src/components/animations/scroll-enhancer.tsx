'use client';

import { useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

/**
 * Scroll Enhancer Component
 *
 * 为现有页面添加滚动动画效果的客户端组件
 * 通过CSS类名选择器自动为元素添加动画
 */
export function ScrollEnhancer() {
  const { scrollYProgress } = useScroll();

  useEffect(() => {
    // 为带有特定类名的元素添加滚动动画
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '-50px 0px',
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;

          // 根据类名应用不同的动画
          if (element.classList.contains('scroll-reveal-scale')) {
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
          } else if (element.classList.contains('scroll-reveal-up')) {
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
          } else if (element.classList.contains('scroll-stagger-item')) {
            const delay =
              Array.from(element.parentElement?.children || []).indexOf(
                element
              ) * 100;
            setTimeout(() => {
              element.style.transform = 'translateY(0)';
              element.style.opacity = '1';
            }, delay);
          }
        }
      });
    }, observerOptions);

    // 初始化元素样式并开始观察
    const initializeElements = () => {
      // 缩放动画元素
      document.querySelectorAll('.scroll-reveal-scale').forEach(el => {
        const element = el as HTMLElement;
        element.style.transform = 'scale(0.8)';
        element.style.opacity = '0';
        element.style.transition =
          'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        observer.observe(element);
      });

      // 上滑动画元素
      document.querySelectorAll('.scroll-reveal-up').forEach(el => {
        const element = el as HTMLElement;
        element.style.transform = 'translateY(60px)';
        element.style.opacity = '0';
        element.style.transition =
          'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        observer.observe(element);
      });

      // 交错动画元素
      document.querySelectorAll('.scroll-stagger-item').forEach(el => {
        const element = el as HTMLElement;
        element.style.transform = 'translateY(40px)';
        element.style.opacity = '0';
        element.style.transition =
          'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        observer.observe(element);
      });
    };

    // 延迟初始化以确保DOM已渲染
    const timer = setTimeout(initializeElements, 100);

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);

  return (
    <>
      {/* 滚动进度指示器 */}
      <motion.div
        className='fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left'
        style={{ scaleX: scrollYProgress }}
      />
    </>
  );
}

/**
 * Scroll to Top Button
 *
 * 滚动到顶部按钮，滚动一定距离后显示
 */
export function ScrollToTopButton() {
  const { scrollY } = useScroll();
  const opacity = useTransform(scrollY, [0, 300], [0, 1]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <motion.button
      onClick={scrollToTop}
      style={{ opacity }}
      className='fixed bottom-8 right-8 p-3 bg-primary text-primary-foreground rounded-full shadow-lg z-50 hover:scale-110 transition-transform'
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      aria-label='回到顶部'
    >
      <svg
        className='w-5 h-5'
        fill='none'
        stroke='currentColor'
        viewBox='0 0 24 24'
      >
        <path
          strokeLinecap='round'
          strokeLinejoin='round'
          strokeWidth={2}
          d='M5 10l7-7m0 0l7 7m-7-7v18'
        />
      </svg>
    </motion.button>
  );
}

/**
 * Page Scroll Animations Provider
 *
 * 为整个页面提供滚动动画功能的提供者组件
 */
export function PageScrollAnimations({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <ScrollEnhancer />
      <ScrollToTopButton />
      {children}
    </>
  );
}
