'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import type { ComponentProps } from 'react';

type ThemeProviderProps = ComponentProps<typeof NextThemesProvider>;

/**
 * Theme Provider Component
 *
 * Provides theme context for the entire application with next-themes.
 * Supports light, dark, and system theme modes with proper SSR handling.
 *
 * Features:
 * - Light/Dark/System theme modes
 * - SSR-safe theme switching
 * - Automatic system preference detection
 * - Smooth theme transitions
 * - Performance optimized
 */
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute='class'
      defaultTheme='system'
      enableSystem
      disableTransitionOnChange={false}
      storageKey='tucsenberg-theme'
      {...props}
    >
      {children}
    </NextThemesProvider>
  );
}
