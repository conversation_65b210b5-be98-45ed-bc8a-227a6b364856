'use client';

import { useTheme } from 'next-themes';
import * as React from 'react';

import { SimpleThemeToggle, ThemeToggle } from './theme-toggle';

/**
 * Theme Demo Component
 *
 * Demonstrates theme functionality and provides testing interface.
 * Shows current theme state and provides multiple toggle options.
 */
export function ThemeDemo() {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className='rounded-lg border p-6'>
        <h3 className='text-lg font-semibold mb-4'>Theme Demo</h3>
        <p>Loading theme information...</p>
      </div>
    );
  }

  return (
    <div className='rounded-lg border p-6 space-y-4'>
      <h3 className='text-lg font-semibold'>Theme Demo</h3>

      {/* Theme Information */}
      <div className='space-y-2 text-sm'>
        <p>
          <strong>Current Theme:</strong> {theme}
        </p>
        <p>
          <strong>System Theme:</strong> {systemTheme}
        </p>
        <p>
          <strong>Resolved Theme:</strong> {resolvedTheme}
        </p>
      </div>

      {/* Theme Toggles */}
      <div className='flex items-center gap-4'>
        <div className='flex items-center gap-2'>
          <span className='text-sm'>Dropdown Toggle:</span>
          <ThemeToggle />
        </div>

        <div className='flex items-center gap-2'>
          <span className='text-sm'>Simple Toggle:</span>
          <SimpleThemeToggle />
        </div>
      </div>

      {/* Color Swatches */}
      <div className='space-y-2'>
        <h4 className='font-medium'>Color Swatches</h4>
        <div className='grid grid-cols-4 gap-2'>
          <div className='h-12 rounded bg-background border flex items-center justify-center text-xs'>
            Background
          </div>
          <div className='h-12 rounded bg-foreground text-background flex items-center justify-center text-xs'>
            Foreground
          </div>
          <div className='h-12 rounded bg-primary text-primary-foreground flex items-center justify-center text-xs'>
            Primary
          </div>
          <div className='h-12 rounded bg-secondary text-secondary-foreground flex items-center justify-center text-xs'>
            Secondary
          </div>
          <div className='h-12 rounded bg-muted text-muted-foreground flex items-center justify-center text-xs'>
            Muted
          </div>
          <div className='h-12 rounded bg-accent text-accent-foreground flex items-center justify-center text-xs'>
            Accent
          </div>
          <div className='h-12 rounded bg-destructive text-destructive-foreground flex items-center justify-center text-xs'>
            Destructive
          </div>
          <div className='h-12 rounded border-2 border-border flex items-center justify-center text-xs'>
            Border
          </div>
        </div>
      </div>
    </div>
  );
}
