'use client';

import { <PERSON>, <PERSON>, Sun } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

/**
 * Theme Toggle Component
 *
 * Provides a dropdown menu for switching between light, dark, and system themes.
 * Uses shadcn/ui components for consistent styling and accessibility.
 *
 * Features:
 * - Light/Dark/System theme options
 * - Accessible dropdown menu
 * - Proper SSR handling
 * - Smooth animations
 * - Keyboard navigation support
 * - Internationalization support
 * - Performance optimized
 */
export const ThemeToggle = React.memo(function ThemeToggle() {
  const { setTheme, theme } = useTheme();
  const t = useTranslations('Theme');
  const [mounted, setMounted] = React.useState(false);

  // Prevent hydration mismatch
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Memoize theme change handlers
  const handleLightTheme = React.useCallback(
    () => setTheme('light'),
    [setTheme]
  );
  const handleDarkTheme = React.useCallback(() => setTheme('dark'), [setTheme]);
  const handleSystemTheme = React.useCallback(
    () => setTheme('system'),
    [setTheme]
  );

  if (!mounted) {
    return (
      <Button variant='ghost' size='sm' className='w-9 px-0'>
        <Sun className='h-[1.2rem] w-[1.2rem]' />
        <span className='sr-only'>Toggle theme</span>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className='w-9 px-0'
          aria-label={t('toggle')}
        >
          <Sun className='h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0' />
          <Moon className='absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100' />
          <span className='sr-only'>{t('toggle')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem
          onClick={handleLightTheme}
          className={`cursor-pointer ${theme === 'light' ? 'bg-accent' : ''}`}
        >
          <Sun className='mr-2 h-4 w-4' />
          <span>{t('light')}</span>
          {theme === 'light' && <span className='ml-auto text-xs'>✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleDarkTheme}
          className={`cursor-pointer ${theme === 'dark' ? 'bg-accent' : ''}`}
        >
          <Moon className='mr-2 h-4 w-4' />
          <span>{t('dark')}</span>
          {theme === 'dark' && <span className='ml-auto text-xs'>✓</span>}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleSystemTheme}
          className={`cursor-pointer ${theme === 'system' ? 'bg-accent' : ''}`}
        >
          <Monitor className='mr-2 h-4 w-4' />
          <span>{t('system')}</span>
          {theme === 'system' && <span className='ml-auto text-xs'>✓</span>}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
});

/**
 * Simple Theme Toggle Button (Alternative Implementation)
 *
 * A simpler toggle button that switches between light and dark modes only.
 * Useful for minimal UI designs or when system mode is not needed.
 */
export function SimpleThemeToggle() {
  const { setTheme, theme } = useTheme();
  const t = useTranslations('Theme');
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button variant='ghost' size='sm' className='w-9 px-0'>
        <Sun className='h-[1.2rem] w-[1.2rem]' />
        <span className='sr-only'>Toggle theme</span>
      </Button>
    );
  }

  return (
    <Button
      variant='ghost'
      size='sm'
      className='w-9 px-0'
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      aria-label={t('toggle')}
    >
      <Sun className='h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0' />
      <Moon className='absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100' />
      <span className='sr-only'>{t('toggle')}</span>
    </Button>
  );
}
