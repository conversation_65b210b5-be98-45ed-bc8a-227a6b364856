'use client';

import { useTheme } from 'next-themes';
import * as React from 'react';

/**
 * Theme Transition Component
 *
 * Provides smooth visual transitions when switching themes.
 * Temporarily disables transitions during theme changes to prevent flashing.
 */
export function ThemeTransition({ children }: { children: React.ReactNode }) {
  const { theme } = useTheme();
  const [isTransitioning, setIsTransitioning] = React.useState(false);
  const previousTheme = React.useRef(theme);

  React.useEffect(() => {
    if (previousTheme.current !== theme) {
      setIsTransitioning(true);

      // Add transition class to body
      document.body.classList.add('theme-transitioning');

      // Remove transition class after animation
      const timer = setTimeout(() => {
        setIsTransitioning(false);
        document.body.classList.remove('theme-transitioning');
      }, 150);

      previousTheme.current = theme;

      return () => clearTimeout(timer);
    }
    return undefined; // Explicit return for else case
  }, [theme]);

  return (
    <div className={isTransitioning ? 'theme-transitioning' : ''}>
      {children}
    </div>
  );
}

/**
 * Theme Transition Styles
 *
 * CSS-in-JS styles for smooth theme transitions.
 * Add these styles to your global CSS.
 */
export const themeTransitionStyles = `
  .theme-transitioning,
  .theme-transitioning * {
    transition: background-color 150ms ease-in-out, 
                border-color 150ms ease-in-out, 
                color 150ms ease-in-out !important;
  }

  /* Disable transitions for specific elements during theme change */
  .theme-transitioning .no-transition,
  .theme-transitioning .no-transition * {
    transition: none !important;
  }
`;

/**
 * Theme Aware Component
 *
 * Higher-order component that provides theme-aware styling.
 */
export function withThemeTransition<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = React.forwardRef<unknown, P>((props, ref) => (
    <ThemeTransition>
      <Component {...props} ref={ref as React.Ref<unknown>} />
    </ThemeTransition>
  ));

  WrappedComponent.displayName = `withThemeTransition(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
