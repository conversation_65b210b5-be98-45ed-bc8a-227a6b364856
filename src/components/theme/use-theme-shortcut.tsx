'use client';

import * as React from 'react';
import { useTheme } from 'next-themes';

/**
 * Theme Keyboard Shortcut Hook
 *
 * Provides keyboard shortcuts for theme switching:
 * - Ctrl/Cmd + Shift + L: Light theme
 * - Ctrl/Cmd + Shift + D: Dark theme
 * - Ctrl/Cmd + Shift + S: System theme
 */
export function useThemeShortcut() {
  const { setTheme } = useTheme();

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for Ctrl/Cmd + Shift combination
      if ((event.ctrlKey || event.metaKey) && event.shiftKey) {
        switch (event.key.toLowerCase()) {
          case 'l':
            event.preventDefault();
            setTheme('light');
            break;
          case 'd':
            event.preventDefault();
            setTheme('dark');
            break;
          case 's':
            event.preventDefault();
            setTheme('system');
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [setTheme]);
}

/**
 * Theme Shortcut Provider
 *
 * Wraps children with theme keyboard shortcut functionality.
 * Add this to your app layout to enable global theme shortcuts.
 */
export function ThemeShortcutProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  useThemeShortcut();
  return <>{children}</>;
}
