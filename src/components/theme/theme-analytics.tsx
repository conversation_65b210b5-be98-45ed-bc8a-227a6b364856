'use client';

import { useTheme } from 'next-themes';
import * as React from 'react';

/**
 * Theme Analytics Hook
 *
 * Tracks theme usage patterns and user preferences.
 * Provides insights into theme switching behavior.
 */
export function useThemeAnalytics() {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [analytics, setAnalytics] = React.useState({
    themeChanges: 0,
    sessionStartTheme: theme,
    mostUsedTheme: theme,
    timeSpentInThemes: {
      light: 0,
      dark: 0,
      system: 0,
    },
    lastChanged: Date.now(),
  });

  // Track theme changes
  React.useEffect(() => {
    if (theme && theme !== analytics.sessionStartTheme) {
      setAnalytics(prev => ({
        ...prev,
        themeChanges: prev.themeChanges + 1,
        lastChanged: Date.now(),
      }));
    }
  }, [theme, analytics.sessionStartTheme]);

  // Track time spent in each theme
  React.useEffect(() => {
    if (!theme) return;

    const startTime = Date.now();

    return () => {
      const timeSpent = Date.now() - startTime;
      setAnalytics(prev => ({
        ...prev,
        timeSpentInThemes: {
          ...prev.timeSpentInThemes,
          [theme]:
            prev.timeSpentInThemes[
              theme as keyof typeof prev.timeSpentInThemes
            ] + timeSpent,
        },
      }));
    };
  }, [theme]);

  // Determine most used theme
  React.useEffect(() => {
    const { timeSpentInThemes } = analytics;
    const mostUsed = Object.entries(timeSpentInThemes).reduce((a, b) =>
      timeSpentInThemes[a[0] as keyof typeof timeSpentInThemes] >
      timeSpentInThemes[b[0] as keyof typeof timeSpentInThemes]
        ? a
        : b
    )[0];

    if (mostUsed !== analytics.mostUsedTheme) {
      setAnalytics(prev => ({ ...prev, mostUsedTheme: mostUsed }));
    }
  }, [analytics.timeSpentInThemes, analytics.mostUsedTheme, analytics]);

  return {
    ...analytics,
    currentTheme: theme,
    systemTheme,
    resolvedTheme,
    isSystemThemeUser: theme === 'system',
    isFrequentSwitcher: analytics.themeChanges > 3,
  };
}

/**
 * Theme Preference Detector
 *
 * Detects user theme preferences based on system settings and usage patterns.
 */
export function useThemePreferenceDetector() {
  const analytics = useThemeAnalytics();
  const [preferences, setPreferences] = React.useState({
    prefersDark: false,
    prefersLight: false,
    prefersSystem: false,
    confidence: 0,
  });

  React.useEffect(() => {
    const { timeSpentInThemes, isSystemThemeUser } = analytics;
    const totalTime = Object.values(timeSpentInThemes).reduce(
      (a, b) => a + b,
      0
    );

    if (totalTime > 0) {
      const darkPercentage = timeSpentInThemes.dark / totalTime;
      const lightPercentage = timeSpentInThemes.light / totalTime;
      const systemPercentage = timeSpentInThemes.system / totalTime;

      setPreferences({
        prefersDark: darkPercentage > 0.6,
        prefersLight: lightPercentage > 0.6,
        prefersSystem: systemPercentage > 0.6 || isSystemThemeUser,
        confidence: Math.max(darkPercentage, lightPercentage, systemPercentage),
      });
    }
  }, [analytics]);

  return preferences;
}
