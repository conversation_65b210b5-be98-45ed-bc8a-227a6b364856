/**
 * Theme Components Export
 *
 * Centralized export for all theme-related components and utilities.
 * Provides a clean API for importing theme functionality.
 */

export { ThemeDemo } from './theme-demo';
export { ThemeProvider } from './theme-provider';
export { SimpleThemeToggle, ThemeToggle } from './theme-toggle';

// Advanced theme features
export {
  useThemeAnalytics,
  useThemePreferenceDetector,
} from './theme-analytics';
export {
  ThemeTransition,
  themeTransitionStyles,
  withThemeTransition,
} from './theme-transition';
export { ThemeShortcutProvider, useThemeShortcut } from './use-theme-shortcut';

// Re-export useTheme hook for convenience
export { useTheme } from 'next-themes';

/**
 * Theme utilities and constants
 */
export const THEME_STORAGE_KEY = 'tucsenberg-theme';
export const THEME_ATTRIBUTE = 'class';
export const DEFAULT_THEME = 'system';

export type Theme = 'light' | 'dark' | 'system';

/**
 * Theme configuration for next-themes
 */
export const themeConfig = {
  attribute: THEME_ATTRIBUTE,
  defaultTheme: DEFAULT_THEME,
  enableSystem: true,
  disableTransitionOnChange: false,
  storageKey: THEME_STORAGE_KEY,
} as const;

/**
 * Theme keyboard shortcuts
 */
export const THEME_SHORTCUTS = {
  light: 'Ctrl/Cmd + Shift + L',
  dark: 'Ctrl/Cmd + Shift + D',
  system: 'Ctrl/Cmd + Shift + S',
} as const;
