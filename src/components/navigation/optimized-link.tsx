/**
 * Optimized Link Component for International Routing
 *
 * This component provides performance-optimized navigation with:
 * - Intelligent prefetching based on user interaction
 * - Locale-aware routing without unnecessary redirects
 * - Performance monitoring integration
 * - SEO-friendly link generation
 */

'use client';

import { Link, useRouter } from '@/i18n/navigation';
import { type Locale } from '@/i18n/routing';
import { useRoutePerformance } from '@/lib/route-performance';
import { useLocale } from 'next-intl';
import {
  ComponentProps,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

interface OptimizedLinkProps
  extends Omit<ComponentProps<typeof Link>, 'href' | 'prefetch'> {
  href: string;
  locale?: Locale;
  prefetch?: boolean | 'hover' | 'viewport';
  trackPerformance?: boolean;
  preloadDelay?: number;
}

/**
 * Optimized Link Component
 */
export function OptimizedLink({
  href,
  locale,
  prefetch = 'hover',
  trackPerformance = true,
  preloadDelay = 100,
  children,
  onMouseEnter,
  onFocus,
  onClick,
  ...props
}: OptimizedLinkProps) {
  const currentLocale = useLocale() as Locale;
  const router = useRouter();
  const { startNavigation, recordRedirect } = useRoutePerformance();
  const linkRef = useRef<HTMLAnchorElement>(null);
  const [isPrefetched, setIsPrefetched] = useState(false);
  const [isInViewport, setIsInViewport] = useState(false);
  const prefetchTimeoutRef = useRef<NodeJS.Timeout>();

  // Use provided locale or current locale
  const targetLocale = locale || currentLocale;

  // Generate optimized href (currently using href directly)
  // const optimizedHref = href.startsWith('/') ? `/${targetLocale}${href}` : href;

  /**
   * Prefetch the route
   */
  const prefetchRoute = useCallback(() => {
    if (isPrefetched || !href.startsWith('/')) return;

    try {
      router.prefetch(href);
      setIsPrefetched(true);
    } catch (error) {
      console.warn('Failed to prefetch route:', href, error);
    }
  }, [isPrefetched, href, router]);

  /**
   * Handle mouse enter with delayed prefetch
   */
  const handleMouseEnter = (event: React.MouseEvent<HTMLAnchorElement>) => {
    if (prefetch === 'hover' || prefetch === true) {
      prefetchTimeoutRef.current = setTimeout(prefetchRoute, preloadDelay);
    }
    onMouseEnter?.(event);
  };

  /**
   * Handle focus with immediate prefetch
   */
  const handleFocus = (event: React.FocusEvent<HTMLAnchorElement>) => {
    if (prefetch === 'hover' || prefetch === true) {
      prefetchRoute();
    }
    onFocus?.(event);
  };

  /**
   * Handle click with performance tracking
   */
  const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    // Clear prefetch timeout
    if (prefetchTimeoutRef.current) {
      clearTimeout(prefetchTimeoutRef.current);
    }

    // Track performance if enabled
    if (trackPerformance) {
      startNavigation(href, targetLocale);

      // Record redirect if locale changed
      if (targetLocale !== currentLocale) {
        recordRedirect(
          `/${currentLocale}${href}`,
          `/${targetLocale}${href}`,
          'manual'
        );
      }
    }

    onClick?.(event);
  };

  /**
   * Handle mouse leave - cancel prefetch
   */
  const handleMouseLeave = () => {
    if (prefetchTimeoutRef.current) {
      clearTimeout(prefetchTimeoutRef.current);
    }
  };

  /**
   * Intersection Observer for viewport prefetching
   */
  useEffect(() => {
    if (prefetch !== 'viewport' || !linkRef.current) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !isInViewport) {
            setIsInViewport(true);
            prefetchRoute();
          }
        });
      },
      {
        rootMargin: '100px', // Start prefetching 100px before entering viewport
        threshold: 0.1,
      }
    );

    observer.observe(linkRef.current);

    return () => {
      observer.disconnect();
    };
  }, [prefetch, isInViewport, prefetchRoute]);

  /**
   * Cleanup timeout on unmount
   */
  useEffect(() => {
    return () => {
      if (prefetchTimeoutRef.current) {
        clearTimeout(prefetchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <Link
      ref={linkRef}
      href={href}
      locale={targetLocale}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onClick={handleClick}
      {...props}
    >
      {children}
    </Link>
  );
}

/**
 * Language Switcher Link Component
 */
interface LanguageSwitcherLinkProps {
  locale: Locale;
  children: React.ReactNode;
  className?: string;
}

export function LanguageSwitcherLink({
  locale,
  children,
  className,
}: LanguageSwitcherLinkProps) {
  const currentLocale = useLocale() as Locale;
  const { recordRedirect } = useRoutePerformance();

  const handleClick = () => {
    if (locale !== currentLocale) {
      recordRedirect(
        `/${currentLocale}${window.location.pathname.replace(`/${currentLocale}`, '')}`,
        `/${locale}${window.location.pathname.replace(`/${currentLocale}`, '')}`,
        'locale-detection'
      );
    }
  };

  return (
    <Link href='/' locale={locale} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
}

/**
 * Navigation Menu Link Component
 */
interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  activeClassName?: string;
}

export function NavLink({
  href,
  children,
  className = '',
  activeClassName = '',
}: NavLinkProps) {
  const currentLocale = useLocale() as Locale;
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    // Check if current path matches the link
    const currentPath =
      window.location.pathname.replace(`/${currentLocale}`, '') || '/';
    setIsActive(
      currentPath === href || (href !== '/' && currentPath.startsWith(href))
    );
  }, [href, currentLocale]);

  return (
    <OptimizedLink
      href={href}
      className={`${className} ${isActive ? activeClassName : ''}`}
      prefetch='hover'
      trackPerformance={true}
    >
      {children}
    </OptimizedLink>
  );
}

/**
 * Breadcrumb Link Component
 */
interface BreadcrumbLinkProps {
  href: string;
  children: React.ReactNode;
  isLast?: boolean;
  className?: string;
}

export function BreadcrumbLink({
  href,
  children,
  isLast = false,
  className = '',
}: BreadcrumbLinkProps) {
  if (isLast) {
    return (
      <span className={`${className} text-gray-500 dark:text-gray-400`}>
        {children}
      </span>
    );
  }

  return (
    <OptimizedLink
      href={href}
      className={`${className} text-blue-600 dark:text-blue-400 hover:underline`}
      prefetch='hover'
      trackPerformance={true}
    >
      {children}
    </OptimizedLink>
  );
}

export default OptimizedLink;
