import { createNavigation } from 'next-intl/navigation';
import { routing } from './routing';

/**
 * Internationalized Navigation APIs for Tucsenberg Web Stable
 *
 * These are lightweight wrappers around Next.js' navigation APIs
 * that automatically consider the routing configuration and locale.
 *
 * Usage:
 * - Use these instead of Next.js native navigation APIs
 * - Automatically handles locale prefixes
 * - Type-safe navigation with locale awareness
 */

export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);
