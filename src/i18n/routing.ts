import { defineRouting } from 'next-intl/routing';

/**
 * Optimized Internationalization Routing Configuration for Tucsenberg Web Stable
 *
 * This configuration defines the core routing setup for next-intl with performance optimizations:
 * - Efficient locale detection and routing
 * - Reduced redirect chains
 * - SEO-friendly URL structure
 * - Browser language preference support
 *
 * Following stable tech stack requirements:
 * - English (en) as source language
 * - Chinese (zh) as auxiliary language
 * - Always prefix locales in URLs for clarity and SEO
 */

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'zh'],

  // Used when no locale matches
  defaultLocale: 'en',

  // Use 'always' for stable routing with optimized root path handling
  // Root path (/) will be handled by middleware for better user experience
  // All locales use prefixes: /en/about, /zh/about
  localePrefix: 'always',

  // Performance optimization: Define alternate links for SEO
  alternateLinks: true,

  // Performance optimization: Disable locale detection in routing
  // We handle this in middleware for better control
  localeDetection: false,
});

// Export types for use throughout the application
export type Locale = (typeof routing.locales)[number];

/**
 * Locale configuration with metadata for optimization
 */
export const localeConfig = {
  en: {
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
    region: 'US',
    currency: 'USD',
    dateFormat: 'MM/dd/yyyy',
    timeFormat: '12h',
  },
  zh: {
    name: 'Chinese',
    nativeName: '中文',
    direction: 'ltr',
    region: 'CN',
    currency: 'CNY',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: '24h',
  },
} as const;

/**
 * Performance: Precomputed locale paths for common routes
 */
export const commonLocalePaths = {
  home: {
    en: '/en',
    zh: '/zh',
  },
  about: {
    en: '/en/about',
    zh: '/zh/about',
  },
  blog: {
    en: '/en/blog',
    zh: '/zh/blog',
  },
  contact: {
    en: '/en/contact',
    zh: '/zh/contact',
  },
  products: {
    en: '/en/products',
    zh: '/zh/products',
  },
} as const;

/**
 * Get localized path for a route
 */
export function getLocalizedPath(
  route: keyof typeof commonLocalePaths,
  locale: Locale
): string {
  return commonLocalePaths[route][locale];
}

/**
 * Check if a locale is supported
 */
export function isSupportedLocale(locale: string): locale is Locale {
  return routing.locales.includes(locale as Locale);
}
