import { hasLocale, IntlErrorCode } from 'next-intl';
import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

/**
 * Server-side Internationalization Configuration for Tucsenberg Web Stable
 *
 * This configuration is used by Server Components to provide
 * request-specific i18n configuration including messages.
 *
 * Following stable tech stack requirements with next-intl integration
 */

// Define formats for reuse and type safety
export const formats = {
  dateTime: {
    short: {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    } as const,
    long: {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      weekday: 'long',
    } as const,
    medium: {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    } as const,
  },
  number: {
    precise: {
      maximumFractionDigits: 5,
    } as const,
    currency: {
      style: 'currency',
      currency: 'USD',
    } as const,
    percent: {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    } as const,
  },
  list: {
    enumeration: {
      style: 'long',
      type: 'conjunction',
    } as const,
  },
} as const;

export default getRequestConfig(async ({ requestLocale }) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
    timeZone: 'UTC',
    now: new Date(),
    formats,
    onError(error) {
      if (error.code === IntlErrorCode.MISSING_MESSAGE) {
        // Missing translations are expected and should only log an error
        console.error('Missing translation:', error.message);
      } else {
        // Other errors indicate a bug in the app and should be reported
        console.error('Internationalization error:', error);
      }
    },
    getMessageFallback({ namespace, key, error }) {
      const path = [namespace, key].filter(part => part != null).join('.');

      if (error.code === IntlErrorCode.MISSING_MESSAGE) {
        return `${path} (translation missing)`;
      } else {
        return `${path} (translation error)`;
      }
    },
  };
});
