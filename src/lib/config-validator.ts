/**
 * Configuration Validator
 *
 * Provides utilities for validating configuration across different environments
 * and ensuring all required settings are properly configured.
 */

import { config, validateConfig } from './config';
import { isDevelopment, isProduction, isTest } from './env';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  environment: string;
}

/**
 * Comprehensive configuration validation
 */
export function validateFullConfiguration(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const environment = config.app.environment;

  try {
    // Run basic environment validation
    validateConfig();

    // Environment-specific validations
    if (isProduction()) {
      validateProductionConfig(errors, warnings);
    } else if (isDevelopment()) {
      validateDevelopmentConfig(errors, warnings);
    } else if (isTest()) {
      validateTestConfig(errors, warnings);
    }

    // General validations
    validateGeneralConfig(errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      environment,
    };
  } catch (error) {
    errors.push(
      error instanceof Error ? error.message : 'Unknown validation error'
    );
    return {
      isValid: false,
      errors,
      warnings,
      environment,
    };
  }
}

/**
 * Production environment validation
 */
function validateProductionConfig(errors: string[], warnings: string[]): void {
  // Required for production
  if (!config.auth.secret) {
    errors.push('NEXTAUTH_SECRET is required in production');
  }

  if (!config.database.url) {
    errors.push('DATABASE_URL is required in production');
  }

  if (!config.security.encryptionKey) {
    errors.push('ENCRYPTION_KEY is required in production');
  }

  // Recommended for production
  if (
    !config.analytics.vercelAnalyticsId &&
    !config.analytics.googleAnalyticsId
  ) {
    warnings.push('Analytics configuration is recommended for production');
  }

  if (!config.email.smtp.host) {
    warnings.push('Email service configuration is recommended for production');
  }

  // Security checks
  if (config.dev.showDebugInfo) {
    errors.push('Debug info should be disabled in production');
  }

  if (config.dev.enableDevtools) {
    errors.push('Dev tools should be disabled in production');
  }
}

/**
 * Development environment validation
 */
function validateDevelopmentConfig(
  _errors: string[],
  warnings: string[]
): void {
  // Development-specific checks
  if (!config.dev.showDebugInfo) {
    warnings.push('Debug info is recommended for development');
  }

  if (config.features.analytics && !config.analytics.vercelAnalyticsId) {
    warnings.push('Analytics is enabled but no analytics ID is configured');
  }
}

/**
 * Test environment validation
 */
function validateTestConfig(_errors: string[], warnings: string[]): void {
  // Test-specific checks
  if (config.features.analytics) {
    warnings.push('Analytics should typically be disabled in test environment');
  }

  if (!config.database.url) {
    warnings.push('Test database URL is recommended for comprehensive testing');
  }
}

/**
 * General configuration validation
 */
function validateGeneralConfig(errors: string[], warnings: string[]): void {
  // URL validation
  try {
    new URL(config.app.url);
  } catch {
    errors.push(`Invalid APP_URL: ${config.app.url}`);
  }

  // Email configuration validation
  if (config.email.smtp.host) {
    if (!config.email.smtp.user || !config.email.smtp.password) {
      errors.push(
        'SMTP_USER and SMTP_PASSWORD are required when SMTP_HOST is configured'
      );
    }

    if (!config.email.smtp.from) {
      warnings.push(
        'SMTP_FROM is recommended when email service is configured'
      );
    }
  }

  // API key validation
  if (config.api.stripe.secretKey && !config.api.stripe.publishableKey) {
    warnings.push(
      'STRIPE_PUBLISHABLE_KEY is recommended when STRIPE_SECRET_KEY is configured'
    );
  }
}

/**
 * Configuration health check
 */
export function getConfigurationHealth(): {
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details: ValidationResult;
} {
  const validation = validateFullConfiguration();

  if (!validation.isValid) {
    return {
      status: 'error',
      message: `Configuration validation failed with ${validation.errors.length} error(s)`,
      details: validation,
    };
  }

  if (validation.warnings.length > 0) {
    return {
      status: 'warning',
      message: `Configuration has ${validation.warnings.length} warning(s)`,
      details: validation,
    };
  }

  return {
    status: 'healthy',
    message: 'Configuration is valid and healthy',
    details: validation,
  };
}

/**
 * Print configuration status to console
 */
export function printConfigurationStatus(): void {
  const health = getConfigurationHealth();
  const { details } = health;

  console.log(`\n🔧 Configuration Status: ${health.status.toUpperCase()}`);
  console.log(`📍 Environment: ${details.environment}`);
  console.log(`🌐 App URL: ${config.app.url}`);
  console.log(`📱 App Name: ${config.app.name}`);

  if (details.errors.length > 0) {
    console.log('\n❌ Errors:');
    details.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }

  if (details.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    details.warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. ${warning}`);
    });
  }

  if (details.errors.length === 0 && details.warnings.length === 0) {
    console.log('\n✅ All configuration checks passed!');
  }

  console.log('');
}

/**
 * Initialize configuration with validation
 */
export function initializeConfiguration(): boolean {
  try {
    const health = getConfigurationHealth();

    if (isDevelopment()) {
      printConfigurationStatus();
    }

    if (health.status === 'error') {
      throw new Error(`Configuration initialization failed: ${health.message}`);
    }

    return true;
  } catch (error) {
    console.error('❌ Configuration initialization failed:', error);
    return false;
  }
}
