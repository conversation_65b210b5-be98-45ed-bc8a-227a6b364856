/**
 * API Utility Functions
 *
 * Centralized utilities for API request handling, response formatting,
 * error handling, and common API operations.
 */

import {
  API_ERROR_CODES,
  ApiErrorCode,
  ApiResponse,
  PaginatedResponse,
  RateLimitInfo,
  ValidationError,
} from '@/types/api';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { config } from './config';
import { validationHelpers } from './validations';

/**
 * Create a standardized API response
 */
export function createApiResponse<T>(
  data: T,
  options: {
    status?: number;
    headers?: Record<string, string>;
    meta?: Record<string, unknown>;
  } = {}
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version,
      ...options.meta,
    },
  };

  return NextResponse.json(response, {
    status: options.status || 200,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Version': config.app.version,
      ...options.headers,
    },
  });
}

/**
 * Create a standardized API error response
 */
export function createApiError(
  code: ApiErrorCode,
  message: string,
  options: {
    status?: number;
    details?: Record<string, unknown>;
    headers?: Record<string, string>;
  } = {}
): NextResponse<ApiResponse> {
  const statusCode = options.status || getStatusCodeForError(code);

  const response: ApiResponse = {
    success: false,
    error: {
      code,
      message,
      ...(options.details && { details: options.details }),
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version,
    },
  };

  return NextResponse.json(response, {
    status: statusCode,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Version': config.app.version,
      ...options.headers,
    },
  });
}

/**
 * Create a paginated API response
 */
export function createPaginatedResponse<T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  options: {
    status?: number;
    headers?: Record<string, string>;
    meta?: Record<string, unknown>;
  } = {}
): NextResponse<PaginatedResponse<T>> {
  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const response: PaginatedResponse<T> = {
    success: true,
    data,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1,
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version,
      ...options.meta,
    },
  };

  return NextResponse.json(response, {
    status: options.status || 200,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Version': config.app.version,
      ...options.headers,
    },
  });
}

/**
 * Validation error response
 */
export function createValidationError(
  error: z.ZodError,
  options: {
    headers?: Record<string, string>;
  } = {}
): NextResponse<ApiResponse> {
  const validationError: ValidationError = {
    code: 'VALIDATION_ERROR',
    message: 'Validation failed',
    details: validationHelpers.formatValidationErrors(error),
  };

  const response: ApiResponse = {
    success: false,
    error: {
      code: validationError.code,
      message: validationError.message,
      details: { validationErrors: validationError.details },
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version,
    },
  };

  return NextResponse.json(response, {
    status: 400,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Version': config.app.version,
      ...options.headers,
    },
  });
}

/**
 * Get HTTP status code for error type
 */
function getStatusCodeForError(code: ApiErrorCode): number {
  const statusMap: Record<ApiErrorCode, number> = {
    [API_ERROR_CODES.BAD_REQUEST]: 400,
    [API_ERROR_CODES.UNAUTHORIZED]: 401,
    [API_ERROR_CODES.FORBIDDEN]: 403,
    [API_ERROR_CODES.NOT_FOUND]: 404,
    [API_ERROR_CODES.METHOD_NOT_ALLOWED]: 405,
    [API_ERROR_CODES.VALIDATION_ERROR]: 400,
    [API_ERROR_CODES.RATE_LIMITED]: 429,
    [API_ERROR_CODES.INTERNAL_ERROR]: 500,
    [API_ERROR_CODES.SERVICE_UNAVAILABLE]: 503,
    [API_ERROR_CODES.DATABASE_ERROR]: 500,
    [API_ERROR_CODES.EXTERNAL_SERVICE_ERROR]: 502,
    [API_ERROR_CODES.CONFIGURATION_ERROR]: 500,
    [API_ERROR_CODES.FEATURE_DISABLED]: 501,
  };

  return statusMap[code] || 500;
}

/**
 * Parse request body with validation
 */
export async function parseRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<
  { success: true; data: T } | { success: false; error: NextResponse }
> {
  try {
    const body = await request.json();
    const result = validationHelpers.validateData(schema, body);

    if (!result.success) {
      return {
        success: false,
        error: createValidationError(result.errors),
      };
    }

    return { success: true, data: result.data };
  } catch (error) {
    return {
      success: false,
      error: createApiError(
        API_ERROR_CODES.BAD_REQUEST,
        'Invalid JSON in request body'
      ),
    };
  }
}

/**
 * Parse query parameters with validation
 */
export function parseQueryParams<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; error: NextResponse } {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());

    const result = validationHelpers.validateData(schema, params);

    if (!result.success) {
      return {
        success: false,
        error: createValidationError(result.errors),
      };
    }

    return { success: true, data: result.data };
  } catch (error) {
    return {
      success: false,
      error: createApiError(
        API_ERROR_CODES.BAD_REQUEST,
        'Invalid query parameters'
      ),
    };
  }
}

/**
 * Handle API route errors
 */
export function handleApiError(error: unknown): NextResponse<ApiResponse> {
  console.error('API Error:', error);

  if (error instanceof z.ZodError) {
    return createValidationError(error);
  }

  if (error instanceof Error) {
    // Check for specific error types
    if (error.message.includes('ECONNREFUSED')) {
      return createApiError(
        API_ERROR_CODES.SERVICE_UNAVAILABLE,
        'External service unavailable'
      );
    }

    if (error.message.includes('timeout')) {
      return createApiError(
        API_ERROR_CODES.SERVICE_UNAVAILABLE,
        'Request timeout'
      );
    }

    return createApiError(
      API_ERROR_CODES.INTERNAL_ERROR,
      config.app.environment === 'development'
        ? error.message
        : 'Internal server error'
    );
  }

  return createApiError(
    API_ERROR_CODES.INTERNAL_ERROR,
    'An unexpected error occurred'
  );
}

/**
 * Rate limiting utilities
 */
export const rateLimiting = {
  /**
   * Check rate limit for a client
   */
  checkRateLimit: async (
    _clientId: string,
    limit: number = config.api.rateLimit,
    windowMs: number = 15 * 60 * 1000 // 15 minutes
  ): Promise<{ allowed: boolean; info: RateLimitInfo }> => {
    // This is a simple in-memory implementation
    // In production, you'd use Redis or a similar store
    const now = Date.now();

    // For now, return allowed (implement actual rate limiting later)
    return {
      allowed: true,
      info: {
        limit,
        remaining: limit - 1,
        reset: now + windowMs,
      },
    };
  },

  /**
   * Add rate limit headers to response
   */
  addRateLimitHeaders: (
    response: NextResponse,
    info: RateLimitInfo
  ): NextResponse => {
    response.headers.set('X-RateLimit-Limit', info.limit.toString());
    response.headers.set('X-RateLimit-Remaining', info.remaining.toString());
    response.headers.set('X-RateLimit-Reset', info.reset.toString());

    if (info.retryAfter) {
      response.headers.set('Retry-After', info.retryAfter.toString());
    }

    return response;
  },
};

/**
 * CORS utilities
 */
export const cors = {
  /**
   * Add CORS headers to response
   */
  addCorsHeaders: (response: NextResponse, origin?: string): NextResponse => {
    const allowedOrigins = config.security.corsOrigins;
    const requestOrigin = origin || '*';

    if (
      allowedOrigins.includes(requestOrigin) ||
      allowedOrigins.includes('*')
    ) {
      response.headers.set('Access-Control-Allow-Origin', requestOrigin);
    }

    response.headers.set(
      'Access-Control-Allow-Methods',
      'GET, POST, PUT, DELETE, OPTIONS'
    );
    response.headers.set(
      'Access-Control-Allow-Headers',
      'Content-Type, Authorization, X-Requested-With'
    );
    response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours

    return response;
  },

  /**
   * Handle preflight OPTIONS request
   */
  handlePreflight: (request: NextRequest): NextResponse => {
    const response = new NextResponse(null, { status: 200 });
    return cors.addCorsHeaders(
      response,
      request.headers.get('origin') || undefined
    );
  },
};

/**
 * Request ID utilities
 */
export const requestId = {
  /**
   * Generate a unique request ID
   */
  generate: (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Get request ID from headers or generate new one
   */
  getOrGenerate: (request: NextRequest): string => {
    return request.headers.get('X-Request-ID') || requestId.generate();
  },

  /**
   * Add request ID to response headers
   */
  addToResponse: (response: NextResponse, id: string): NextResponse => {
    response.headers.set('X-Request-ID', id);
    return response;
  },
};
