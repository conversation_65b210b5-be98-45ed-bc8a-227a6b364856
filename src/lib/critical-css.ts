/**
 * Critical CSS optimization utilities
 *
 * This module provides utilities for optimizing CSS loading and rendering performance
 * by identifying and inlining critical CSS for above-the-fold content.
 */

/**
 * Critical CSS for above-the-fold content
 * These styles are essential for initial page render and should be inlined
 */
export const criticalCSS = `
  /* Reset and base styles */
  *,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
  ::before,::after{--tw-content:''}
  html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
  body{margin:0;line-height:inherit}
  
  /* Critical layout styles */
  .flex{display:flex}
  .min-h-screen{min-height:100vh}
  .flex-col{flex-direction:column}
  .items-center{align-items:center}
  .justify-between{justify-content:space-between}
  .justify-center{justify-content:center}
  .p-24{padding:6rem}
  .text-center{text-align:center}
  .text-4xl{font-size:2.25rem;line-height:2.5rem}
  .font-bold{font-weight:700}
  .mb-4{margin-bottom:1rem}
  .text-lg{font-size:1.125rem;line-height:1.75rem}
  
  /* Critical color and theme styles */
  .text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity))}
  .dark .dark\\:text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity))}
  
  /* Critical responsive styles */
  @media (min-width: 1024px) {
    .lg\\:flex{display:flex}
    .lg\\:w-auto{width:auto}
    .lg\\:static{position:static}
    .lg\\:rounded-xl{border-radius:0.75rem}
    .lg\\:border{border-width:1px}
    .lg\\:bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity))}
    .lg\\:p-4{padding:1rem}
    .lg\\:dark\\:bg-zinc-800\\/30{background-color:rgb(39 39 42 / 0.3)}
  }
  
  /* Critical gradient and backdrop styles */
  .bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}
  .from-zinc-200{--tw-gradient-from:#e4e4e7 var(--tw-gradient-from-position);--tw-gradient-to:rgb(228 228 231 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}
  .backdrop-blur-2xl{--tw-backdrop-blur:blur(40px);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}
`;

/**
 * Get critical CSS for a specific page
 */
export function getCriticalCSS(
  page: 'home' | 'about' | 'blog' | 'contact' | 'products'
): string {
  let pageCriticalCSS = criticalCSS;

  switch (page) {
    case 'home':
      pageCriticalCSS += `
        /* Home page specific critical styles */
        .relative{position:relative}
        .absolute{position:absolute}
        .z-10{z-index:10}
        .-z-20{z-index:-20}
        .h-\\[300px\\]{height:300px}
        .h-\\[180px\\]{height:180px}
        .w-full{width:100%}
        .max-w-5xl{max-width:64rem}
        .-translate-x-1\\/2{--tw-translate-x:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
        .translate-x-1\\/3{--tw-translate-x:33.333333%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
        .rounded-full{border-radius:9999px}
        .opacity-60{opacity:0.6}
        .opacity-40{opacity:0.4}
        .blur-2xl{--tw-blur:blur(40px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}
        .will-change-transform{will-change:transform}
      `;
      break;
    case 'about':
      pageCriticalCSS += `
        /* About page specific critical styles */
        .container{width:100%}
        .mx-auto{margin-left:auto;margin-right:auto}
        .px-4{padding-left:1rem;padding-right:1rem}
        .py-16{padding-top:4rem;padding-bottom:4rem}
      `;
      break;
    // Add other page-specific critical CSS as needed
  }

  return pageCriticalCSS;
}

/**
 * Generate critical CSS link tag for non-critical CSS
 */
export function generateNonCriticalCSSLink(): string {
  return `
    <link
      rel="preload"
      href="/_next/static/css/app.css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript>
      <link rel="stylesheet" href="/_next/static/css/app.css" />
    </noscript>
  `;
}

/**
 * Font loading optimization
 */
export const fontOptimizationCSS = `
  /* Font display optimization */
  @font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('/_next/static/media/inter-latin.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  
  /* Prevent layout shift during font loading */
  .font-inter {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  }
`;
