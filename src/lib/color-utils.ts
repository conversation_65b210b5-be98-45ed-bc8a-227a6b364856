/**
 * OKLCH Color Utilities
 *
 * Utilities for working with OKLCH color space in the design system.
 * Provides type-safe color manipulation and validation functions.
 */

export type OKLCHColor = {
  lightness: number; // 0-100
  chroma: number; // 0-0.4+
  hue: number; // 0-360
};

/**
 * Convert OKLCH values to CSS oklch() string
 */
export function oklch(lightness: number, chroma: number, hue: number): string {
  return `oklch(${lightness}% ${chroma} ${hue})`;
}

/**
 * Create a lighter variant of an OKLCH color
 */
export function lighten(color: OKLCHColor, amount: number): OKLCHColor {
  return {
    ...color,
    lightness: Math.min(100, color.lightness + amount),
  };
}

/**
 * Create a darker variant of an OKLCH color
 */
export function darken(color: OKLCHColor, amount: number): OKLCHColor {
  return {
    ...color,
    lightness: Math.max(0, color.lightness - amount),
  };
}

/**
 * Adjust chroma (color intensity) of an OKLCH color
 */
export function adjustChroma(color: OKLCHColor, amount: number): OKLCHColor {
  return {
    ...color,
    chroma: Math.max(0, color.chroma + amount),
  };
}

/**
 * Rotate hue of an OKLCH color
 */
export function rotateHue(color: OKLCHColor, degrees: number): OKLCHColor {
  return {
    ...color,
    hue: (color.hue + degrees) % 360,
  };
}

/**
 * Validate OKLCH color values
 */
export function isValidOKLCH(color: OKLCHColor): boolean {
  return (
    color.lightness >= 0 &&
    color.lightness <= 100 &&
    color.chroma >= 0 &&
    color.hue >= 0 &&
    color.hue <= 360
  );
}

/**
 * Design system color palette using OKLCH
 */
export const colorPalette = {
  // Primary colors
  primary: { lightness: 47.2, chroma: 0.13, hue: 264 },
  primaryForeground: { lightness: 98.5, chroma: 0, hue: 0 },

  // Secondary colors
  secondary: { lightness: 96.1, chroma: 0.01, hue: 264 },
  secondaryForeground: { lightness: 44.8, chroma: 0.1, hue: 264 },

  // Semantic colors
  destructive: { lightness: 64.8, chroma: 0.15, hue: 28 },
  success: { lightness: 65, chroma: 0.12, hue: 142 },
  warning: { lightness: 75, chroma: 0.15, hue: 85 },
  info: { lightness: 65, chroma: 0.12, hue: 220 },
} as const;

/**
 * Generate color variants for a given base color
 */
export function generateColorVariants(baseColor: OKLCHColor) {
  return {
    50: lighten(baseColor, 45),
    100: lighten(baseColor, 40),
    200: lighten(baseColor, 30),
    300: lighten(baseColor, 20),
    400: lighten(baseColor, 10),
    500: baseColor,
    600: darken(baseColor, 10),
    700: darken(baseColor, 20),
    800: darken(baseColor, 30),
    900: darken(baseColor, 40),
    950: darken(baseColor, 45),
  };
}
