/**
 * Data Validation Schemas
 *
 * Centralized Zod validation schemas for API requests,
 * form data, and other data structures throughout the application.
 */

import {
  contentStatusSchema,
  localeSchema,
  statusSchema,
  userRoleSchema,
} from '@/types/common';
import { z } from 'zod';

/**
 * Common Validation Patterns
 */
export const commonValidations = {
  // ID validations
  uuid: z.string().uuid('Invalid UUID format'),
  objectId: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'),

  // String validations
  nonEmptyString: z.string().min(1, 'This field is required'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .max(100, 'Slug must be less than 100 characters')
    .regex(
      /^[a-z0-9-]+$/,
      'Slug can only contain lowercase letters, numbers, and hyphens'
    ),

  // Email validation
  email: z
    .string()
    .email('Invalid email format')
    .max(254, 'Email must be less than 254 characters'),

  // URL validation
  url: z.string().url('Invalid URL format'),

  // Phone validation
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format'),

  // Password validation
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),

  // Date validations
  dateString: z.string().datetime('Invalid date format'),
  futureDate: z
    .string()
    .datetime()
    .refine(date => new Date(date) > new Date(), 'Date must be in the future'),
  pastDate: z
    .string()
    .datetime()
    .refine(date => new Date(date) < new Date(), 'Date must be in the past'),
};

/**
 * API Request Validations
 */
export const apiValidations = {
  // Pagination
  pagination: z.object({
    page: z.coerce.number().int().min(1).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(20),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).default('asc'),
  }),

  // Search
  search: z.object({
    q: z.string().min(1).max(100),
    fields: z.array(z.string()).optional(),
    filters: z.record(z.string()).optional(),
  }),

  // File upload
  fileUpload: z.object({
    file: z.instanceof(File),
    type: z.enum(['image', 'document', 'video', 'audio']),
    maxSize: z.number().default(10 * 1024 * 1024), // 10MB
    allowedTypes: z.array(z.string()).optional(),
  }),
};

/**
 * User Validations
 */
export const userValidations = {
  // User creation
  createUser: z.object({
    email: commonValidations.email,
    name: z.string().min(1).max(100),
    password: commonValidations.password,
    role: userRoleSchema.default('viewer'),
  }),

  // User update
  updateUser: z.object({
    name: z.string().min(1).max(100).optional(),
    avatar: commonValidations.url.optional(),
    role: userRoleSchema.optional(),
    status: statusSchema.optional(),
  }),

  // User preferences
  userPreferences: z.object({
    locale: localeSchema,
    theme: z.enum(['light', 'dark', 'system']),
    timezone: z.string(),
    notifications: z.object({
      email: z.boolean(),
      push: z.boolean(),
      sms: z.boolean(),
    }),
  }),

  // Login
  login: z.object({
    email: commonValidations.email,
    password: z.string().min(1, 'Password is required'),
    rememberMe: z.boolean().default(false),
  }),

  // Password reset
  passwordReset: z.object({
    email: commonValidations.email,
  }),

  // Password change
  passwordChange: z
    .object({
      currentPassword: z.string().min(1, 'Current password is required'),
      newPassword: commonValidations.password,
      confirmPassword: z.string(),
    })
    .refine(data => data.newPassword === data.confirmPassword, {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    }),
};

/**
 * Content Validations
 */
export const contentValidations = {
  // Content creation
  createContent: z.object({
    title: z.string().min(1).max(200),
    slug: commonValidations.slug,
    excerpt: z.string().max(500).optional(),
    content: z.string().min(1),
    status: contentStatusSchema.default('draft'),
    tags: z.array(z.string()).default([]),
    publishedAt: z.string().datetime().optional(),
    metadata: z
      .record(z.union([z.string(), z.number(), z.boolean()]))
      .optional(),
  }),

  // Content update
  updateContent: z.object({
    title: z.string().min(1).max(200).optional(),
    slug: commonValidations.slug.optional(),
    excerpt: z.string().max(500).optional(),
    content: z.string().min(1).optional(),
    status: contentStatusSchema.optional(),
    tags: z.array(z.string()).optional(),
    publishedAt: z.string().datetime().optional(),
    metadata: z
      .record(z.union([z.string(), z.number(), z.boolean()]))
      .optional(),
  }),
};

/**
 * Contact Form Validations
 */
export const contactValidations = {
  contactForm: z.object({
    name: z.string().min(1).max(100),
    email: commonValidations.email,
    subject: z.string().min(1).max(200),
    message: z.string().min(10).max(2000),
    phone: commonValidations.phone.optional(),
    company: z.string().max(100).optional(),
  }),
};

/**
 * Settings Validations
 */
export const settingsValidations = {
  // General settings
  generalSettings: z.object({
    siteName: z.string().min(1).max(100),
    siteDescription: z.string().max(500),
    siteUrl: commonValidations.url,
    adminEmail: commonValidations.email,
    timezone: z.string(),
    locale: localeSchema,
  }),

  // API settings
  apiSettings: z.object({
    rateLimit: z.number().int().min(1).max(10000),
    timeout: z.number().int().min(1000).max(300000), // 1s to 5min
    retryAttempts: z.number().int().min(0).max(10),
    enableCaching: z.boolean(),
    cacheTimeout: z.number().int().min(0).max(86400), // 0 to 24 hours
  }),

  // Security settings
  securitySettings: z.object({
    enableTwoFactor: z.boolean(),
    sessionTimeout: z.number().int().min(300).max(86400), // 5min to 24 hours
    passwordExpiry: z.number().int().min(0).max(365), // 0 to 1 year
    maxLoginAttempts: z.number().int().min(1).max(20),
    enableAuditLog: z.boolean(),
  }),
};

/**
 * Webhook Validations
 */
export const webhookValidations = {
  webhookPayload: z.object({
    id: commonValidations.uuid,
    event: z.string().min(1),
    timestamp: commonValidations.dateString,
    data: z.record(z.any()),
    signature: z.string().optional(),
  }),

  webhookConfig: z.object({
    url: commonValidations.url,
    events: z.array(z.string()).min(1),
    secret: z.string().min(1),
    active: z.boolean().default(true),
  }),
};

/**
 * Batch Operation Validations
 */
export const batchValidations = {
  batchRequest: z.object({
    operations: z
      .array(
        z.object({
          id: z.string(),
          method: z.enum(['CREATE', 'UPDATE', 'DELETE']),
          data: z.record(z.any()),
        })
      )
      .min(1)
      .max(100), // Limit batch size
  }),
};

/**
 * Health Check Validations
 */
export const healthValidations = {
  healthCheck: z.object({
    component: z.string().optional(),
    detailed: z.boolean().default(false),
  }),
};

/**
 * Validation Helper Functions
 */
export const validationHelpers = {
  /**
   * Validate and parse data with a schema
   */
  validateData: <T>(
    schema: z.ZodSchema<T>,
    data: unknown
  ): { success: true; data: T } | { success: false; errors: z.ZodError } => {
    const result = schema.safeParse(data);
    if (result.success) {
      return { success: true, data: result.data };
    }
    return { success: false, errors: result.error };
  },

  /**
   * Format validation errors for API responses
   */
  formatValidationErrors: (error: z.ZodError) => {
    return error.errors.map(err => ({
      field: err.path.join('.'),
      code: err.code,
      message: err.message,
      value: 'received' in err ? err.received : undefined,
    }));
  },

  /**
   * Create a validation middleware for API routes
   */
  createValidationMiddleware: <T>(schema: z.ZodSchema<T>) => {
    return (data: unknown) => {
      const result = validationHelpers.validateData(schema, data);
      if (!result.success) {
        throw new Error(
          `Validation failed: ${JSON.stringify(validationHelpers.formatValidationErrors(result.errors))}`
        );
      }
      return result.data;
    };
  },
};
