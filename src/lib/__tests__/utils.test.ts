import { cn } from '../utils';

describe('cn utility function', () => {
  it('should merge classes correctly', () => {
    expect(cn('px-2 py-1', 'px-3')).toBe('py-1 px-3');
  });

  it('should handle conditional classes', () => {
    expect(cn('text-red-500', true && 'text-blue-500')).toBe('text-blue-500');
    expect(cn('text-red-500', false && 'text-blue-500')).toBe('text-red-500');
  });

  it('should handle empty inputs', () => {
    expect(cn()).toBe('');
    expect(cn('')).toBe('');
    expect(cn(null, undefined)).toBe('');
  });

  it('should handle complex class merging', () => {
    expect(
      cn('px-2 py-1 bg-red-500', 'px-3 bg-blue-500', 'hover:bg-green-500')
    ).toBe('py-1 px-3 bg-blue-500 hover:bg-green-500');
  });

  it('should handle object syntax', () => {
    expect(
      cn({
        'text-red-500': true,
        'text-blue-500': false,
        'font-bold': true,
      })
    ).toBe('text-red-500 font-bold');
  });

  it('should handle array syntax', () => {
    expect(cn(['px-2', 'py-1'], ['px-3', 'bg-blue-500'])).toBe(
      'py-1 px-3 bg-blue-500'
    );
  });
});
