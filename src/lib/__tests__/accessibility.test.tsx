import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

// Simple test component for accessibility testing
const TestButton = ({
  children,
  ...props
}: {
  children: React.ReactNode;
  [key: string]: any;
}) => <button {...props}>{children}</button>;

// Removed unused component - TestButtonWithoutLabel

const TestButtonWithAriaLabel = () => (
  <button aria-label='Submit form'>Submit</button>
);

describe('Accessibility Tests', () => {
  it('should not have accessibility violations for button with text', async () => {
    const { container } = render(<TestButton>Click me</TestButton>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations for button with aria-label', async () => {
    const { container } = render(<TestButtonWithAriaLabel />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should detect accessibility violations for button without accessible name', async () => {
    const { container } = render(<button></button>);
    const results = await axe(container);
    // This should have violations because button has no accessible name
    expect(results.violations.length).toBeGreaterThan(0);
  });

  it('should pass for properly structured heading hierarchy', async () => {
    const { container } = render(
      <div>
        <h1>Main Title</h1>
        <h2>Section Title</h2>
        <h3>Subsection Title</h3>
      </div>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should pass for form with proper labels', async () => {
    const { container } = render(
      <form>
        <label htmlFor='email'>Email</label>
        <input type='email' id='email' name='email' />

        <label htmlFor='password'>Password</label>
        <input type='password' id='password' name='password' />

        <button type='submit'>Submit</button>
      </form>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
