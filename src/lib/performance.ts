/**
 * Performance Monitoring and Optimization Utilities
 *
 * Provides tools for monitoring Core Web Vitals, performance metrics,
 * and optimization helpers for the Next.js application.
 */

import { getCLS, getFCP, getFID, getLCP, getTTFB } from 'web-vitals';

// Type definitions for third-party analytics and browser APIs
declare global {
  interface Window {
    gtag?: (
      command: string,
      eventName: string,
      parameters?: Record<string, unknown>
    ) => void;
    va?: (
      command: string,
      eventName: string,
      parameters?: Record<string, unknown>
    ) => void;
  }

  interface Performance {
    memory?: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit: number;
    };
  }
}

export interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

/**
 * Core Web Vitals thresholds (2024 standards)
 */
const THRESHOLDS = {
  LCP: { good: 2500, poor: 4000 }, // Largest Contentful Paint
  FID: { good: 100, poor: 300 }, // First Input Delay
  CLS: { good: 0.1, poor: 0.25 }, // Cumulative Layout Shift
  FCP: { good: 1800, poor: 3000 }, // First Contentful Paint
  TTFB: { good: 800, poor: 1800 }, // Time to First Byte
};

/**
 * Get performance rating based on value and thresholds
 */
function getRating(
  name: string,
  value: number
): 'good' | 'needs-improvement' | 'poor' {
  const threshold = THRESHOLDS[name as keyof typeof THRESHOLDS];
  if (!threshold) return 'good';

  if (value <= threshold.good) return 'good';
  if (value <= threshold.poor) return 'needs-improvement';
  return 'poor';
}

/**
 * Send metric to analytics service
 */
function sendToAnalytics(metric: PerformanceMetric) {
  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 Performance Metric:', metric);
    return;
  }

  // Send to your analytics service
  // Example: Google Analytics 4
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      custom_map: { metric_rating: 'custom_parameter_1' },
      metric_rating: metric.rating,
      value: Math.round(metric.value),
    });
  }

  // Example: Vercel Analytics
  if (typeof window !== 'undefined' && window.va) {
    window.va('track', 'Web Vital', {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
    });
  }
}

/**
 * Initialize Core Web Vitals monitoring
 */
export function initPerformanceMonitoring() {
  if (typeof window === 'undefined') return;

  // Largest Contentful Paint
  getLCP(metric => {
    const performanceMetric: PerformanceMetric = {
      name: 'LCP',
      value: metric.value,
      rating: getRating('LCP', metric.value),
      timestamp: Date.now(),
    };
    sendToAnalytics(performanceMetric);
  });

  // First Input Delay
  getFID(metric => {
    const performanceMetric: PerformanceMetric = {
      name: 'FID',
      value: metric.value,
      rating: getRating('FID', metric.value),
      timestamp: Date.now(),
    };
    sendToAnalytics(performanceMetric);
  });

  // Cumulative Layout Shift
  getCLS(metric => {
    const performanceMetric: PerformanceMetric = {
      name: 'CLS',
      value: metric.value,
      rating: getRating('CLS', metric.value),
      timestamp: Date.now(),
    };
    sendToAnalytics(performanceMetric);
  });

  // First Contentful Paint
  getFCP(metric => {
    const performanceMetric: PerformanceMetric = {
      name: 'FCP',
      value: metric.value,
      rating: getRating('FCP', metric.value),
      timestamp: Date.now(),
    };
    sendToAnalytics(performanceMetric);
  });

  // Time to First Byte
  getTTFB(metric => {
    const performanceMetric: PerformanceMetric = {
      name: 'TTFB',
      value: metric.value,
      rating: getRating('TTFB', metric.value),
      timestamp: Date.now(),
    };
    sendToAnalytics(performanceMetric);
  });
}

/**
 * Performance optimization utilities
 */
export const performanceUtils = {
  /**
   * Preload critical resources
   */
  preloadResource(href: string, as: string, type?: string) {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  },

  /**
   * Prefetch next page resources
   */
  prefetchPage(href: string) {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  },

  /**
   * Lazy load images with Intersection Observer
   */
  lazyLoadImages(selector = 'img[data-src]') {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window))
      return;

    const images = document.querySelectorAll(selector);
    const imageObserver = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src || '';
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  },

  /**
   * Measure custom performance metrics
   */
  measureCustomMetric(name: string, startTime: number) {
    const duration = performance.now() - startTime;

    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }

    // Send to analytics
    const metric: PerformanceMetric = {
      name,
      value: duration,
      rating:
        duration < 100 ? 'good' : duration < 300 ? 'needs-improvement' : 'poor',
      timestamp: Date.now(),
    };
    sendToAnalytics(metric);

    return duration;
  },

  /**
   * Get current performance metrics
   */
  getCurrentMetrics() {
    if (typeof window === 'undefined') return null;

    const navigation = performance.getEntriesByType(
      'navigation'
    )[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');

    return {
      // Navigation timing
      domContentLoaded:
        navigation.domContentLoadedEventEnd -
        navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,

      // Paint timing
      firstPaint:
        paint.find(entry => entry.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint:
        paint.find(entry => entry.name === 'first-contentful-paint')
          ?.startTime || 0,

      // Resource timing
      totalResources: performance.getEntriesByType('resource').length,

      // Memory (if available)
      memory: performance.memory
        ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit,
          }
        : null,
    };
  },
};

/**
 * Performance monitoring hook for React components
 */
export function usePerformanceMonitoring(componentName: string) {
  if (typeof window === 'undefined') {
    return {
      measureRender: () => {
        // No-op function for server-side rendering
        return () => {
          // Performance measurement not available on server
        };
      },
    };
  }

  const measureRender = () => {
    const startTime = performance.now();

    return () => {
      performanceUtils.measureCustomMetric(
        `${componentName} Render`,
        startTime
      );
    };
  };

  return { measureRender };
}

/**
 * Bundle analyzer helper
 */
export function analyzeBundleSize() {
  if (typeof window === 'undefined') return;

  // Log bundle information in development
  if (process.env.NODE_ENV === 'development') {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const bundleScripts = scripts.filter(script => {
      const src = (script as HTMLScriptElement).src;
      return src.includes('/_next/static/');
    });

    console.log(
      `📦 Total scripts: ${scripts.length}, Bundle scripts: ${bundleScripts.length}`
    );
    console.log(`📊 Performance metrics available in DevTools`);
  }
}
