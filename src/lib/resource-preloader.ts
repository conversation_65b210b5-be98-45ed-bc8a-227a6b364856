/**
 * Resource Preloader Utilities
 *
 * This module provides utilities for optimizing resource loading performance
 * through strategic preloading, prefetching, and resource prioritization.
 */

export interface PreloadResource {
  href: string;
  as: 'style' | 'script' | 'font' | 'image' | 'fetch' | 'document';
  type?: string;
  crossOrigin?: 'anonymous' | 'use-credentials';
  media?: string;
  integrity?: string;
}

export interface PrefetchResource {
  href: string;
  as?: string;
  type?: string;
}

/**
 * Critical resources that should be preloaded for optimal performance
 */
export const criticalResources: PreloadResource[] = [
  // Critical fonts
  {
    href: '/_next/static/media/inter-latin.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },

  // Critical CSS (if using external CSS)
  {
    href: '/_next/static/css/app.css',
    as: 'style',
  },

  // Critical JavaScript chunks
  {
    href: '/_next/static/chunks/framework.js',
    as: 'script',
  },
];

/**
 * Resources to prefetch for improved navigation performance
 */
export const prefetchResources: PrefetchResource[] = [
  // Common page routes
  { href: '/about' },
  { href: '/blog' },
  { href: '/contact' },
  { href: '/products' },

  // API endpoints that might be needed
  { href: '/api/health', as: 'fetch' },
];

/**
 * Generate preload link tags
 */
export function generatePreloadTags(resources: PreloadResource[]): string {
  return resources
    .map(resource => {
      const attrs = [
        'rel="preload"',
        `href="${resource.href}"`,
        `as="${resource.as}"`,
      ];

      if (resource.type) attrs.push(`type="${resource.type}"`);
      if (resource.crossOrigin)
        attrs.push(`crossorigin="${resource.crossOrigin}"`);
      if (resource.media) attrs.push(`media="${resource.media}"`);
      if (resource.integrity) attrs.push(`integrity="${resource.integrity}"`);

      return `<link ${attrs.join(' ')} />`;
    })
    .join('\n');
}

/**
 * Generate prefetch link tags
 */
export function generatePrefetchTags(resources: PrefetchResource[]): string {
  return resources
    .map(resource => {
      const attrs = ['rel="prefetch"', `href="${resource.href}"`];

      if (resource.as) attrs.push(`as="${resource.as}"`);
      if (resource.type) attrs.push(`type="${resource.type}"`);

      return `<link ${attrs.join(' ')} />`;
    })
    .join('\n');
}

/**
 * Generate DNS prefetch tags for external domains
 */
export function generateDNSPrefetchTags(domains: string[]): string {
  return domains
    .map(domain => `<link rel="dns-prefetch" href="${domain}" />`)
    .join('\n');
}

/**
 * Generate preconnect tags for critical external resources
 */
export function generatePreconnectTags(domains: string[]): string {
  return domains
    .map(domain => `<link rel="preconnect" href="${domain}" crossorigin />`)
    .join('\n');
}

/**
 * Client-side resource preloader
 */
export class ResourcePreloader {
  private static instance: ResourcePreloader;
  private preloadedResources = new Set<string>();
  private prefetchedResources = new Set<string>();

  static getInstance(): ResourcePreloader {
    if (!ResourcePreloader.instance) {
      ResourcePreloader.instance = new ResourcePreloader();
    }
    return ResourcePreloader.instance;
  }

  /**
   * Preload a resource with high priority
   */
  preload(resource: PreloadResource): void {
    if (this.preloadedResources.has(resource.href)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;

    if (resource.type) link.type = resource.type;
    if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin;
    if (resource.media) link.media = resource.media;
    if (resource.integrity) link.integrity = resource.integrity;

    document.head.appendChild(link);
    this.preloadedResources.add(resource.href);
  }

  /**
   * Prefetch a resource for future navigation
   */
  prefetch(resource: PrefetchResource): void {
    if (this.prefetchedResources.has(resource.href)) return;

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = resource.href;

    if (resource.as) link.as = resource.as;
    if (resource.type) link.type = resource.type;

    document.head.appendChild(link);
    this.prefetchedResources.add(resource.href);
  }

  /**
   * Preload critical resources for the current page
   */
  preloadCriticalResources(): void {
    criticalResources.forEach(resource => this.preload(resource));
  }

  /**
   * Prefetch resources for likely next navigation
   */
  prefetchNextResources(): void {
    // Use requestIdleCallback for non-critical prefetching
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        prefetchResources.forEach(resource => this.prefetch(resource));
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        prefetchResources.forEach(resource => this.prefetch(resource));
      }, 2000);
    }
  }
}

/**
 * Initialize resource preloading on page load
 */
export function initializeResourcePreloading(): void {
  if (typeof window === 'undefined') return;

  const preloader = ResourcePreloader.getInstance();

  // Preload critical resources immediately
  preloader.preloadCriticalResources();

  // Prefetch next resources when page is loaded
  if (document.readyState === 'complete') {
    preloader.prefetchNextResources();
  } else {
    window.addEventListener('load', () => {
      preloader.prefetchNextResources();
    });
  }
}
