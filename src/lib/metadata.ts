/**
 * Metadata Configuration Utilities
 *
 * Centralized utilities for generating SEO-friendly metadata
 * with internationalization support for Next.js 14.2.18 App Router.
 */

import { routing } from '@/i18n/routing';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { config } from './config';

/**
 * Base metadata configuration
 */
export const baseMetadata: Metadata = {
  metadataBase: new URL(config.app.url),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  ...(process.env.GOOGLE_SITE_VERIFICATION ||
  process.env.YANDEX_VERIFICATION ||
  process.env.YAHOO_VERIFICATION
    ? {
        verification: {
          ...(process.env.GOOGLE_SITE_VERIFICATION && {
            google: process.env.GOOGLE_SITE_VERIFICATION,
          }),
          ...(process.env.YANDEX_VERIFICATION && {
            yandex: process.env.YANDEX_VERIFICATION,
          }),
          ...(process.env.YAHOO_VERIFICATION && {
            yahoo: process.env.YAHOO_VERIFICATION,
          }),
        },
      }
    : {}),
};

/**
 * Generate localized metadata for pages
 */
export async function generatePageMetadata({
  locale,
  path = '',
  images,
  noIndex = false,
}: {
  locale: string;
  path?: string;
  images?: string[];
  noIndex?: boolean;
}): Promise<Metadata> {
  const t = await getTranslations({ locale: locale as 'en' | 'zh' });

  const title = t('HomePage.title');
  const description = t('HomePage.description');
  const canonicalPath = path.startsWith('/') ? path : `/${path}`;

  // Generate alternate language URLs
  const alternateLanguages = Object.fromEntries(
    routing.locales.map(loc => [
      loc,
      `${config.app.url}/${loc}${canonicalPath}`,
    ])
  );

  // Default Open Graph images
  const defaultImages = images || [
    {
      url: '/og-image.jpg',
      width: 1200,
      height: 630,
      alt: title,
    },
  ];

  return {
    ...baseMetadata,
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
      url: `${config.app.url}/${locale}${canonicalPath}`,
      siteName: config.app.name,
      images: defaultImages,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: defaultImages,
      creator: '@tucsenberg',
      site: '@tucsenberg',
    },
    alternates: {
      canonical: `${config.app.url}/${locale}${canonicalPath}`,
      languages: alternateLanguages,
    },
    ...(noIndex
      ? {
          robots: {
            index: false,
            follow: false,
          },
        }
      : {}),
  };
}

/**
 * Generate metadata for blog posts
 */
export async function generateBlogMetadata({
  locale,
  title,
  description,
  slug,
  publishedTime,
  modifiedTime,
  authors = [],
  tags = [],
  images,
}: {
  locale: string;
  title: string;
  description: string;
  slug: string;
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  tags?: string[];
  images?: string[];
}): Promise<Metadata> {
  const canonicalPath = `/blog/${slug}`;

  // Generate alternate language URLs
  const alternateLanguages = Object.fromEntries(
    routing.locales.map(loc => [
      loc,
      `${config.app.url}/${loc}${canonicalPath}`,
    ])
  );

  // Default blog images
  const defaultImages = images || [
    {
      url: `/blog/${slug}/og-image.jpg`,
      width: 1200,
      height: 630,
      alt: title,
    },
  ];

  return {
    ...baseMetadata,
    title: `${title} | ${config.app.name}`,
    description,
    keywords: tags,
    authors: authors.map(author => ({ name: author })),
    openGraph: {
      title,
      description,
      type: 'article',
      locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
      url: `${config.app.url}/${locale}${canonicalPath}`,
      siteName: config.app.name,
      images: defaultImages,
      publishedTime,
      modifiedTime,
      authors,
      tags,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: defaultImages,
      creator: '@tucsenberg',
      site: '@tucsenberg',
    },
    alternates: {
      canonical: `${config.app.url}/${locale}${canonicalPath}`,
      languages: alternateLanguages,
    },
  };
}

/**
 * Generate metadata for product pages
 */
export async function generateProductMetadata({
  locale,
  title,
  description,
  slug,
  price,
  currency = 'USD',
  availability = 'in stock',
  images,
}: {
  locale: string;
  title: string;
  description: string;
  slug: string;
  price?: number;
  currency?: string;
  availability?: string;
  images?: string[];
}): Promise<Metadata> {
  const canonicalPath = `/products/${slug}`;

  // Generate alternate language URLs
  const alternateLanguages = Object.fromEntries(
    routing.locales.map(loc => [
      loc,
      `${config.app.url}/${loc}${canonicalPath}`,
    ])
  );

  // Default product images
  const defaultImages = images || [
    {
      url: `/products/${slug}/image-1.jpg`,
      width: 1200,
      height: 630,
      alt: title,
    },
  ];

  const metadata: Metadata = {
    ...baseMetadata,
    title: `${title} | ${config.app.name}`,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
      url: `${config.app.url}/${locale}${canonicalPath}`,
      siteName: config.app.name,
      images: defaultImages,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: defaultImages,
      creator: '@tucsenberg',
      site: '@tucsenberg',
    },
    alternates: {
      canonical: `${config.app.url}/${locale}${canonicalPath}`,
      languages: alternateLanguages,
    },
  };

  // Add product-specific structured data
  if (price) {
    metadata.other = {
      'product:price:amount': price.toString(),
      'product:price:currency': currency,
      'product:availability': availability,
    };
  }

  return metadata;
}

/**
 * Generate JSON-LD structured data
 */
export function generateStructuredData({
  type,
  data,
}: {
  type: 'Organization' | 'WebSite' | 'Article' | 'Product' | 'BreadcrumbList';
  data: Record<string, unknown>;
}) {
  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data,
  };

  return {
    __html: JSON.stringify(baseStructuredData),
  };
}

/**
 * Generate organization structured data
 */
export function generateOrganizationStructuredData() {
  return generateStructuredData({
    type: 'Organization',
    data: {
      name: config.app.name,
      url: config.app.url,
      logo: `${config.app.url}/logo.png`,
      sameAs: [
        'https://twitter.com/tucsenberg',
        'https://linkedin.com/company/tucsenberg',
        'https://github.com/tucsenberg',
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '******-0123',
        contactType: 'customer service',
        availableLanguage: routing.locales,
      },
    },
  });
}

/**
 * Generate website structured data
 */
export function generateWebsiteStructuredData(locale: string) {
  return generateStructuredData({
    type: 'WebSite',
    data: {
      name: config.app.name,
      url: `${config.app.url}/${locale}`,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${config.app.url}/${locale}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string',
      },
      inLanguage: locale,
    },
  });
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbStructuredData({
  locale,
  items,
}: {
  locale: string;
  items: Array<{ name: string; url: string }>;
}) {
  return generateStructuredData({
    type: 'BreadcrumbList',
    data: {
      itemListElement: items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: `${config.app.url}/${locale}${item.url}`,
      })),
    },
  });
}

/**
 * Metadata utilities for common patterns
 */
export const metadataUtils = {
  /**
   * Combine multiple metadata objects
   */
  combine: (...metadataObjects: Metadata[]): Metadata => {
    return metadataObjects.reduce(
      (acc, metadata) => ({
        ...acc,
        ...metadata,
        openGraph: {
          ...acc.openGraph,
          ...metadata.openGraph,
        },
        twitter: {
          ...acc.twitter,
          ...metadata.twitter,
        },
        alternates: {
          ...acc.alternates,
          ...metadata.alternates,
        },
      }),
      {}
    );
  },

  /**
   * Create no-index metadata
   */
  noIndex: (): Metadata => ({
    robots: {
      index: false,
      follow: false,
    },
  }),

  /**
   * Create redirect metadata
   */
  redirect: (url: string): Metadata => ({
    other: {
      'http-equiv': 'refresh',
      content: `0; url=${url}`,
    },
  }),
};
