/**
 * Configuration Management System
 *
 * Centralized configuration management with environment-specific settings,
 * type safety, and validation for the Tucsenberg Web Stable project.
 */

import {
  getClientEnv,
  getServerEnv,
  isDevelopment,
  isProduction,
  validateEnv,
} from './env';

// Initialize and validate environment variables
const env = validateEnv();

/**
 * Application Configuration
 * Contains all application settings organized by domain
 */
export const config = {
  // Application Information
  app: {
    name: env.NEXT_PUBLIC_APP_NAME,
    url: env.NEXT_PUBLIC_APP_URL,
    environment: env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  },

  // Development Configuration
  dev: {
    showDebugInfo: env.NEXT_PUBLIC_SHOW_DEBUG_INFO,
    enableDevtools: env.NEXT_PUBLIC_ENABLE_DEVTOOLS,
    hotReload: isDevelopment(),
  },

  // Database Configuration
  database: {
    url: env.DATABASE_URL,
    directUrl: env.DATABASE_DIRECT_URL,
    // Connection pool settings
    maxConnections: isProduction() ? 20 : 5,
    connectionTimeout: 30000, // 30 seconds
  },

  // Authentication Configuration
  auth: {
    secret: env.NEXTAUTH_SECRET,
    url: env.NEXTAUTH_URL || env.NEXT_PUBLIC_APP_URL,
    sessionMaxAge: 30 * 24 * 60 * 60, // 30 days
    jwtSecret: env.JWT_SECRET,
  },

  // Email Service Configuration
  email: {
    smtp: {
      host: env.SMTP_HOST,
      port: env.SMTP_PORT || 587,
      user: env.SMTP_USER,
      password: env.SMTP_PASSWORD,
      from: env.SMTP_FROM,
    },
    // Email templates configuration
    templates: {
      welcome: 'welcome',
      resetPassword: 'reset-password',
      verification: 'verification',
    },
  },

  // Analytics Configuration
  analytics: {
    enabled: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    vercelAnalyticsId: env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID,
    googleAnalyticsId: env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    // Privacy settings
    anonymizeIp: true,
    cookieConsent: true,
  },

  // API Configuration
  api: {
    // General API settings
    baseUrl: env.API_BASE_URL || env.NEXT_PUBLIC_APP_URL,
    timeout: env.API_TIMEOUT,
    retryAttempts: env.API_RETRY_ATTEMPTS,
    rateLimit: env.API_RATE_LIMIT,

    // External services
    openai: {
      apiKey: env.OPENAI_API_KEY,
      model: 'gpt-4',
      maxTokens: 2000,
    },
    stripe: {
      secretKey: env.STRIPE_SECRET_KEY,
      publishableKey: env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    },

    // External API endpoints
    external: {
      apiUrl: env.EXTERNAL_API_URL,
      webhookUrl: env.WEBHOOK_URL,
    },
  },

  // Security Configuration
  security: {
    encryptionKey: env.ENCRYPTION_KEY,
    corsOrigins: isProduction()
      ? [env.NEXT_PUBLIC_APP_URL]
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    rateLimiting: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: isProduction() ? 100 : 1000,
    },
  },

  // Feature Flags
  features: {
    analytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    errorReporting: env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING,
    maintenance: false,
    betaFeatures: isDevelopment(),
  },

  // Performance Configuration
  performance: {
    // Image optimization
    images: {
      domains: ['localhost', 'tucsenberg.com'],
      formats: ['image/webp', 'image/avif'],
      quality: isProduction() ? 80 : 90,
    },
    // Caching
    cache: {
      staticAssets: isProduction() ? '1y' : '0',
      apiResponses: isProduction() ? '5m' : '0',
      redis: {
        url: env.REDIS_URL,
        ttl: env.CACHE_TTL,
        keyPrefix: 'tucsenberg:',
      },
    },
  },

  // Logging Configuration
  logging: {
    level: isProduction() ? 'error' : 'debug',
    enableConsole: !isProduction(),
    enableFile: isProduction(),
    maxFileSize: '10MB',
    maxFiles: 5,
  },
} as const;

/**
 * Environment-specific configuration getters
 */
export const getConfig = () => config;

/**
 * Get client-safe configuration
 * Only includes environment variables that are safe to expose to the browser
 */
export const getClientConfig = () => {
  const clientEnv = getClientEnv();

  return {
    app: {
      name: clientEnv.NEXT_PUBLIC_APP_NAME,
      url: clientEnv.NEXT_PUBLIC_APP_URL,
      environment: clientEnv.NODE_ENV,
    },
    dev: {
      showDebugInfo: clientEnv.NEXT_PUBLIC_SHOW_DEBUG_INFO,
      enableDevtools: clientEnv.NEXT_PUBLIC_ENABLE_DEVTOOLS,
    },
    analytics: {
      enabled: clientEnv.NEXT_PUBLIC_ENABLE_ANALYTICS,
      vercelAnalyticsId: clientEnv.NEXT_PUBLIC_VERCEL_ANALYTICS_ID,
      googleAnalyticsId: clientEnv.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    },
    features: {
      analytics: clientEnv.NEXT_PUBLIC_ENABLE_ANALYTICS,
      errorReporting: clientEnv.NEXT_PUBLIC_ENABLE_ERROR_REPORTING,
    },
  };
};

/**
 * Get server-only configuration
 * Includes sensitive environment variables that should never be exposed to the browser
 */
export const getServerConfig = () => {
  // Ensure server environment is loaded
  getServerEnv();

  return {
    database: config.database,
    auth: config.auth,
    email: config.email,
    api: config.api,
    security: config.security,
    logging: config.logging,
  };
};

/**
 * Configuration validation
 */
export const validateConfig = () => {
  const errors: string[] = [];

  // Validate production requirements
  if (isProduction()) {
    if (!env.NEXTAUTH_SECRET) {
      errors.push('NEXTAUTH_SECRET is required in production');
    }
    if (!env.DATABASE_URL) {
      errors.push('DATABASE_URL is required in production');
    }
  }

  // Validate email configuration if SMTP is configured
  if (env.SMTP_HOST && (!env.SMTP_USER || !env.SMTP_PASSWORD)) {
    errors.push(
      'SMTP_USER and SMTP_PASSWORD are required when SMTP_HOST is set'
    );
  }

  if (errors.length > 0) {
    throw new Error(
      `❌ Configuration validation failed:\n${errors.join('\n')}`
    );
  }

  return true;
};

// Validate configuration on module load
validateConfig();

/**
 * Export environment utilities
 */
export { isDevelopment, isProduction, isTest } from './env';
