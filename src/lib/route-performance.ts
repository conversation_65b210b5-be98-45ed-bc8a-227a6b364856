/**
 * Route Performance Monitoring and Optimization
 *
 * This module provides utilities for monitoring and optimizing route performance,
 * including redirect tracking, navigation timing, and performance metrics.
 */

'use client';

export interface RouteMetrics {
  path: string;
  locale: string;
  loadTime: number;
  redirectCount: number;
  navigationStart: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
  firstInputDelay?: number;
}

export interface RedirectInfo {
  from: string;
  to: string;
  timestamp: number;
  reason: 'locale-detection' | 'route-change' | 'middleware' | 'manual';
  duration: number;
}

/**
 * Route Performance Monitor
 */
export class RoutePerformanceMonitor {
  private static instance: RoutePerformanceMonitor;
  private metrics: RouteMetrics[] = [];
  private redirects: RedirectInfo[] = [];
  private currentNavigation: {
    start: number;
    path: string;
    locale: string;
  } | null = null;

  static getInstance(): RoutePerformanceMonitor {
    if (!RoutePerformanceMonitor.instance) {
      RoutePerformanceMonitor.instance = new RoutePerformanceMonitor();
    }
    return RoutePerformanceMonitor.instance;
  }

  /**
   * Start tracking a navigation
   */
  startNavigation(path: string, locale: string): void {
    this.currentNavigation = {
      start: performance.now(),
      path,
      locale,
    };
  }

  /**
   * End tracking a navigation and record metrics
   */
  endNavigation(): void {
    if (!this.currentNavigation) return;

    const endTime = performance.now();
    const loadTime = endTime - this.currentNavigation.start;

    // Get navigation timing
    const navigation = performance.getEntriesByType(
      'navigation'
    )[0] as PerformanceNavigationTiming;

    // Get paint timing
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(
      entry => entry.name === 'first-contentful-paint'
    );

    // Get LCP from observer (if available)
    const lcp = this.getLargestContentfulPaint();

    const metrics: RouteMetrics = {
      path: this.currentNavigation.path,
      locale: this.currentNavigation.locale,
      loadTime,
      redirectCount: navigation?.redirectCount || 0,
      navigationStart: navigation?.fetchStart || 0,
      domContentLoaded:
        (navigation?.domContentLoadedEventEnd || 0) -
        (navigation?.fetchStart || 0),
      ...(fcp?.startTime !== undefined && {
        firstContentfulPaint: fcp.startTime,
      }),
      ...(lcp !== undefined && { largestContentfulPaint: lcp }),
      ...(this.getCumulativeLayoutShift() !== undefined && {
        cumulativeLayoutShift: this.getCumulativeLayoutShift()!,
      }),
      ...(this.getFirstInputDelay() !== undefined && {
        firstInputDelay: this.getFirstInputDelay()!,
      }),
    };

    this.metrics.push(metrics);
    this.currentNavigation = null;

    // Log performance data in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Route Performance:', metrics);
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(metrics);
    }
  }

  /**
   * Record a redirect
   */
  recordRedirect(
    from: string,
    to: string,
    reason: RedirectInfo['reason']
  ): void {
    const redirect: RedirectInfo = {
      from,
      to,
      timestamp: Date.now(),
      reason,
      duration: performance.now(),
    };

    this.redirects.push(redirect);

    // Log redirect in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Redirect recorded:', redirect);
    }
  }

  /**
   * Get performance metrics for analysis
   */
  getMetrics(): RouteMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get redirect history
   */
  getRedirects(): RedirectInfo[] {
    return [...this.redirects];
  }

  /**
   * Get average load time for a specific route
   */
  getAverageLoadTime(path?: string): number {
    const relevantMetrics = path
      ? this.metrics.filter(m => m.path === path)
      : this.metrics;

    if (relevantMetrics.length === 0) return 0;

    const totalTime = relevantMetrics.reduce((sum, m) => sum + m.loadTime, 0);
    return totalTime / relevantMetrics.length;
  }

  /**
   * Get redirect count for analysis
   */
  getTotalRedirects(): number {
    return this.redirects.length;
  }

  /**
   * Clear metrics (useful for testing)
   */
  clearMetrics(): void {
    this.metrics = [];
    this.redirects = [];
  }

  /**
   * Get Largest Contentful Paint from observer
   */
  private getLargestContentfulPaint(): number | undefined {
    try {
      const lcpEntries = performance.getEntriesByType(
        'largest-contentful-paint'
      );
      return lcpEntries.length > 0
        ? lcpEntries[lcpEntries.length - 1]?.startTime
        : undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Get Cumulative Layout Shift
   */
  private getCumulativeLayoutShift(): number | undefined {
    try {
      const clsEntries = performance.getEntriesByType('layout-shift');
      return clsEntries.reduce((sum, entry: any) => {
        if (!entry.hadRecentInput) {
          return sum + entry.value;
        }
        return sum;
      }, 0);
    } catch {
      return undefined;
    }
  }

  /**
   * Get First Input Delay
   */
  private getFirstInputDelay(): number | undefined {
    try {
      const fidEntries = performance.getEntriesByType('first-input');
      return fidEntries.length > 0
        ? (fidEntries[0] as any)?.processingStart -
            (fidEntries[0]?.startTime || 0)
        : undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Send metrics to analytics service
   */
  private sendToAnalytics(metrics: RouteMetrics): void {
    // In a real application, you would send this to your analytics service
    // For now, we'll just store it locally
    try {
      const existingData = localStorage.getItem('route-performance-metrics');
      const allMetrics = existingData ? JSON.parse(existingData) : [];
      allMetrics.push(metrics);

      // Keep only last 100 entries to prevent storage bloat
      if (allMetrics.length > 100) {
        allMetrics.splice(0, allMetrics.length - 100);
      }

      localStorage.setItem(
        'route-performance-metrics',
        JSON.stringify(allMetrics)
      );
    } catch (error) {
      console.warn('Failed to store performance metrics:', error);
    }
  }
}

/**
 * Hook for tracking route performance in React components
 */
export function useRoutePerformance() {
  const monitor = RoutePerformanceMonitor.getInstance();

  return {
    startNavigation: monitor.startNavigation.bind(monitor),
    endNavigation: monitor.endNavigation.bind(monitor),
    recordRedirect: monitor.recordRedirect.bind(monitor),
    getMetrics: monitor.getMetrics.bind(monitor),
    getRedirects: monitor.getRedirects.bind(monitor),
    getAverageLoadTime: monitor.getAverageLoadTime.bind(monitor),
    getTotalRedirects: monitor.getTotalRedirects.bind(monitor),
  };
}

/**
 * Initialize route performance monitoring
 */
export function initializeRoutePerformanceMonitoring(): void {
  if (typeof window === 'undefined') return;

  const monitor = RoutePerformanceMonitor.getInstance();

  // Track initial page load
  if (document.readyState === 'complete') {
    monitor.endNavigation();
  } else {
    window.addEventListener('load', () => {
      monitor.endNavigation();
    });
  }

  // Track navigation changes
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function (...args) {
    const url = typeof args[2] === 'string' ? args[2] : location.pathname;
    monitor.startNavigation(url, getLocaleFromPath(url));
    return originalPushState.apply(this, args);
  };

  history.replaceState = function (...args) {
    const url = typeof args[2] === 'string' ? args[2] : location.pathname;
    monitor.recordRedirect(location.pathname, url, 'route-change');
    return originalReplaceState.apply(this, args);
  };

  // Track popstate events
  window.addEventListener('popstate', () => {
    monitor.startNavigation(
      location.pathname,
      getLocaleFromPath(location.pathname)
    );
  });
}

/**
 * Extract locale from path
 */
function getLocaleFromPath(path: string): string {
  const match = path.match(/^\/([a-z]{2})\//);
  return match?.[1] || 'en';
}
