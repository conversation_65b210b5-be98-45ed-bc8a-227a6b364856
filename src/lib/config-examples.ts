/**
 * Configuration Usage Examples
 *
 * This file demonstrates how to use the configuration system
 * in different parts of the application.
 */

import { config, getClientConfig, getServerConfig } from './config';
import { isDevelopment, isProduction } from './env';

/**
 * Example: Using configuration in a React component (client-side)
 */
export function useClientConfig() {
  // Only use client-safe configuration in components
  const clientConfig = getClientConfig();

  return {
    appName: clientConfig.app.name,
    appUrl: clientConfig.app.url,
    showDebug: clientConfig.dev.showDebugInfo,
    analyticsEnabled: clientConfig.analytics.enabled,
  };
}

/**
 * Example: Using configuration in API routes (server-side)
 */
export function getApiConfig() {
  // Use server configuration for API routes and server-side logic
  const serverConfig = getServerConfig();

  return {
    database: serverConfig.database,
    auth: serverConfig.auth,
    email: serverConfig.email,
    security: serverConfig.security,
  };
}

/**
 * Example: Environment-specific behavior
 */
export function getEnvironmentSpecificSettings() {
  return {
    // Different logging levels per environment
    logLevel: isDevelopment() ? 'debug' : 'error',

    // Different cache durations
    cacheTimeout: isProduction() ? 3600 : 0, // 1 hour in prod, no cache in dev

    // Different rate limiting
    rateLimit: isProduction() ? 100 : 1000, // Stricter in production

    // Different error handling
    showStackTrace: isDevelopment(),
  };
}

/**
 * Example: Database connection configuration
 */
export function getDatabaseConfig() {
  return {
    url: config.database.url,
    maxConnections: config.database.maxConnections,
    connectionTimeout: config.database.connectionTimeout,
    ssl: isProduction() ? { rejectUnauthorized: false } : false,
  };
}

/**
 * Example: Email service configuration
 */
export function getEmailConfig() {
  if (!config.email.smtp.host) {
    throw new Error('Email service is not configured');
  }

  return {
    host: config.email.smtp.host,
    port: config.email.smtp.port,
    secure: config.email.smtp.port === 465, // true for 465, false for other ports
    auth: {
      user: config.email.smtp.user,
      pass: config.email.smtp.password,
    },
    from: config.email.smtp.from,
  };
}

/**
 * Example: Analytics configuration
 */
export function getAnalyticsConfig() {
  if (!config.features.analytics) {
    return null;
  }

  return {
    vercelAnalyticsId: config.analytics.vercelAnalyticsId,
    googleAnalyticsId: config.analytics.googleAnalyticsId,
    anonymizeIp: config.analytics.anonymizeIp,
    cookieConsent: config.analytics.cookieConsent,
  };
}

/**
 * Example: Security configuration
 */
export function getSecurityConfig() {
  return {
    corsOrigins: config.security.corsOrigins,
    rateLimiting: config.security.rateLimiting,
    encryptionKey: config.security.encryptionKey,
  };
}

/**
 * Example: Feature flags usage
 */
export function getFeatureFlags() {
  return {
    analytics: config.features.analytics,
    errorReporting: config.features.errorReporting,
    maintenance: config.features.maintenance,
    betaFeatures: config.features.betaFeatures,
  };
}

/**
 * Example: Performance configuration
 */
export function getPerformanceConfig() {
  return {
    images: {
      domains: config.performance.images.domains,
      formats: config.performance.images.formats,
      quality: config.performance.images.quality,
    },
    cache: {
      staticAssets: config.performance.cache.staticAssets,
      apiResponses: config.performance.cache.apiResponses,
    },
  };
}

/**
 * Example: Conditional configuration based on environment
 */
export function getConditionalConfig() {
  const baseConfig = {
    apiTimeout: 30000, // 30 seconds
    retryAttempts: 3,
  };

  if (isDevelopment()) {
    return {
      ...baseConfig,
      apiTimeout: 60000, // Longer timeout in development
      retryAttempts: 1, // Fewer retries for faster feedback
      mockData: true,
    };
  }

  if (isProduction()) {
    return {
      ...baseConfig,
      apiTimeout: 15000, // Shorter timeout in production
      retryAttempts: 5, // More retries for reliability
      mockData: false,
    };
  }

  return baseConfig;
}

/**
 * Example: Configuration validation in application startup
 */
export function validateApplicationConfig() {
  const requiredConfigs = [];

  // Check database configuration
  if (!config.database.url && isProduction()) {
    requiredConfigs.push('Database URL is required in production');
  }

  // Check authentication configuration
  if (!config.auth.secret && isProduction()) {
    requiredConfigs.push('Authentication secret is required in production');
  }

  // Check email configuration if features require it
  if (config.features.errorReporting && !config.email.smtp.host) {
    requiredConfigs.push('Email configuration is required for error reporting');
  }

  if (requiredConfigs.length > 0) {
    throw new Error(
      `Configuration validation failed:\n${requiredConfigs.join('\n')}`
    );
  }

  return true;
}
