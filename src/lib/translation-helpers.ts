/**
 * Translation helpers for type-safe internationalization
 */

import { getTranslations } from 'next-intl/server';

/**
 * Type-safe locale type
 */
export type SupportedLocale = 'en' | 'zh';

/**
 * Get translations with proper type casting
 */
export async function getTypedTranslations(locale: string, namespace?: string) {
  if (namespace) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return await getTranslations({
      locale: locale as SupportedLocale,
      namespace: namespace as any,
    });
  }
  return await getTranslations({ locale: locale as SupportedLocale });
}

/**
 * Get page translations for home page
 */
export async function getHomeTranslations(locale: string) {
  const t = await getTranslations({
    locale: locale as SupportedLocale,
    namespace: 'HomePage',
  });
  const techStack = await getTranslations({
    locale: locale as SupportedLocale,
    namespace: 'HomePage.techStack',
  });
  return { t, techStack };
}

/**
 * Get metadata translations
 */
export async function getMetadataTranslations(locale: string) {
  return await getTypedTranslations(locale, 'metadata');
}
