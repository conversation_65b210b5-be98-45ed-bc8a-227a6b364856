/**
 * Environment Variables Type Definitions and Validation
 *
 * This module provides type-safe environment variable access with validation
 * and default values for different deployment environments.
 */

import { z } from 'zod';

// Environment variable schema definition
const envSchema = z.object({
  // Node.js Environment
  NODE_ENV: z
    .enum(['development', 'test', 'production'])
    .default('development'),

  // Next.js Configuration
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  NEXT_PUBLIC_APP_NAME: z.string().default('Tucsenberg Web Stable'),

  // Development Configuration
  NEXT_PUBLIC_SHOW_DEBUG_INFO: z
    .string()
    .transform(val => val === 'true')
    .default('false'),
  NEXT_PUBLIC_ENABLE_DEVTOOLS: z
    .string()
    .transform(val => val === 'true')
    .default('false'),

  // Database Configuration (Optional)
  DATABASE_URL: z.string().optional(),
  DATABASE_DIRECT_URL: z.string().optional(),

  // Authentication Configuration (Optional)
  NEXTAUTH_SECRET: z.string().optional(),
  NEXTAUTH_URL: z.string().url().optional(),

  // Email Service Configuration (Optional)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z
    .string()
    .transform(val => parseInt(val, 10))
    .optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  SMTP_FROM: z.string().email().optional(),

  // Analytics Configuration (Optional)
  NEXT_PUBLIC_VERCEL_ANALYTICS_ID: z.string().optional(),
  NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().optional(),

  // API Keys (Optional)
  OPENAI_API_KEY: z.string().optional(),
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),

  // Security Configuration
  ENCRYPTION_KEY: z.string().optional(),
  JWT_SECRET: z.string().optional(),

  // Feature Flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: z
    .string()
    .transform(val => val === 'true')
    .default('false'),
  NEXT_PUBLIC_ENABLE_ERROR_REPORTING: z
    .string()
    .transform(val => val === 'true')
    .default('false'),

  // API Configuration
  API_BASE_URL: z.string().url().optional(),
  API_TIMEOUT: z
    .string()
    .transform(val => parseInt(val, 10))
    .default('30000'),
  API_RETRY_ATTEMPTS: z
    .string()
    .transform(val => parseInt(val, 10))
    .default('3'),
  API_RATE_LIMIT: z
    .string()
    .transform(val => parseInt(val, 10))
    .default('100'),

  // External Service URLs
  EXTERNAL_API_URL: z.string().url().optional(),
  WEBHOOK_URL: z.string().url().optional(),

  // Cache Configuration
  REDIS_URL: z.string().optional(),
  CACHE_TTL: z
    .string()
    .transform(val => parseInt(val, 10))
    .default('3600'),
});

// Infer TypeScript type from schema
export type Env = z.infer<typeof envSchema>;

// Environment validation function
export function validateEnv(): Env {
  try {
    const env = envSchema.parse(process.env);
    return env;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join('\n');

      throw new Error(
        `❌ Invalid environment variables:\n${missingVars}\n\n` +
          `Please check your .env.local file and ensure all required variables are set.`
      );
    }
    throw error;
  }
}

// Environment type guards
export const isDevelopment = () => process.env.NODE_ENV === 'development';
export const isProduction = () => process.env.NODE_ENV === 'production';
export const isTest = () => process.env.NODE_ENV === 'test';

// Client-side environment variables (safe to expose)
export const getClientEnv = () => {
  return {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    NEXT_PUBLIC_SHOW_DEBUG_INFO:
      process.env.NEXT_PUBLIC_SHOW_DEBUG_INFO === 'true',
    NEXT_PUBLIC_ENABLE_DEVTOOLS:
      process.env.NEXT_PUBLIC_ENABLE_DEVTOOLS === 'true',
    NEXT_PUBLIC_VERCEL_ANALYTICS_ID:
      process.env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID,
    NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    NEXT_PUBLIC_ENABLE_ANALYTICS:
      process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    NEXT_PUBLIC_ENABLE_ERROR_REPORTING:
      process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true',
  };
};

// Server-side environment variables (sensitive data)
export const getServerEnv = () => {
  if (typeof window !== 'undefined') {
    throw new Error(
      '❌ Server environment variables cannot be accessed on the client side'
    );
  }

  return validateEnv();
};
