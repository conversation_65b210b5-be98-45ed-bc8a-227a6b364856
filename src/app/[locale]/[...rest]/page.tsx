import { notFound } from 'next/navigation';

/**
 * Catch-All Page for Unknown Localized Routes
 *
 * Captures all unknown paths within localized routes and ensures users
 * see localized 404 pages instead of root-level errors.
 *
 * Technical Details:
 * - Server component (no client-side interaction needed)
 * - Uses [...rest] dynamic route to catch all remaining path segments
 * - Calls Next.js notFound() to trigger localized error handling
 * - Integrates with existing locale validation and error handling patterns
 * - Maintains separation between localized and root-level error handling
 *
 * Examples:
 * - /en/unknown → triggers English 404 page
 * - /zh/nonexistent → triggers Chinese 404 page
 * - /en/some/deep/path → triggers English 404 page
 */
export default function CatchAllPage() {
  notFound();
}
