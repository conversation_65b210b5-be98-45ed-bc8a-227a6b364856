/**
 * Products Page
 *
 * Displays product listings with internationalization support.
 * Built for Next.js 14.2.18 App Router with next-intl integration.
 */

import { routing } from '@/i18n/routing';
import { Metadata } from 'next';
import { hasLocale } from 'next-intl';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface ProductsPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * Generate metadata for the products page
 */
export async function generateMetadata({
  params,
}: ProductsPageProps): Promise<Metadata> {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const t = await getTranslations({
    locale,
    namespace: 'ProductsPage.metadata',
  });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
    alternates: {
      canonical: '/products',
      languages: Object.fromEntries(
        routing.locales.map(loc => [loc, `/${loc}/products`])
      ),
    },
  };
}

/**
 * Generate static params for all supported locales
 */
export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}

/**
 * Products Page Component
 */
export default async function ProductsPage({ params }: ProductsPageProps) {
  const { locale } = await params;

  // Validate locale
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  // Get translations
  const t = await getTranslations('ProductsPage');

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Page Header */}
      <header className='mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4'>
          {t('title')}
        </h1>
        <p className='text-lg text-gray-600 dark:text-gray-400 max-w-2xl'>
          {t('description')}
        </p>
      </header>

      {/* Under Construction Notice */}
      <div className='bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 mb-8'>
        <div className='flex items-center'>
          <div className='flex-shrink-0'>
            <svg
              className='h-5 w-5 text-yellow-400'
              viewBox='0 0 20 20'
              fill='currentColor'
            >
              <path
                fillRule='evenodd'
                d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-yellow-800 dark:text-yellow-200'>
              {t('underConstruction.title')}
            </h3>
            <p className='mt-1 text-sm text-yellow-700 dark:text-yellow-300'>
              {t('underConstruction.message')}
            </p>
          </div>
        </div>
      </div>

      {/* Placeholder Content */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {[1, 2, 3, 4, 5, 6].map(item => (
          <div
            key={item}
            className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700'
          >
            <div className='animate-pulse'>
              <div className='bg-gray-300 dark:bg-gray-600 h-48 rounded-lg mb-4'></div>
              <div className='space-y-2'>
                <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-3/4'></div>
                <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-1/2'></div>
                <div className='bg-gray-300 dark:bg-gray-600 h-6 rounded w-1/4 mt-4'></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Coming Soon Section */}
      <div className='mt-12 text-center'>
        <h2 className='text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4'>
          {t('comingSoon.title')}
        </h2>
        <p className='text-gray-600 dark:text-gray-400 mb-6'>
          {t('comingSoon.description')}
        </p>
        <div className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors'>
          {t('comingSoon.notifyButton')}
        </div>
      </div>
    </div>
  );
}
