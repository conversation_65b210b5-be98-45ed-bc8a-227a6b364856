import { getHomeTranslations } from '@/lib/translation-helpers';

interface HomeProps {
  params: {
    locale: string;
  };
}

export default async function Home({ params: { locale } }: HomeProps) {
  const { t, techStack } = await getHomeTranslations(locale);

  return (
    <>
      {/* 滚动进度指示器 */}
      <div
        className='fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left'
        style={{ transform: 'scaleX(0)' }}
      />

      <main className='flex min-h-screen flex-col items-center justify-between p-24'>
        {/* 顶部标题区域 - 添加滚动显示动画 */}
        <div className='z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex'>
          <p className='fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-6 pt-8 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto  lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30'>
            {t('title')}&nbsp;
            <code className='font-mono font-bold'>Next.js 14.2.18</code>
          </p>
        </div>

        {/* 英雄区域 - 添加缩放进入动画 */}
        <div className='relative flex place-items-center scroll-reveal-scale'>
          {/* 优化的渐变背景 - 使用 transform3d 和 will-change 提升性能 */}
          <div
            className='absolute h-[300px] w-full sm:w-[480px] -translate-x-1/2 rounded-full opacity-60 blur-2xl will-change-transform'
            style={{
              background:
                'radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%)',
              transform: 'translate3d(-50%, 0, 0)',
            }}
            aria-hidden='true'
          />
          <div
            className='absolute -z-20 h-[180px] w-full sm:w-[240px] translate-x-1/3 opacity-40 blur-2xl will-change-transform dark:opacity-60'
            style={{
              background:
                'conic-gradient(from 0deg, #0ea5e9, #3b82f6, #0ea5e9)',
              transform: 'translate3d(33%, 0, 0)',
            }}
            aria-hidden='true'
          />
          <div className='text-center relative z-10'>
            <h1 className='text-4xl font-bold mb-4'>{t('welcome')}</h1>
            <p className='text-lg text-gray-600 dark:text-gray-300'>
              {t('subtitle')}
            </p>
          </div>
        </div>

        {/* 技术栈卡片 - 添加交错动画 */}
        <div className='mb-32 grid text-center lg:max-w-5xl lg:w-full lg:mb-0 lg:grid-cols-4 lg:text-left scroll-stagger-container'>
          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30 scroll-stagger-item'>
            <h2 className='mb-3 text-2xl font-semibold'>
              Next.js 14.2.18{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              {techStack('nextjs')}
            </p>
          </div>

          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30 scroll-stagger-item'>
            <h2 className='mb-3 text-2xl font-semibold'>
              TypeScript 5.6.3{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              {techStack('typescript')}
            </p>
          </div>

          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30 scroll-stagger-item'>
            <h2 className='mb-3 text-2xl font-semibold'>
              Tailwind CSS 3.4.14{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              {techStack('tailwind')}
            </p>
          </div>

          <div className='group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30 scroll-stagger-item'>
            <h2 className='mb-3 text-2xl font-semibold'>
              {techStack('stableTitle')}{' '}
              <span className='inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none'>
                -&gt;
              </span>
            </h2>
            <p className='m-0 max-w-[30ch] text-sm opacity-50'>
              {techStack('stable')}
            </p>
          </div>
        </div>
      </main>
    </>
  );
}
