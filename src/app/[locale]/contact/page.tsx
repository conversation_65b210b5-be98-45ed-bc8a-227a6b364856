/**
 * Contact Page
 *
 * Displays contact information and form with internationalization support.
 * Built for Next.js 14.2.18 App Router with next-intl integration.
 */

import { routing } from '@/i18n/routing';
import { Metadata } from 'next';
import { hasLocale } from 'next-intl';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface ContactPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * Generate metadata for the contact page
 */
export async function generateMetadata({
  params,
}: ContactPageProps): Promise<Metadata> {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const t = await getTranslations({
    locale,
    namespace: 'ContactPage.metadata',
  });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
    alternates: {
      canonical: '/contact',
      languages: Object.fromEntries(
        routing.locales.map(loc => [loc, `/${loc}/contact`])
      ),
    },
  };
}

/**
 * Generate static params for all supported locales
 */
export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}

/**
 * Contact Page Component
 */
export default async function ContactPage({ params }: ContactPageProps) {
  const { locale } = await params;

  // Validate locale
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  // Get translations
  const t = await getTranslations('ContactPage');

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Page Header */}
      <header className='text-center mb-12'>
        <h1 className='text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6'>
          {t('title')}
        </h1>
        <p className='text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto'>
          {t('description')}
        </p>
      </header>

      {/* Under Construction Notice */}
      <div className='bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6 mb-12'>
        <div className='flex items-center'>
          <div className='flex-shrink-0'>
            <svg
              className='h-5 w-5 text-purple-400'
              viewBox='0 0 20 20'
              fill='currentColor'
            >
              <path d='M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z' />
              <path d='M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z' />
            </svg>
          </div>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-purple-800 dark:text-purple-200'>
              {t('underConstruction.title')}
            </h3>
            <p className='mt-1 text-sm text-purple-700 dark:text-purple-300'>
              {t('underConstruction.message')}
            </p>
          </div>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
        {/* Contact Information */}
        <div>
          <h2 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8'>
            {t('info.title')}
          </h2>

          <div className='space-y-6'>
            {/* Address */}
            <div className='flex items-start'>
              <div className='flex-shrink-0'>
                <svg
                  className='w-6 h-6 text-blue-600 dark:text-blue-400 mt-1'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z'
                  />
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M15 11a3 3 0 11-6 0 3 3 0 016 0z'
                  />
                </svg>
              </div>
              <div className='ml-4'>
                <h3 className='text-lg font-medium text-gray-900 dark:text-gray-100'>
                  {t('info.address.title')}
                </h3>
                <p className='text-gray-600 dark:text-gray-400'>
                  {t('info.address.value')}
                </p>
              </div>
            </div>

            {/* Phone */}
            <div className='flex items-start'>
              <div className='flex-shrink-0'>
                <svg
                  className='w-6 h-6 text-blue-600 dark:text-blue-400 mt-1'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z'
                  />
                </svg>
              </div>
              <div className='ml-4'>
                <h3 className='text-lg font-medium text-gray-900 dark:text-gray-100'>
                  {t('info.phone.title')}
                </h3>
                <p className='text-gray-600 dark:text-gray-400'>
                  {t('info.phone.value')}
                </p>
              </div>
            </div>

            {/* Email */}
            <div className='flex items-start'>
              <div className='flex-shrink-0'>
                <svg
                  className='w-6 h-6 text-blue-600 dark:text-blue-400 mt-1'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
                  />
                </svg>
              </div>
              <div className='ml-4'>
                <h3 className='text-lg font-medium text-gray-900 dark:text-gray-100'>
                  {t('info.email.title')}
                </h3>
                <p className='text-gray-600 dark:text-gray-400'>
                  {t('info.email.value')}
                </p>
              </div>
            </div>

            {/* Business Hours */}
            <div className='flex items-start'>
              <div className='flex-shrink-0'>
                <svg
                  className='w-6 h-6 text-blue-600 dark:text-blue-400 mt-1'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
              </div>
              <div className='ml-4'>
                <h3 className='text-lg font-medium text-gray-900 dark:text-gray-100'>
                  {t('info.hours.title')}
                </h3>
                <p className='text-gray-600 dark:text-gray-400'>
                  {t('info.hours.value')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <div>
          <h2 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8'>
            {t('form.title')}
          </h2>

          <form className='space-y-6'>
            {/* Name */}
            <div>
              <label
                htmlFor='name'
                className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
              >
                {t('form.fields.name')}
              </label>
              <input
                type='text'
                id='name'
                name='name'
                className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white'
                placeholder={t('form.placeholders.name')}
                disabled
              />
            </div>

            {/* Email */}
            <div>
              <label
                htmlFor='email'
                className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
              >
                {t('form.fields.email')}
              </label>
              <input
                type='email'
                id='email'
                name='email'
                className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white'
                placeholder={t('form.placeholders.email')}
                disabled
              />
            </div>

            {/* Subject */}
            <div>
              <label
                htmlFor='subject'
                className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
              >
                {t('form.fields.subject')}
              </label>
              <input
                type='text'
                id='subject'
                name='subject'
                className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white'
                placeholder={t('form.placeholders.subject')}
                disabled
              />
            </div>

            {/* Message */}
            <div>
              <label
                htmlFor='message'
                className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
              >
                {t('form.fields.message')}
              </label>
              <textarea
                id='message'
                name='message'
                rows={6}
                className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white'
                placeholder={t('form.placeholders.message')}
                disabled
              />
            </div>

            {/* Submit Button */}
            <div>
              <button
                type='submit'
                className='w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
                disabled
              >
                {t('form.submit')}
              </button>
            </div>

            {/* Coming Soon Notice */}
            <div className='text-center'>
              <p className='text-sm text-gray-500 dark:text-gray-400'>
                {t('form.comingSoon')}
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Map Placeholder */}
      <div className='mt-16'>
        <h2 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center'>
          {t('map.title')}
        </h2>
        <div className='bg-gray-200 dark:bg-gray-700 rounded-lg h-64 flex items-center justify-center'>
          <div className='text-gray-500 dark:text-gray-400 text-center'>
            <svg
              className='w-16 h-16 mx-auto mb-4'
              fill='currentColor'
              viewBox='0 0 20 20'
            >
              <path
                fillRule='evenodd'
                d='M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z'
                clipRule='evenodd'
              />
            </svg>
            <p className='text-sm'>{t('map.placeholder')}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
