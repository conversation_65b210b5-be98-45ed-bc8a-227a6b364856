/**
 * About Page
 *
 * Displays company information with internationalization support.
 * Built for Next.js 14.2.18 App Router with next-intl integration.
 */

import { routing } from '@/i18n/routing';
import { Metadata } from 'next';
import { hasLocale } from 'next-intl';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface AboutPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * Generate metadata for the about page
 */
export async function generateMetadata({
  params,
}: AboutPageProps): Promise<Metadata> {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const t = await getTranslations({ locale, namespace: 'AboutPage.metadata' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
    alternates: {
      canonical: '/about',
      languages: Object.fromEntries(
        routing.locales.map(loc => [loc, `/${loc}/about`])
      ),
    },
  };
}

/**
 * Generate static params for all supported locales
 */
export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}

/**
 * About Page Component
 */
export default async function AboutPage({ params }: AboutPageProps) {
  const { locale } = await params;

  // Validate locale
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  // Get translations
  const t = await getTranslations('AboutPage');

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Hero Section */}
      <section className='text-center mb-16'>
        <h1 className='text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6'>
          {t('hero.title')}
        </h1>
        <p className='text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed'>
          {t('hero.description')}
        </p>
      </section>

      {/* Under Construction Notice */}
      <div className='bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 mb-12'>
        <div className='flex items-center'>
          <div className='flex-shrink-0'>
            <svg
              className='h-5 w-5 text-green-400'
              viewBox='0 0 20 20'
              fill='currentColor'
            >
              <path
                fillRule='evenodd'
                d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-green-800 dark:text-green-200'>
              {t('underConstruction.title')}
            </h3>
            <p className='mt-1 text-sm text-green-700 dark:text-green-300'>
              {t('underConstruction.message')}
            </p>
          </div>
        </div>
      </div>

      {/* Mission Section */}
      <section className='mb-16'>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-center'>
          <div>
            <h2 className='text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6'>
              {t('mission.title')}
            </h2>
            <p className='text-gray-600 dark:text-gray-400 text-lg leading-relaxed mb-6'>
              {t('mission.description')}
            </p>
            <div className='space-y-4'>
              <div className='flex items-center'>
                <div className='flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mr-3'></div>
                <span className='text-gray-700 dark:text-gray-300'>
                  {t('mission.values.innovation')}
                </span>
              </div>
              <div className='flex items-center'>
                <div className='flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mr-3'></div>
                <span className='text-gray-700 dark:text-gray-300'>
                  {t('mission.values.quality')}
                </span>
              </div>
              <div className='flex items-center'>
                <div className='flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mr-3'></div>
                <span className='text-gray-700 dark:text-gray-300'>
                  {t('mission.values.sustainability')}
                </span>
              </div>
            </div>
          </div>
          <div className='bg-gray-200 dark:bg-gray-700 rounded-lg h-80 flex items-center justify-center'>
            <div className='text-gray-500 dark:text-gray-400 text-center'>
              <svg
                className='w-16 h-16 mx-auto mb-4'
                fill='currentColor'
                viewBox='0 0 20 20'
              >
                <path
                  fillRule='evenodd'
                  d='M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z'
                  clipRule='evenodd'
                />
              </svg>
              <p className='text-sm'>{t('placeholders.image')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className='mb-16'>
        <h2 className='text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-12'>
          {t('team.title')}
        </h2>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
          {[1, 2, 3, 4, 5, 6].map(member => (
            <div key={member} className='text-center'>
              <div className='animate-pulse'>
                {/* Avatar */}
                <div className='w-32 h-32 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto mb-4'></div>

                {/* Name */}
                <div className='bg-gray-300 dark:bg-gray-600 h-6 rounded w-32 mx-auto mb-2'></div>

                {/* Position */}
                <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-24 mx-auto mb-4'></div>

                {/* Bio */}
                <div className='space-y-2'>
                  <div className='bg-gray-300 dark:bg-gray-600 h-3 rounded w-full'></div>
                  <div className='bg-gray-300 dark:bg-gray-600 h-3 rounded w-5/6 mx-auto'></div>
                  <div className='bg-gray-300 dark:bg-gray-600 h-3 rounded w-4/6 mx-auto'></div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className='text-center mt-8'>
          <p className='text-gray-600 dark:text-gray-400'>
            {t('team.comingSoon')}
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className='bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-16'>
        <h2 className='text-3xl font-bold text-gray-900 dark:text-gray-100 text-center mb-12'>
          {t('stats.title')}
        </h2>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-8'>
          <div className='text-center'>
            <div className='text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2'>
              100+
            </div>
            <div className='text-gray-600 dark:text-gray-400 text-sm uppercase tracking-wide'>
              {t('stats.projects')}
            </div>
          </div>
          <div className='text-center'>
            <div className='text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2'>
              50+
            </div>
            <div className='text-gray-600 dark:text-gray-400 text-sm uppercase tracking-wide'>
              {t('stats.clients')}
            </div>
          </div>
          <div className='text-center'>
            <div className='text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2'>
              5+
            </div>
            <div className='text-gray-600 dark:text-gray-400 text-sm uppercase tracking-wide'>
              {t('stats.experience')}
            </div>
          </div>
          <div className='text-center'>
            <div className='text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2'>
              10+
            </div>
            <div className='text-gray-600 dark:text-gray-400 text-sm uppercase tracking-wide'>
              {t('stats.awards')}
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className='text-center'>
        <h2 className='text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6'>
          {t('contact.title')}
        </h2>
        <p className='text-gray-600 dark:text-gray-400 text-lg mb-8 max-w-2xl mx-auto'>
          {t('contact.description')}
        </p>
        <div className='inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors'>
          {t('contact.button')}
        </div>
      </section>
    </div>
  );
}
