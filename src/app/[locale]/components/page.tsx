import { FadeIn } from '@/components/animations/fade-in';
import { PageTransition } from '@/components/animations/page-transition';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { getTranslations } from 'next-intl/server';

interface ComponentsPageProps {
  params: {
    locale: 'en' | 'zh';
  };
}

export default async function ComponentsPage({
  params: { locale },
}: ComponentsPageProps) {
  const t = await getTranslations({ locale, namespace: 'ComponentsPage' });

  return (
    <PageTransition>
      <div className='container mx-auto px-4 py-8'>
        <FadeIn>
          <div className='mb-8'>
            <h1 className='text-4xl font-bold mb-4'>{t('title')}</h1>
            <p className='text-lg text-muted-foreground'>{t('description')}</p>
          </div>
        </FadeIn>

        <div className='grid gap-8'>
          {/* Buttons Section */}
          <FadeIn delay={0.1}>
            <Card>
              <CardHeader>
                <CardTitle>Buttons</CardTitle>
                <CardDescription>
                  Various button styles and sizes
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex gap-2 flex-wrap'>
                  <Button>Default</Button>
                  <Button variant='secondary'>Secondary</Button>
                  <Button variant='outline'>Outline</Button>
                  <Button variant='ghost'>Ghost</Button>
                  <Button variant='link'>Link</Button>
                  <Button variant='destructive'>Destructive</Button>
                </div>
                <div className='flex gap-2 flex-wrap'>
                  <Button size='sm'>Small</Button>
                  <Button size='default'>Default</Button>
                  <Button size='lg'>Large</Button>
                  <Button size='icon'>
                    <span className='i-lucide-heart' />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </FadeIn>

          {/* Cards Section */}
          <FadeIn delay={0.2}>
            <Card>
              <CardHeader>
                <CardTitle>Cards</CardTitle>
                <CardDescription>
                  Flexible card components for various content
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <Card>
                    <CardHeader>
                      <CardTitle>Basic Card</CardTitle>
                      <CardDescription>
                        A simple card with header and content
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p>
                        This is the card content area where you can place any
                        content.
                      </p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Card with Footer</CardTitle>
                      <CardDescription>
                        Card demonstrating footer usage
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p>Content area with footer actions below.</p>
                    </CardContent>
                    <div className='flex items-center p-6 pt-0 justify-between'>
                      <Button variant='outline'>Cancel</Button>
                      <Button>Save</Button>
                    </div>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </FadeIn>

          {/* Badges Section */}
          <FadeIn delay={0.3}>
            <Card>
              <CardHeader>
                <CardTitle>Badges</CardTitle>
                <CardDescription>
                  Compact status indicators and labels
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex gap-2 flex-wrap'>
                  <Badge>Default</Badge>
                  <Badge variant='secondary'>Secondary</Badge>
                  <Badge variant='outline'>Outline</Badge>
                  <Badge variant='destructive'>Destructive</Badge>
                </div>
              </CardContent>
            </Card>
          </FadeIn>

          {/* Form Elements Section */}
          <FadeIn delay={0.4}>
            <Card>
              <CardHeader>
                <CardTitle>Form Elements</CardTitle>
                <CardDescription>
                  Input fields and form controls
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='default-input'>Default Input</Label>
                    <Input
                      id='default-input'
                      placeholder='Enter text here...'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='disabled-input'>Disabled Input</Label>
                    <Input
                      id='disabled-input'
                      placeholder='Disabled input'
                      disabled
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </FadeIn>
        </div>
      </div>
    </PageTransition>
  );
}
