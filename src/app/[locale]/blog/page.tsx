/**
 * Blog Page
 *
 * Displays blog posts with internationalization support.
 * Built for Next.js 14.2.18 App Router with next-intl integration.
 */

import { routing } from '@/i18n/routing';
import { Metadata } from 'next';
import { hasLocale } from 'next-intl';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface BlogPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * Generate metadata for the blog page
 */
export async function generateMetadata({
  params,
}: BlogPageProps): Promise<Metadata> {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const t = await getTranslations({ locale, namespace: 'BlogPage.metadata' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: locale,
      alternateLocale: routing.locales.filter(l => l !== locale),
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
    alternates: {
      canonical: '/blog',
      languages: Object.fromEntries(
        routing.locales.map(loc => [loc, `/${loc}/blog`])
      ),
    },
  };
}

/**
 * Generate static params for all supported locales
 */
export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}

/**
 * Blog Page Component
 */
export default async function BlogPage({ params }: BlogPageProps) {
  const { locale } = await params;

  // Validate locale
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  // Get translations
  const t = await getTranslations('BlogPage');

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Page Header */}
      <header className='mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4'>
          {t('title')}
        </h1>
        <p className='text-lg text-gray-600 dark:text-gray-400 max-w-2xl'>
          {t('description')}
        </p>
      </header>

      {/* Under Construction Notice */}
      <div className='bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-8'>
        <div className='flex items-center'>
          <div className='flex-shrink-0'>
            <svg
              className='h-5 w-5 text-blue-400'
              viewBox='0 0 20 20'
              fill='currentColor'
            >
              <path
                fillRule='evenodd'
                d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-blue-800 dark:text-blue-200'>
              {t('underConstruction.title')}
            </h3>
            <p className='mt-1 text-sm text-blue-700 dark:text-blue-300'>
              {t('underConstruction.message')}
            </p>
          </div>
        </div>
      </div>

      {/* Blog Categories */}
      <div className='mb-8'>
        <h2 className='text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4'>
          {t('categories.title')}
        </h2>
        <div className='flex flex-wrap gap-3'>
          <span className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors cursor-pointer'>
            {t('categories.technology')}
          </span>
          <span className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors cursor-pointer'>
            {t('categories.design')}
          </span>
          <span className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors cursor-pointer'>
            {t('categories.business')}
          </span>
          <span className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors cursor-pointer'>
            {t('categories.tutorials')}
          </span>
          <span className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors cursor-pointer'>
            {t('categories.news')}
          </span>
        </div>
      </div>

      {/* Placeholder Blog Posts */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
        {[1, 2, 3, 4, 5, 6].map(item => (
          <article
            key={item}
            className='bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow'
          >
            <div className='animate-pulse'>
              {/* Featured Image Placeholder */}
              <div className='bg-gray-300 dark:bg-gray-600 h-48 w-full'></div>

              {/* Content */}
              <div className='p-6'>
                {/* Category Badge */}
                <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded-full w-20 mb-3'></div>

                {/* Title */}
                <div className='space-y-2 mb-4'>
                  <div className='bg-gray-300 dark:bg-gray-600 h-6 rounded w-full'></div>
                  <div className='bg-gray-300 dark:bg-gray-600 h-6 rounded w-3/4'></div>
                </div>

                {/* Excerpt */}
                <div className='space-y-2 mb-4'>
                  <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-full'></div>
                  <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-5/6'></div>
                  <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-2/3'></div>
                </div>

                {/* Meta Info */}
                <div className='flex items-center justify-between'>
                  <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-24'></div>
                  <div className='bg-gray-300 dark:bg-gray-600 h-4 rounded w-16'></div>
                </div>
              </div>
            </div>
          </article>
        ))}
      </div>

      {/* Newsletter Signup */}
      <div className='mt-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-8 text-white'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold mb-4'>{t('newsletter.title')}</h2>
          <p className='text-blue-100 mb-6 max-w-2xl mx-auto'>
            {t('newsletter.description')}
          </p>
          <div className='flex flex-col sm:flex-row gap-4 max-w-md mx-auto'>
            <input
              type='email'
              placeholder={t('newsletter.emailPlaceholder')}
              className='flex-1 px-4 py-2 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50'
              disabled
            />
            <button
              className='px-6 py-2 bg-white text-blue-600 rounded-md font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-colors disabled:opacity-50'
              disabled
            >
              {t('newsletter.subscribeButton')}
            </button>
          </div>
          <p className='text-xs text-blue-200 mt-3'>
            {t('newsletter.comingSoon')}
          </p>
        </div>
      </div>
    </div>
  );
}
