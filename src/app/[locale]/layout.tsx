import { routing } from '@/i18n/routing';
import type { Metadata } from 'next';
import { NextIntlClientProvider, hasLocale } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Inter } from 'next/font/google';
import { notFound } from 'next/navigation';
import '../globals.css';

import { PageScrollAnimations } from '@/components/animations/scroll-enhancer';
import { ThemeProvider } from '@/components/theme';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'arial'],
  adjustFontFallback: true,
  variable: '--font-inter',
});

// Type definition for messages structure
interface MessagesType {
  metadata?: {
    defaultTitle?: string;
    defaultDescription?: string;
    keywords?: string;
  };
  [key: string]: unknown;
}

export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  // Get messages for metadata
  const messages = (await getMessages({
    locale: locale as 'en' | 'zh',
  })) as MessagesType;

  return {
    title: messages?.metadata?.defaultTitle || 'Tucsenberg Web Stable',
    description:
      messages?.metadata?.defaultDescription ||
      'Enterprise B2B website template built with stable tech stack',
    keywords:
      messages?.metadata?.keywords ||
      'Next.js, React, TypeScript, Tailwind CSS, Enterprise, B2B, Template',
    authors: [{ name: 'Tucsenberg Web Stable Team' }],
    creator: 'Tucsenberg Web Stable',
    publisher: 'Tucsenberg Web Stable',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: 'website',
      locale: locale,
      alternateLocale: routing.locales.filter((l: string) => l !== locale),
      title: messages?.metadata?.defaultTitle || 'Tucsenberg Web Stable',
      description:
        messages?.metadata?.defaultDescription ||
        'Enterprise B2B website template built with stable tech stack',
      siteName: 'Tucsenberg Web Stable',
    },
    twitter: {
      card: 'summary_large_image',
      title: messages?.metadata?.defaultTitle || 'Tucsenberg Web Stable',
      description:
        messages?.metadata?.defaultDescription ||
        'Enterprise B2B website template built with stable tech stack',
    },
    alternates: {
      canonical: locale === 'en' ? '/' : `/${locale}`,
      languages: {
        en: '/', // Default language without prefix
        zh: '/zh', // Other languages keep prefix
      },
    },
  };
}

export default async function LocaleLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // Validate that the incoming `locale` parameter is valid
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Providing all messages to the client side is the easiest way to get started
  const messages = await getMessages({ locale });

  return (
    <html lang={locale} dir='ltr'>
      <head>
        {/* 性能优化：内联关键 CSS */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            /* Critical CSS for above-the-fold content */
            *,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
            html{line-height:1.5;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif}
            body{margin:0;line-height:inherit}
            .flex{display:flex}.min-h-screen{min-height:100vh}.flex-col{flex-direction:column}
            .items-center{align-items:center}.justify-between{justify-content:space-between}
            .justify-center{justify-content:center}.p-24{padding:6rem}.text-center{text-align:center}
            .text-4xl{font-size:2.25rem;line-height:2.5rem}.font-bold{font-weight:700}
            .mb-4{margin-bottom:1rem}.text-lg{font-size:1.125rem;line-height:1.75rem}
            .text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity))}
            .dark .dark\\:text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity))}
            /* Font optimization */
            @font-face{font-family:'Inter';font-style:normal;font-weight:400;font-display:swap;
            src:url('/_next/static/media/inter-latin.woff2') format('woff2')}
          `,
          }}
        />

        {/* 性能优化：关键 CSS 内联提示 */}
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1, viewport-fit=cover'
        />
        <meta name='color-scheme' content='light dark' />

        {/* 字体由 next/font/google 自动优化处理，无需手动预加载 */}
      </head>
      <body className={inter.className}>
        <ThemeProvider>
          <NextIntlClientProvider messages={messages}>
            <PageScrollAnimations>{children}</PageScrollAnimations>
          </NextIntlClientProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
