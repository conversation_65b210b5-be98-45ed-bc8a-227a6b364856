export default function AnimationsPage() {
  return (
    <div className='min-h-screen'>
      {/* 英雄区域 */}
      <section className='h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800'>
        <div className='text-center scroll-reveal-scale'>
          <h1 className='text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'>
            滚动动画演示
          </h1>
          <p className='text-xl text-gray-600 dark:text-gray-300 mb-8'>
            向下滚动查看各种动画效果
          </p>
          <div className='animate-bounce'>
            <svg
              className='w-6 h-6 mx-auto text-gray-400'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M19 14l-7 7m0 0l-7-7m0 0l7-7m0 0l7 7'
              />
            </svg>
          </div>
        </div>
      </section>

      {/* 基础滚动显示效果 */}
      <section className='py-20 px-8'>
        <div className='max-w-4xl mx-auto'>
          <div className='text-center mb-16 scroll-reveal-up'>
            <h2 className='text-4xl font-bold mb-4'>基础滚动显示效果</h2>
            <p className='text-lg text-gray-600 dark:text-gray-300'>
              这些内容会在您滚动时逐步显示
            </p>
          </div>

          <div className='space-y-16'>
            <div className='bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg scroll-reveal-up'>
              <h3 className='text-2xl font-semibold mb-4'>从下方滑入</h3>
              <p className='text-gray-600 dark:text-gray-300'>
                这个卡片使用了从下方滑入的动画效果。当它进入视口时，会从下方平滑地滑入并淡入显示。
              </p>
            </div>

            <div className='bg-gradient-to-r from-purple-500 to-pink-500 p-8 rounded-lg text-white scroll-reveal-up'>
              <h3 className='text-2xl font-semibold mb-4'>渐变背景卡片</h3>
              <p>这是另一个带有渐变背景的卡片，同样使用了滚动显示动画。</p>
            </div>

            <div className='bg-blue-50 dark:bg-blue-900/20 p-8 rounded-lg scroll-reveal-up'>
              <h3 className='text-2xl font-semibold mb-4'>信息卡片</h3>
              <p className='text-gray-700 dark:text-gray-300'>
                每个卡片都会在进入视口时触发动画，创造流畅的用户体验。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 交错动画效果 */}
      <section className='py-20 px-8 bg-gray-50 dark:bg-gray-900'>
        <div className='max-w-6xl mx-auto'>
          <div className='text-center mb-16 scroll-reveal-up'>
            <h2 className='text-4xl font-bold mb-4'>交错动画效果</h2>
            <p className='text-lg text-gray-600 dark:text-gray-300'>
              多个元素依次出现，创造节奏感
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 scroll-stagger-container'>
            <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md scroll-stagger-item'>
              <div className='w-12 h-12 bg-blue-500 rounded-lg mb-4'></div>
              <h4 className='text-lg font-semibold mb-2'>功能特性 1</h4>
              <p className='text-gray-600 dark:text-gray-300'>
                这些卡片会依次出现，每个都有轻微的延迟。
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md scroll-stagger-item'>
              <div className='w-12 h-12 bg-green-500 rounded-lg mb-4'></div>
              <h4 className='text-lg font-semibold mb-2'>功能特性 2</h4>
              <p className='text-gray-600 dark:text-gray-300'>
                交错动画创造了更有趣的视觉效果。
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md scroll-stagger-item'>
              <div className='w-12 h-12 bg-purple-500 rounded-lg mb-4'></div>
              <h4 className='text-lg font-semibold mb-2'>功能特性 3</h4>
              <p className='text-gray-600 dark:text-gray-300'>
                每个元素都有自己的时机出现。
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md scroll-stagger-item'>
              <div className='w-12 h-12 bg-red-500 rounded-lg mb-4'></div>
              <h4 className='text-lg font-semibold mb-2'>功能特性 4</h4>
              <p className='text-gray-600 dark:text-gray-300'>
                这种效果特别适合展示产品功能。
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md scroll-stagger-item'>
              <div className='w-12 h-12 bg-yellow-500 rounded-lg mb-4'></div>
              <h4 className='text-lg font-semibold mb-2'>功能特性 5</h4>
              <p className='text-gray-600 dark:text-gray-300'>
                或者展示团队成员信息。
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md scroll-stagger-item'>
              <div className='w-12 h-12 bg-indigo-500 rounded-lg mb-4'></div>
              <h4 className='text-lg font-semibold mb-2'>功能特性 6</h4>
              <p className='text-gray-600 dark:text-gray-300'>
                最后一个元素完成整个序列。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 缩放动画效果 */}
      <section className='py-20 px-8'>
        <div className='max-w-4xl mx-auto'>
          <div className='text-center mb-16 scroll-reveal-scale'>
            <h2 className='text-4xl font-bold mb-4'>缩放动画效果</h2>
            <p className='text-lg text-gray-600 dark:text-gray-300'>
              这些元素使用缩放动画进入视图
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
            <div className='text-center scroll-reveal-scale'>
              <div className='w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center'>
                <svg
                  className='w-12 h-12 text-white'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 10V3L4 14h7v7l9-11h-7z'
                  />
                </svg>
              </div>
              <h3 className='text-xl font-semibold mb-2'>快速响应</h3>
              <p className='text-gray-600 dark:text-gray-300'>
                从小到大的缩放动画效果
              </p>
            </div>

            <div className='text-center scroll-reveal-scale'>
              <div className='w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full mx-auto mb-4 flex items-center justify-center'>
                <svg
                  className='w-12 h-12 text-white'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
              </div>
              <h3 className='text-xl font-semibold mb-2'>高质量</h3>
              <p className='text-gray-600 dark:text-gray-300'>
                缩放动画增加视觉冲击力
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 结尾区域 */}
      <section className='py-20 px-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white'>
        <div className='max-w-4xl mx-auto text-center scroll-reveal-up'>
          <h2 className='text-4xl font-bold mb-6'>演示完成</h2>
          <p className='text-xl mb-8'>
            这就是&ldquo;页面下拉、上移过程中逐步浮现内容&rdquo;的滚动动画效果
          </p>
          <div className='scroll-reveal-scale'>
            <button className='bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors'>
              返回首页
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
