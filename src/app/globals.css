@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance optimizations */
@layer base {
  /* Font variables for Inter */
  :root {
    --font-inter: 'Inter', system-ui, -apple-system, BlinkMacSystemFont,
      'Segoe UI', Roboto, sans-serif;
  }

  /* Optimize font loading */
  html {
    font-family: var(--font-inter);
    font-display: swap;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Optimize layout shifts */
  * {
    box-sizing: border-box;
  }

  /* Optimize images */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Focus management for accessibility */
  :focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
}

/* CSS Custom Properties for theming - OKLCH Color Space */
:root {
  /*
   * Modern OKLCH color system with optimized contrast ratios
   * OKLCH format: oklch(lightness% chroma hue)
   * - Lightness: 0-100% (perceptually uniform)
   * - Chroma: 0-0.4+ (color intensity)
   * - Hue: 0-360 degrees (color wheel position)
   *
   * Benefits over HSL:
   * - Perceptually uniform lightness
   * - Better color consistency across hues
   * - More predictable contrast ratios
   * - Future-proof for wide-gamut displays
   */
  --background: oklch(100% 0 0);
  --foreground: oklch(8.5% 0 0);
  --primary: oklch(47.2% 0.13 264);
  --primary-foreground: oklch(98.5% 0 0);
  --secondary: oklch(96.1% 0.01 264);
  --secondary-foreground: oklch(44.8% 0.1 264);
  --muted: oklch(96.1% 0.01 264);
  --muted-foreground: oklch(44.5% 0.03 264);
  --accent: oklch(96.1% 0.01 264);
  --accent-foreground: oklch(44.8% 0.1 264);
  --destructive: oklch(64.8% 0.15 28);
  --destructive-foreground: oklch(98.5% 0 0);
  --border: oklch(89.2% 0.005 264);
  --input: oklch(89.2% 0.005 264);
  --ring: oklch(47.2% 0.13 264);
  --radius: 0.5rem;

  /* Legacy RGB variables for backward compatibility */
  --foreground-rgb: 23, 23, 23;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Performance variables */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark theme - OKLCH Color Space */
@media (prefers-color-scheme: dark) {
  :root {
    --background: oklch(3.8% 0 0);
    --foreground: oklch(98.2% 0 0);
    --primary: oklch(83.2% 0.12 264);
    --primary-foreground: oklch(8.5% 0 0);
    --secondary: oklch(14.8% 0.02 264);
    --secondary-foreground: oklch(98.2% 0 0);
    --muted: oklch(14.8% 0.02 264);
    --muted-foreground: oklch(65.2% 0.03 264);
    --accent: oklch(14.8% 0.02 264);
    --accent-foreground: oklch(98.2% 0 0);
    --destructive: oklch(64.8% 0.15 28);
    --destructive-foreground: oklch(98.2% 0 0);
    --border: oklch(19.8% 0.01 264);
    --input: oklch(19.8% 0.01 264);
    --ring: oklch(83.2% 0.12 264);

    /* Legacy RGB variables for backward compatibility */
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Theme attribute selector for next-themes compatibility */
[data-theme='dark'] {
  --background: oklch(4% 0 0);
  --foreground: oklch(98% 0 0);
  --primary: oklch(83% 0.12 264);
  --primary-foreground: oklch(9% 0 0);
  --secondary: oklch(15% 0.02 264);
  --secondary-foreground: oklch(98% 0 0);
  --muted: oklch(15% 0.02 264);
  --muted-foreground: oklch(65% 0.03 264);
  --accent: oklch(15% 0.02 264);
  --accent-foreground: oklch(98% 0 0);
  --destructive: oklch(65% 0.15 28);
  --destructive-foreground: oklch(98% 0 0);
  --border: oklch(20% 0.01 264);
  --input: oklch(20% 0.01 264);
  --ring: oklch(83% 0.12 264);

  /* Legacy RGB variables for backward compatibility */
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}

/* Base styles with progressive enhancement for OKLCH */
body {
  /* Fallback for browsers without OKLCH support */
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Progressive enhancement: Use OKLCH when supported */
@supports (color: oklch(0% 0 0)) {
  body {
    color: hsl(var(--foreground));
    background-color: hsl(var(--background));
  }

  /* Ensure all OKLCH colors are properly applied */
  * {
    border-color: hsl(var(--border));
  }
}

/* Performance optimizations for all elements */
* {
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

/* Theme transition animations */
.theme-transitioning,
.theme-transitioning * {
  transition:
    background-color 150ms ease-in-out,
    border-color 150ms ease-in-out,
    color 150ms ease-in-out !important;
}

/* Disable transitions for specific elements during theme change */
.theme-transitioning .no-transition,
.theme-transitioning .no-transition * {
  transition: none !important;
}

/* Smooth theme transitions for common elements */
@media (prefers-reduced-motion: no-preference) {
  :root {
    --transition-theme: background-color 150ms ease-in-out,
      border-color 150ms ease-in-out, color 150ms ease-in-out;
  }

  body,
  [data-theme] {
    transition: var(--transition-theme);
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .transition-fast {
    transition: all var(--transition-fast);
  }

  .transition-normal {
    transition: all var(--transition-normal);
  }

  .transition-slow {
    transition: all var(--transition-slow);
  }

  /* Performance utilities */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .contain-layout {
    contain: layout;
  }

  .contain-paint {
    contain: paint;
  }

  /* Accessibility utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
  }
}

/* Component styles */
@layer components {
  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-muted;
  }

  /* Interactive elements */
  .interactive {
    @apply transition-fast hover:scale-105 active:scale-95;
  }

  /* Container queries support */
  .container-queries {
    container-type: inline-size;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  img {
    max-width: 100% !important;
  }

  @page {
    margin: 0.5cm;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
