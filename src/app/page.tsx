'use client';

import { redirect } from 'next/navigation';

/**
 * Root Path Redirect Page
 *
 * Automatically redirects users from root path (/) to default locale (/en)
 * This ensures users always see content instead of 404 errors.
 *
 * Technical Details:
 * - Uses client-side redirect for immediate response
 * - Redirects to /en (default locale as configured in routing)
 * - Works in conjunction with middleware for complete root path handling
 * - Follows Next.js 14 App Router best practices
 */
export default function RootPage() {
  redirect('/en');
}
