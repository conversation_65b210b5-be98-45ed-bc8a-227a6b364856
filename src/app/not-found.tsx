'use client';

import Error from 'next/error';

/**
 * Root Not Found Page
 *
 * Handles 404 errors for non-localized requests at the root level.
 * Provides a complete HTML structure for error display when users
 * access paths that don't exist outside of the localized routing.
 *
 * Technical Details:
 * - Client component to support Next.js Error component
 * - Provides complete html/body structure (required for root-level errors)
 * - Uses Next.js built-in Error component for consistent UX
 * - Default language set to English for non-localized errors
 * - Separate from localized error handling in [locale] routes
 */
export default function NotFound() {
  return (
    <html lang='en'>
      <body>
        <Error statusCode={404} />
      </body>
    </html>
  );
}
