/**
 * UI Components Showcase
 *
 * Comprehensive demonstration of all core UI components
 * with all variants, sizes, and states for visual verification
 */

import { Button } from '@/components/ui/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function UIShowcasePage() {
  return (
    <div className='container mx-auto p-8 space-y-12'>
      <div className='text-center space-y-4'>
        <h1 className='text-4xl font-bold'>UI Components Showcase</h1>
        <p className='text-muted-foreground'>
          Comprehensive demonstration of shadcn/ui components with New York
          style
        </p>
      </div>

      {/* Button Showcase */}
      <section className='space-y-6'>
        <h2 className='text-2xl font-semibold'>Button Component</h2>

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Variants</h3>
          <div className='flex flex-wrap gap-4'>
            <Button variant='default'>Default</Button>
            <Button variant='destructive'>Destructive</Button>
            <Button variant='outline'>Outline</Button>
            <Button variant='secondary'>Secondary</Button>
            <Button variant='ghost'>Ghost</Button>
            <Button variant='link'>Link</Button>
          </div>
        </div>

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Sizes</h3>
          <div className='flex flex-wrap items-center gap-4'>
            <Button size='sm'>Small</Button>
            <Button size='default'>Default</Button>
            <Button size='lg'>Large</Button>
            <Button size='icon'>🎯</Button>
          </div>
        </div>

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>States</h3>
          <div className='flex flex-wrap gap-4'>
            <Button>Normal</Button>
            <Button disabled>Disabled</Button>
          </div>
        </div>
      </section>

      {/* Card Showcase */}
      <section className='space-y-6'>
        <h2 className='text-2xl font-semibold'>Card Component</h2>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          <Card>
            <CardHeader>
              <CardTitle>Basic Card</CardTitle>
              <CardDescription>
                A simple card with header and content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>
                This is the card content area where you can place any content.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Card with Footer</CardTitle>
              <CardDescription>Card demonstrating footer usage</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Content area with footer actions below.</p>
            </CardContent>
            <CardFooter className='justify-between'>
              <Button variant='outline'>Cancel</Button>
              <Button>Save</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Interactive Card</CardTitle>
              <CardDescription>Card with form elements</CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Name</Label>
                <Input id='name' placeholder='Enter your name' />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='email'>Email</Label>
                <Input id='email' type='email' placeholder='Enter your email' />
              </div>
            </CardContent>
            <CardFooter>
              <Button className='w-full'>Submit</Button>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Badge Showcase */}
      <section className='space-y-6'>
        <h2 className='text-2xl font-semibold'>Badge Component</h2>

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Variants</h3>
          <div className='flex flex-wrap gap-4'>
            <Badge variant='default'>Default</Badge>
            <Badge variant='secondary'>Secondary</Badge>
            <Badge variant='destructive'>Destructive</Badge>
            <Badge variant='outline'>Outline</Badge>
          </div>
        </div>

        <div className='space-y-4'>
          <h3 className='text-lg font-medium'>Use Cases</h3>
          <div className='flex flex-wrap gap-4'>
            <Badge>New</Badge>
            <Badge variant='secondary'>Beta</Badge>
            <Badge variant='destructive'>Deprecated</Badge>
            <Badge variant='outline'>Coming Soon</Badge>
          </div>
        </div>
      </section>

      {/* Input & Label Showcase */}
      <section className='space-y-6'>
        <h2 className='text-2xl font-semibold'>Input & Label Components</h2>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <Card>
            <CardHeader>
              <CardTitle>Form Examples</CardTitle>
              <CardDescription>Various input types with labels</CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='text-input'>Text Input</Label>
                <Input id='text-input' type='text' placeholder='Enter text' />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='email-input'>Email Input</Label>
                <Input
                  id='email-input'
                  type='email'
                  placeholder='Enter email'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='password-input'>Password Input</Label>
                <Input
                  id='password-input'
                  type='password'
                  placeholder='Enter password'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='number-input'>Number Input</Label>
                <Input
                  id='number-input'
                  type='number'
                  placeholder='Enter number'
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Input States</CardTitle>
              <CardDescription>
                Different input states and validation
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='normal-input'>Normal State</Label>
                <Input id='normal-input' placeholder='Normal input' />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='disabled-input'>Disabled State</Label>
                <Input
                  id='disabled-input'
                  placeholder='Disabled input'
                  disabled
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='readonly-input'>Readonly State</Label>
                <Input id='readonly-input' value='Readonly value' readOnly />
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Accessibility Demo */}
      <section className='space-y-6'>
        <h2 className='text-2xl font-semibold'>Accessibility Features</h2>

        <Card>
          <CardHeader>
            <CardTitle>Keyboard Navigation Test</CardTitle>
            <CardDescription>
              Use Tab, Enter, Space, and Arrow keys to navigate and interact
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='flex flex-wrap gap-4'>
              <Button>Tab Stop 1</Button>
              <Button variant='outline'>Tab Stop 2</Button>
              <Button variant='secondary'>Tab Stop 3</Button>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='accessible-input'>
                Accessible Input (try Tab + typing)
              </Label>
              <Input
                id='accessible-input'
                placeholder='Focus with Tab, type to test'
                aria-describedby='input-help'
              />
              <p id='input-help' className='text-sm text-muted-foreground'>
                This input has proper ARIA attributes for screen readers
              </p>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
