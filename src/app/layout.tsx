/**
 * Root Layout
 *
 * Minimal root layout required by Next.js when root not-found.tsx exists.
 * Simply passes through children without additional logic.
 *
 * Technical Details:
 * - Server component (no 'use client' directive)
 * - Does not include html/body tags to avoid conflicts with [locale] layout
 * - Required by Next.js 14 App Router when root-level error pages exist
 * - Maintains separation between root-level and localized routing
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
