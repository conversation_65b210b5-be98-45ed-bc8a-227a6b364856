/**
 * Health Check API Route
 *
 * Provides system health status, configuration validation,
 * and basic diagnostics for monitoring and debugging.
 */

import { config } from '@/lib/config';
import { getConfigurationHealth } from '@/lib/config-validator';
import { NextResponse } from 'next/server';

export interface HealthCheckResponse {
  status: 'healthy' | 'warning' | 'error';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  checks: {
    configuration: {
      status: 'healthy' | 'warning' | 'error';
      message: string;
    };
    database: {
      status: 'healthy' | 'warning' | 'error';
      message: string;
    };
    memory: {
      status: 'healthy' | 'warning' | 'error';
      usage: number;
      limit: number;
    };
  };
}

/**
 * GET /api/health
 * Returns comprehensive health status
 */
export async function GET(): Promise<NextResponse<HealthCheckResponse>> {
  try {
    const startTime = Date.now();

    // Configuration health check
    const configHealth = getConfigurationHealth();

    // Memory usage check
    const memoryUsage = process.memoryUsage();
    const memoryLimit = 512 * 1024 * 1024; // 512MB default limit
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryLimit) * 100;

    // Database health check (placeholder for now)
    const databaseHealth: {
      status: 'healthy' | 'warning' | 'error';
      message: string;
    } = {
      status: 'healthy',
      message: 'Database connection not implemented yet',
    };

    // Memory health status
    const memoryHealth = {
      status:
        memoryUsagePercent > 90
          ? ('error' as const)
          : memoryUsagePercent > 70
            ? ('warning' as const)
            : ('healthy' as const),
      usage: memoryUsage.heapUsed,
      limit: memoryLimit,
    };

    // Overall health status
    const overallStatus =
      configHealth.status === 'error' ||
      memoryHealth.status === 'error' ||
      databaseHealth.status === 'error'
        ? ('error' as const)
        : configHealth.status === 'warning' ||
            memoryHealth.status === 'warning' ||
            databaseHealth.status === 'warning'
          ? ('warning' as const)
          : ('healthy' as const);

    const response: HealthCheckResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: config.app.version,
      environment: config.app.environment,
      uptime: process.uptime(),
      checks: {
        configuration: {
          status: configHealth.status,
          message: configHealth.message,
        },
        database: databaseHealth,
        memory: memoryHealth,
      },
    };

    const responseTime = Date.now() - startTime;

    return NextResponse.json(response, {
      status: overallStatus === 'error' ? 503 : 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Response-Time': `${responseTime}ms`,
        'X-Health-Status': overallStatus,
      },
    });
  } catch (error) {
    console.error('Health check failed:', error);

    const errorResponse: HealthCheckResponse = {
      status: 'error',
      timestamp: new Date().toISOString(),
      version: config.app.version,
      environment: config.app.environment,
      uptime: process.uptime(),
      checks: {
        configuration: {
          status: 'error',
          message:
            error instanceof Error
              ? error.message
              : 'Unknown configuration error',
        },
        database: {
          status: 'error',
          message: 'Health check failed',
        },
        memory: {
          status: 'error',
          usage: 0,
          limit: 0,
        },
      },
    };

    return NextResponse.json(errorResponse, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Health-Status': 'error',
      },
    });
  }
}

/**
 * HEAD /api/health
 * Returns only status code for simple health checks
 */
export async function HEAD(): Promise<NextResponse> {
  try {
    const configHealth = getConfigurationHealth();
    const status = configHealth.status === 'error' ? 503 : 200;

    return new NextResponse(null, {
      status,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Health-Status': configHealth.status,
      },
    });
  } catch (error) {
    return new NextResponse(null, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Health-Status': 'error',
      },
    });
  }
}
